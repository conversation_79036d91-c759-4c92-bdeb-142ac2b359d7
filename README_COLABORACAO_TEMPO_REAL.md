# Funcionalidade de Colaboração em Tempo Real

Este documento descreve a implementação da funcionalidade de colaboração em tempo real no sistema, inspirada pelo Figma, Miro e outras ferramentas de colaboração online.

## Funcionalidades Implementadas

1. **Presença em Tempo Real**: Visualização de cursores de outros usuários ativos na mesma página
2. **Notas Adesivas (Sticky Notes)**: Criação e edição de notas que podem ser posicionadas livremente na página
3. **Sincronização em Tempo Real**: Mudanças são sincronizadas automaticamente entre os usuários

## Tecnologias Utilizadas

- **Supabase Realtime**: Para sincronização em tempo real usando a API de Presence e Postgres Changes
- **React**: Para a interface do usuário e gerenciamento de estado

## Arquivos Criados/Modificados

1. **Tipos e Definições**:

   - `src/types/index.ts`: Adicionados tipos `UserPresence` e `StickyNote`
   - `src/integrations/supabase/types.ts`: Adicionado o tipo da tabela `sticky_notes`

2. **Contexto de Tempo Real**:

   - `src/contexts/RealtimeContext.tsx`: Gerencia a conexão realtime, usuários ativos e notas adesivas

3. **Componentes**:

   - `src/components/Shared/UserCursor.tsx`: Componente para exibir o cursor de outros usuários
   - `src/components/Shared/StickyNoteComponent.tsx`: Componente para exibir e interagir com as notas adesivas
   - `src/components/Shared/CollaborationOverlay.tsx`: Overlay para gerenciar cursores e notas
   - `src/components/Editor/StickyNoteBlock.tsx`: Bloco de conteúdo para criar notas adesivas

4. **Integração**:

   - `src/App.tsx`: Adicionado o `RealtimeProvider`
   - `src/components/Editor/PageEditor.tsx`: Integrado o `CollaborationOverlay`
   - `src/components/Editor/ContentBlock.tsx`: Adicionado suporte ao tipo `sticky_note`

5. **Banco de Dados**:
   - `supabase/migrations/20240628000000_add_sticky_notes.sql`: Script para criar a tabela e políticas

## Configuração do Banco de Dados

O script de migração `20240628000000_add_sticky_notes.sql` cria:

1. Tabela `sticky_notes` com os campos:

   - `id`: UUID
   - `content`: TEXT
   - `position`: JSONB (coordenadas x, y)
   - `color`: TEXT
   - `created_by`: UUID (referência ao usuário)
   - `created_at`: TIMESTAMP
   - `updated_at`: TIMESTAMP
   - `page_id`: UUID (referência à página)
   - `workspace_id`: UUID (referência ao workspace)

2. Políticas de segurança:

   - Leitura: Todos os membros do workspace
   - Criação: Todos os membros do workspace
   - Atualização: Proprietário da nota ou admin/owner do workspace
   - Exclusão: Proprietário da nota ou admin/owner do workspace

3. Configuração de realtime:
   - Habilitado para a tabela `sticky_notes`

## Passos para Finalizar a Implementação

1. **Aplicar a Migração do Banco de Dados**:

   ```bash
   # Se usando Supabase local
   supabase db push

   # Se usando Supabase hospedado, execute o conteúdo do arquivo SQL no editor SQL
   ```

2. **Corrigir Problemas de Tipos**:

   - Certifique-se de que o tipo `BlockType` em `src/types/index.ts` inclui `"sticky_note"` como opção válida

3. **Implementar a Interface da ImageUpload**:

   - Atualizar ou criar o componente `ImageUpload` para aceitar a prop `onUpload`

4. **Verificar a Interface da ContentSearch**:
   - Garantir que a `ContentSearch` aceite a prop `onClose`

## Como Usar

1. **Visualizar Cursores de Outros Usuários**:

   - Os cursores de outros usuários são exibidos automaticamente quando estão ativos na mesma página
   - O nome do usuário é exibido junto com o cursor

2. **Criar Notas Adesivas**:

   - Clique no botão "+" no canto inferior direito da tela
   - Selecione uma cor para a nota
   - Clique em "Adicionar nota" para criar

3. **Editar Notas Adesivas**:

   - Clique no conteúdo da nota para editar (se você for o criador)
   - Arraste a nota para movê-la para outra posição

4. **Excluir Notas Adesivas**:

   - Clique no ícone de lixeira na nota para excluí-la (se você for o criador)

5. **Mostrar/Esconder Cursores**:
   - Use o botão de olho no canto inferior direito para mostrar/esconder os cursores dos outros usuários

## Como Funciona

1. **Conexão Realtime**:

   - Cada usuário se conecta a um canal específico para a página atual
   - Ao entrar na página, o usuário compartilha sua presença com informações básicas

2. **Sincronização de Cursores**:

   - A posição do cursor é atualizada em tempo real e enviada para o canal
   - Os cursores de outros usuários são renderizados usando os dados de presença

3. **Sincronização de Notas**:
   - As alterações nas notas (criação, edição, movimento, exclusão) são sincronizadas pelo Supabase
   - As atualizações do banco são automaticamente refletidas na interface

## Resolução de Problemas

- **Cursores não aparecem**: Verifique se o Supabase Realtime está configurado corretamente
- **Notas não sincronizam**: Verifique se a tabela `sticky_notes` foi criada e as políticas aplicadas
- **Problemas de tipos**: Verifique se todos os tipos (UserPresence, StickyNote, BlockType) estão corretamente definidos

## Referências

- [Repositório de referência](https://github.com/keyurparalkar/realtime-whiteboard-app)
- [Tutorial no Dev.to](https://dev.to/keyurparalkar/mastering-real-time-collaboration-building-figma-and-miro-inspired-features-with-supabase-57eh)
- [Documentação do Supabase Realtime](https://supabase.com/docs/guides/realtime)
