[ ] Adicionar opção de controle da largura do Sidebar dinâmica salva no local storage
[ ] Adicionar um novo componente de Editor HTML como componente para o editor de páginas
[ ] Adicionar opção para mover páginas e conteúdos de um workspace para outro
[ ] Quando o usuário ler a notificação de ajustes de versão, marcar como lido no local storage, e não exibir mais como nova notificação (Dee sempre comparar a versao atual e só exibir quando tiver uma nova versão)
[ ] No Kanban adicionar campo para pesquisa de tarefas (Independente do mês ou ano)
[x] Nas tasks do Kanban adicionar o campo prioridade que pode ser (alta, baixa, média) - CONCLUÍDO
[x] No Kanban adicionar uma coluna backlog para tasks sem data, sem hora e sem atribuição essa coluna é sempre exibida e pega todas as taregas que tiverem com o status backlog independente do mês ou ano da tarefa - CONCLUÍDO
[x] Adicionar o nome do criador da tarefa automaticamente (não editável) - CONCLUÍDO
