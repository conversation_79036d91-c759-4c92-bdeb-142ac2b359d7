# Status de Desenvolvimento das Tarefas

Este arquivo controla o status e contexto das tarefas de desenvolvimento do projeto.

## 📋 Legenda de Status
- ⏳ **Pendente**: Tarefa não iniciada
- 🔄 **Em Progresso**: Tarefa sendo desenvolvida
- ✅ **Concluído**: Tarefa finalizada
- 🔍 **Em Análise**: Tarefa sendo analisada/planejada
- ❌ **Cancelado**: Tarefa cancelada

## 🎯 Tarefas Identificadas

### 1. Controle Dinâmico da Largura do Sidebar
- **Status**: ⏳ Pendente
- **Prioridade**: Média
- **Contexto**: Implementar funcionalidade para ajustar largura do sidebar dinamicamente e salvar no localStorage
- **Arquivos Envolvidos**: `src/components/Layout/Sidebar.tsx`, `src/hooks/useUserPreferences.ts`
- **Dependências**: Nenhuma

### 2. Componente Editor HTML
- **Status**: ⏳ Pendente  
- **Prioridade**: Média
- **Contexto**: Adicionar novo componente de Editor HTML como bloco para o PageEditor
- **Arquivos Envolvidos**: `src/components/Editor/`, novo componente `HtmlEditorBlock.tsx`
- **Dependências**: Possível integração com biblioteca de editor HTML

### 3. Mover Páginas Entre Workspaces
- **Status**: ⏳ Pendente
- **Prioridade**: Média  
- **Contexto**: Funcionalidade para transferir páginas e conteúdos entre workspaces diferentes
- **Arquivos Envolvidos**: `src/contexts/WorkspaceContext.tsx`, `src/hooks/usePageOperations.ts`
- **Dependências**: Validações de permissões entre workspaces

### 4. Sistema de Notificações de Versão
- **Status**: ⏳ Pendente
- **Prioridade**: Baixa
- **Contexto**: Marcar notificações de versão como lidas no localStorage e comparar versões
- **Arquivos Envolvidos**: `src/components/Layout/ReleaseNotesModal.tsx`, `src/data/release-notes.json`
- **Dependências**: Sistema de versionamento

### 5. Campo de Pesquisa no Kanban
- **Status**: ⏳ Pendente
- **Prioridade**: Média
- **Contexto**: Adicionar funcionalidade de busca global de tarefas independente de mês/ano
- **Arquivos Envolvidos**: `src/components/Kanban/KanbanBoard.tsx`, `src/hooks/useTaskFilters.ts`
- **Dependências**: Melhorias na query de tarefas

### 6. Campo Prioridade nas Tasks
- **Status**: ⏳ Pendente
- **Prioridade**: Alta
- **Contexto**: Adicionar campo prioridade (alta, baixa, média) nas tarefas do Kanban
- **Arquivos Envolvidos**: `src/types/taskTypes.ts`, `src/components/Kanban/TaskCard.tsx`, `src/components/Kanban/TaskDialog.tsx`
- **Dependências**: Alteração na estrutura do banco de dados (coluna priority)
- **Queries Necessárias**: 
  ```sql
  ALTER TABLE tasks ADD COLUMN priority VARCHAR(10) DEFAULT 'baixa' CHECK (priority IN ('alta', 'baixa', 'média'));
  ```

### 7. Coluna Backlog no Kanban
- **Status**: ⏳ Pendente
- **Prioridade**: Alta
- **Contexto**: Coluna para tarefas sem data/hora/atribuição, sempre visível, status 'backlog'
- **Arquivos Envolvidos**: `src/components/Kanban/KanbanBoard.tsx`, `src/hooks/useTaskOperations.ts`
- **Dependências**: Alteração na estrutura do banco (status backlog)
- **Queries Necessárias**:
  ```sql
  -- Verificar se status backlog já existe, senão adicionar
  ALTER TABLE tasks ALTER COLUMN status TYPE VARCHAR(20);
  -- Adicionar constraint se necessário
  ```

### 8. Nome do Criador da Tarefa
- **Status**: ⏳ Pendente
- **Prioridade**: Média
- **Contexto**: Adicionar automaticamente o nome do criador da tarefa (não editável)
- **Arquivos Envolvidos**: `src/components/Kanban/TaskDialog.tsx`, `src/hooks/useTaskOperations.ts`
- **Dependências**: Relação com tabela de usuários
- **Queries Necessárias**:
  ```sql
  ALTER TABLE tasks ADD COLUMN created_by_name VARCHAR(255);
  ALTER TABLE tasks ADD COLUMN created_by_id UUID REFERENCES auth.users(id);
  ```

## 🔄 Histórico de Alterações
- **2025-01-06**: Arquivo criado com status inicial das 8 tarefas identificadas