
CREATE OR REPLACE FUNCTION public.check_free_tier_limits(user_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  is_subscribed BOOLEAN;
  is_user_admin BOOLEAN;
  user_trial_expired BOOLEAN;
  workspace_count INTEGER;
  invites_count INTEGER;
BEGIN
  -- Check if user is subscribed or is admin
  SELECT 
    subscribed, 
    is_admin, 
    (trial_end_date IS NOT NULL AND trial_end_date < now()) 
  INTO 
    is_subscribed, 
    is_user_admin, 
    user_trial_expired
  FROM 
    public.subscribers 
  WHERE 
    user_id = user_id_param;
  
  -- Admin users (including your email) have no restrictions
  IF is_user_admin OR EXISTS (
    SELECT 1 FROM auth.users WHERE id = user_id_param AND email = '<EMAIL>'
  ) THEN
    RETURN false;
  END IF;
  
  -- Subscribed users have no restrictions
  IF is_subscribed THEN
    RETURN false;
  END IF;
  
  -- Count user's workspaces where they are owners or members
  SELECT COUNT(*) INTO workspace_count
  FROM public.workspaces w
  JOIN public.workspace_members wm ON w.id = wm.workspace_id
  WHERE wm.user_id = user_id_param;
  
  -- Count active invitations sent by the user
  SELECT COUNT(*) INTO invites_count
  FROM public.workspace_invitations
  WHERE invited_by = user_id_param
  AND NOT accepted
  AND expires_at > now();
  
  -- Free tier users can only have 1 workspace and cannot invite users
  RETURN (workspace_count > 1) OR (invites_count > 0);
END;
$function$;
