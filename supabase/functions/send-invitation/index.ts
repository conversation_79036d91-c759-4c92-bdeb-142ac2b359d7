import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

interface InvitationEmailRequest {
  to: string;
  workspaceName: string;
  inviterName: string;
  inviteUrl: string;
  inviterId: string;
}

interface EmailResponse {
  success: boolean;
  message: string;
  emailSent: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const requestData = await req.json();
    console.log("Received invitation request:", requestData);

    const { to, workspaceName, inviterName, inviteUrl, inviterId } = requestData as InvitationEmailRequest;

    // Validate request data
    if (!to) {
      throw new Error("Missing 'to' email in request");
    }

    if (!workspaceName) {
      throw new Error("Missing workspace name in request");
    }

    if (!inviterName) {
      throw new Error("Missing inviter name in request");
    }

    if (!inviteUrl) {
      throw new Error("Missing invite URL in request");
    }

    if (inviterId) {
      // Verificar se o perfil do convidador existe
      const supabaseAdmin = createClient(Deno.env.get("SUPABASE_URL") ?? "", Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "", {
        auth: { persistSession: false },
      });

      const { data: profile } = await supabaseAdmin.from("profiles").select("id").eq("id", inviterId).single();

      // Se o perfil não existir, criar um
      if (!profile) {
        const { data: userData } = await supabaseAdmin.auth.admin.getUserById(inviterId);

        if (userData?.user) {
          await supabaseAdmin.from("profiles").insert({
            id: inviterId,
            email: userData.user.email,
            name: userData.user.user_metadata?.name || userData.user.email?.split("@")[0] || "Usuário",
            created_at: new Date(),
          });
        }
      }
    }

    // Here you can integrate with an email service like Resend, SendGrid, etc.
    console.log(`Sending invitation email to: ${to}`);
    console.log(`Workspace: ${workspaceName}`);
    console.log(`Invited by: ${inviterName}`);
    console.log(`Invite URL: ${inviteUrl}`);

    // Successfully processed - in a real app this would return after actually sending the email
    const response: EmailResponse = {
      success: true,
      message: "Invitation email processed successfully",
      emailSent: true,
    };

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Error in send-invitation function:", error);

    const errorResponse: EmailResponse = {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error",
      emailSent: false,
    };

    return new Response(JSON.stringify(errorResponse), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
