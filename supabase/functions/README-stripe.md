# Configuração das Chaves do Stripe para o E-manager

Este documento contém instruções sobre como configurar as chaves de API do Stripe nas funções Edge do Supabase.

## Chaves de API do Stripe

Existem duas chaves principais a serem configuradas:

### 1. Chave Secreta

**Produção:** `***********************************************************************************************************`

**Teste (desenvolvimento):** `sk_test_xxxxx` (mantenha essa chave para testes futuros)

### 2. Chave Pública

**Produção:** `pk_live_51PkVaQIxqQbmbZWPJiE0vtb8MTOld4WRvyN8zmuXtNoWRSCYO6sfGcCjijeeBLrgwEkwKk4s2JQBCor1alUo17Ly002XmwsQA7`

**Teste (desenvolvimento):** `pk_test_51PkVaQIxqQbmbZWPatjl4a492UAEDJRcaKZCA6qchePR3YAHn7wD7sQlKwLcK9PYXswWuM0afvl8Kc4JP7V7hTO600xQkDjclU`

## Como Configurar

Para configurar as variáveis de ambiente nas funções Edge do Supabase:

1. Acesse o painel do Supabase (dashboard)
2. Navegue até o projeto do E-manager
3. Vá para "Functions" > "Edge Functions"
4. Selecione "Variables" no menu lateral
5. Configure as seguintes variáveis:

```
STRIPE_SECRET_KEY=***********************************************************************************************************
```

6. Salve as alterações

## Alternando Entre Ambientes

Para alternar entre os ambientes de produção e desenvolvimento, atualize a variável `STRIPE_SECRET_KEY` com a chave apropriada:

Para produção:

```
STRIPE_SECRET_KEY=***********************************************************************************************************
```

Para desenvolvimento:

```
STRIPE_SECRET_KEY=sk_test_xxxxx
```

**IMPORTANTE:** As chaves de API do Stripe são sensíveis e devem ser mantidas em segurança. Não compartilhe essas chaves em repositórios públicos ou com pessoas não autorizadas.
