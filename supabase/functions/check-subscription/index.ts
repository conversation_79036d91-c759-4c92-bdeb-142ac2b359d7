import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import <PERSON><PERSON> from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create Supabase client with service role key for admin operations
    const supabaseAdmin = createClient(Deno.env.get("SUPABASE_URL") ?? "", Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "", {
      auth: { persistSession: false },
    });

    // Get user from auth header
    const authHeader = req.headers.get("Authorization")!;
    const token = authHeader.replace("Bearer ", "");
    const {
      data: { user },
      error: userError,
    } = await supabaseAdmin.auth.getUser(token);

    if (userError || !user?.email) {
      throw new Error("User not authenticated");
    }

    // Verify if profile exists for user and create if it doesn't
    const { data: profile } = await supabaseAdmin.from("profiles").select("id").eq("id", user.id).single();

    // If profile doesn't exist, create it
    if (!profile) {
      await supabaseAdmin.from("profiles").insert({
        id: user.id,
        email: user.email,
        name: user.user_metadata?.name || user.email.split("@")[0],
        created_at: new Date(),
      });
    }

    // Get subscriber info
    const { data: subscriber } = await supabaseAdmin.from("subscribers").select("*").eq("user_id", user.id).single();

    // Special handling for admin users (including your email)
    if (subscriber?.is_admin || user.email === "<EMAIL>") {
      return new Response(
        JSON.stringify({
          isSubscribed: true,
          isAdmin: true,
          trialEndDate: null,
          hasReachedLimit: false,
          subscriptionTier: "business", // Admin tem acesso ao plano mais completo
          subscriptionEnd: null,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Check trial status - only show as ended if more than 7 days have passed
    const trialHasEnded = subscriber?.trial_end_date && new Date(subscriber.trial_end_date) < new Date();

    // Check subscription in Stripe if user has a customer ID
    let isSubscribed = false;
    let subscriptionTier = null;
    let subscriptionEnd = null;

    if (subscriber?.stripe_customer_id) {
      const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
        apiVersion: "2023-10-16",
      });

      const subscriptions = await stripe.subscriptions.list({
        customer: subscriber.stripe_customer_id,
        status: "active",
      });

      isSubscribed = subscriptions.data.length > 0;

      // Se tiver uma assinatura ativa, pegar os detalhes
      if (isSubscribed && subscriptions.data.length > 0) {
        const subscription = subscriptions.data[0];

        subscriptionEnd = new Date(subscription.current_period_end * 1000);

        // Buscar detalhes do produto para identificar o plano
        if (subscription.items.data.length > 0) {
          const productId = subscription.items.data[0].price.product;
          const product = await stripe.products.retrieve(productId as string);

          // Definir o tier com base no nome do produto
          if (product.name.toLowerCase().includes("individual")) {
            subscriptionTier = "individual";
          } else if (product.name.toLowerCase().includes("compartilhado")) {
            subscriptionTier = "shared";
          } else if (product.name.toLowerCase().includes("empresarial")) {
            subscriptionTier = "business";
          }
        }
      }
    }

    // Check if user has reached free tier limits
    const { data: limitCheck } = await supabaseAdmin.rpc("check_free_tier_limits", { user_id_param: user.id });

    const hasReachedLimit = (limitCheck && trialHasEnded) || (trialHasEnded && !isSubscribed);

    // Update subscriber status
    await supabaseAdmin
      .from("subscribers")
      .update({
        subscribed: isSubscribed,
        subscription_tier: subscriptionTier,
        subscription_end: subscriptionEnd?.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq("user_id", user.id);

    return new Response(
      JSON.stringify({
        isSubscribed,
        isAdmin: false,
        trialEndDate: subscriber?.trial_end_date,
        hasReachedLimit,
        subscriptionTier,
        subscriptionEnd: subscriptionEnd?.toISOString(),
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Subscription check error:", error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
