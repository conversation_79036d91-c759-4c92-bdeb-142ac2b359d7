import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Recupera o corpo da requisição
    const payload = await req.text();
    const signature = req.headers.get("stripe-signature") || "";

    // Inicializa o Stripe
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    // Verifica a assinatura do webhook
    let event;
    try {
      event = stripe.webhooks.constructEvent(payload, signature, Deno.env.get("STRIPE_WEBHOOK_SECRET") || "");
    } catch (err) {
      console.error(`Webhook signature verification failed: ${err.message}`);
      return new Response(JSON.stringify({ error: "Webhook signature verification failed" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Cria cliente do Supabase com role de serviço para operações administrativas
    const supabaseAdmin = createClient(Deno.env.get("SUPABASE_URL") ?? "", Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "", {
      auth: { persistSession: false },
    });

    // Processa eventos específicos
    switch (event.type) {
      case "checkout.session.completed": {
        // Recupera os dados da sessão
        const session = event.data.object;
        const customerId = session.customer;
        const subscriptionId = session.subscription;

        if (!customerId || !subscriptionId) {
          throw new Error("Missing customer or subscription ID");
        }

        // Recupera detalhes da assinatura
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);

        // Recupera o produto para determinar o plano
        const productId = subscription.items.data[0].price.product;
        const product = await stripe.products.retrieve(productId as string);

        // Mapeia o nome do produto para o tier
        let subscriptionTier;
        if (product.name.toLowerCase().includes("individual")) {
          subscriptionTier = "individual";
        } else if (product.name.toLowerCase().includes("compartilhado")) {
          subscriptionTier = "shared";
        } else if (product.name.toLowerCase().includes("empresarial")) {
          subscriptionTier = "business";
        } else {
          subscriptionTier = "individual"; // Padrão
        }

        // Calcula a data de término da assinatura
        const subscriptionEnd = new Date(subscription.current_period_end * 1000);

        // Busca o usuário pelo customer_id do Stripe
        const { data: customerData } = await supabaseAdmin
          .from("subscribers")
          .select("user_id, email")
          .eq("stripe_customer_id", customerId)
          .maybeSingle();

        let userId, userEmail;

        if (customerData) {
          userId = customerData.user_id;
          userEmail = customerData.email;
        } else {
          // Se não encontrou pelo customer_id, tenta buscar pelo metadata da assinatura
          if (subscription.metadata.user_id) {
            userId = subscription.metadata.user_id;
            // Busca o email do usuário pelo ID
            const { data: userData } = await supabaseAdmin.auth.admin.getUserById(userId);
            if (userData.user) {
              userEmail = userData.user.email;
            }
          }
        }

        if (!userId || !userEmail) {
          // Tenta buscar o email do customer no Stripe
          const customer = await stripe.customers.retrieve(customerId as string);
          userEmail = customer.email;

          // Busca o usuário pelo email
          if (userEmail) {
            const { data: userRecord } = await supabaseAdmin.from("profiles").select("id").eq("email", userEmail).single();

            if (userRecord) {
              userId = userRecord.id;
            }
          }
        }

        if (!userId || !userEmail) {
          throw new Error("Could not identify user");
        }

        // Atualiza ou cria o registro de inscrição
        const { data: existingSubscriber } = await supabaseAdmin.from("subscribers").select("id").eq("user_id", userId).maybeSingle();

        if (existingSubscriber) {
          // Atualiza o assinante existente
          await supabaseAdmin
            .from("subscribers")
            .update({
              stripe_customer_id: customerId,
              stripe_subscription_id: subscriptionId,
              subscribed: true,
              subscription_tier: subscriptionTier,
              subscription_end: subscriptionEnd.toISOString(),
              updated_at: new Date().toISOString(),
            })
            .eq("user_id", userId);
        } else {
          // Cria um novo registro de assinante
          await supabaseAdmin.from("subscribers").insert({
            user_id: userId,
            email: userEmail,
            stripe_customer_id: customerId,
            stripe_subscription_id: subscriptionId,
            subscribed: true,
            subscription_tier: subscriptionTier,
            subscription_end: subscriptionEnd.toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        }

        break;
      }

      case "customer.subscription.updated": {
        const subscription = event.data.object;
        const customerId = subscription.customer;

        // Busca o assinante pelo customer_id
        const { data: subscriber } = await supabaseAdmin.from("subscribers").select("user_id").eq("stripe_customer_id", customerId).maybeSingle();

        if (subscriber) {
          // Atualiza o status e a data de término da assinatura
          await supabaseAdmin
            .from("subscribers")
            .update({
              subscribed: subscription.status === "active",
              subscription_end: new Date(subscription.current_period_end * 1000).toISOString(),
              updated_at: new Date().toISOString(),
            })
            .eq("user_id", subscriber.user_id);
        }

        break;
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object;
        const customerId = subscription.customer;

        // Busca o assinante pelo customer_id
        const { data: subscriber } = await supabaseAdmin.from("subscribers").select("user_id").eq("stripe_customer_id", customerId).maybeSingle();

        if (subscriber) {
          // Marca a assinatura como cancelada
          await supabaseAdmin
            .from("subscribers")
            .update({
              subscribed: false,
              subscription_end: new Date().toISOString(), // Termina imediatamente
              updated_at: new Date().toISOString(),
            })
            .eq("user_id", subscriber.user_id);
        }

        break;
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Webhook error:", error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
