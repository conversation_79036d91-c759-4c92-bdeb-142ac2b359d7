
CREATE OR REPLACE FUNCTION public.get_invitation_details(token_param TEXT)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invitation_record public.workspace_invitations;
  workspace_name TEXT;
  inviter_name TEXT;
  result JSONB;
BEGIN
  -- Find invitation
  SELECT * INTO invitation_record
  FROM public.workspace_invitations
  WHERE token = token_param
    AND NOT accepted
    AND expires_at > now();
    
  IF invitation_record IS NULL THEN
    RETURN jsonb_build_object(
      'valid', false,
      'error', 'Invalid or expired invitation'
    );
  END IF;
  
  -- Get workspace name
  SELECT name INTO workspace_name
  FROM public.workspaces
  WHERE id = invitation_record.workspace_id;
  
  -- Get inviter name
  SELECT name INTO inviter_name
  FROM public.profiles
  WHERE id = invitation_record.invited_by;
  
  RETURN jsonb_build_object(
    'valid', true,
    'workspace_name', workspace_name,
    'invited_by_name', inviter_name,
    'role', invitation_record.role
  );
END;
$$;
