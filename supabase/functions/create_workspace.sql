
-- Function to create a new workspace with proper permissions
CREATE OR REPLACE FUNCTION public.create_workspace(workspace_name text)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  workspace_id uuid;
BEGIN
  -- Create the workspace
  INSERT INTO public.workspaces (name, owner_id)
  VALUES (workspace_name, auth.uid())
  RETURNING id INTO workspace_id;
  
  -- Add the current user as member and owner
  INSERT INTO public.workspace_members (workspace_id, user_id, role)
  VALUES (workspace_id, auth.uid(), 'owner');
  
  -- Return the workspace ID
  RETURN workspace_id;
END;
$$;
