-- Migration: Add priority field to tasks
-- Date: 2025-01-06
-- Description: Adds priority column to tasks table with controlled values

-- Add priority column with check constraint
ALTER TABLE tasks 
ADD COLUMN priority VARCHAR(10) DEFAULT 'baixa' 
CHECK (priority IN ('alta', 'baixa', 'média'));

-- Add comment for documentation
COMMENT ON COLUMN tasks.priority IS 'Prioridade da tarefa: alta, baixa, média';

-- Update existing tasks with default priority
UPDATE tasks 
SET priority = 'baixa' 
WHERE priority IS NULL;