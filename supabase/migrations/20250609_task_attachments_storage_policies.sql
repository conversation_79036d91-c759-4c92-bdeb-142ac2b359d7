-- Migration para criar bucket e políticas de storage para anexos de tarefas

-- Criar o bucket task-attachments (se não existir)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'task-attachments', 
  'task-attachments', 
  false, 
  52428800, -- 50MB limit
  ARRAY[
    'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    'application/pdf', 'text/plain', 'text/csv',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/zip', 'application/x-zip-compressed'
  ]
)
ON CONFLICT (id) DO NOTHING;

-- Política para permitir que membros do workspace visualizem arquivos
CREATE POLICY "Workspace members can view task attachments" ON storage.objects
FOR SELECT 
TO authenticated
USING (
  bucket_id = 'task-attachments' AND
  EXISTS (
    SELECT 1 
    FROM task_attachments ta
    JOIN tasks t ON ta.task_id = t.id
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE ta.file_path = name
    AND wm.user_id = auth.uid()
  )
);

-- Política para permitir que membros do workspace façam upload de arquivos
CREATE POLICY "Workspace members can upload task attachments" ON storage.objects
FOR INSERT 
TO authenticated
WITH CHECK (
  bucket_id = 'task-attachments' AND
  -- Verificar se o usuário tem acesso ao workspace através do task_id no path
  -- O path deve seguir o formato: workspace_id/task_id/filename
  EXISTS (
    SELECT 1
    FROM workspace_members wm
    WHERE wm.workspace_id::text = split_part(name, '/', 1)
    AND wm.user_id = auth.uid()
  )
);

-- Política para permitir que membros do workspace deletem arquivos de tarefas
CREATE POLICY "Workspace members can delete task attachments" ON storage.objects
FOR DELETE 
TO authenticated
USING (
  bucket_id = 'task-attachments' AND
  EXISTS (
    SELECT 1 
    FROM task_attachments ta
    JOIN tasks t ON ta.task_id = t.id
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE ta.file_path = name
    AND wm.user_id = auth.uid()
  )
);

-- Política para permitir que membros do workspace atualizem metadados de arquivos
CREATE POLICY "Workspace members can update task attachments metadata" ON storage.objects
FOR UPDATE 
TO authenticated
USING (
  bucket_id = 'task-attachments' AND
  EXISTS (
    SELECT 1 
    FROM task_attachments ta
    JOIN tasks t ON ta.task_id = t.id
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE ta.file_path = name
    AND wm.user_id = auth.uid()
  )
);

-- Adicionar created_by padrão na tabela task_attachments
ALTER TABLE task_attachments 
ALTER COLUMN created_by SET DEFAULT auth.uid();

-- Atualizar a política de inserção para incluir o created_by automaticamente
DROP POLICY IF EXISTS "Membros do workspace podem inserir anexos" ON task_attachments;

CREATE POLICY "Membros do workspace podem inserir anexos" ON task_attachments
FOR INSERT
WITH CHECK (
  created_by = auth.uid() AND
  EXISTS (
    SELECT 1
    FROM tasks t
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE t.id = task_attachments.task_id
    AND wm.user_id = auth.uid()
  )
);