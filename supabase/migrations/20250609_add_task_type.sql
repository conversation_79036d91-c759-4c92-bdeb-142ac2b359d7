-- Migration: Add task_type field to tasks table
-- Description: Adds a task_type column to categorize tasks as 'bug', 'nova_funcionalidade', or null

-- Add task_type column to the tasks table
ALTER TABLE tasks 
ADD COLUMN task_type TEXT CHECK (task_type IN ('bug', 'nova_funcionalidade') OR task_type IS NULL);

-- Add comment to the column for documentation
COMMENT ON COLUMN tasks.task_type IS 'Tipo da tarefa: bug, nova_funcionalidade ou null (sem tipo)';

-- Create index for better query performance on task_type
CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type);