-- Migration: Add backlog status support
-- Date: 2025-01-06  
-- Description: Expands status column and adds backlog support with optimized indexing

-- Remove existing status constraint
ALTER TABLE tasks DROP CONSTRAINT IF EXISTS tasks_status_check;

-- Expand status column type to support more values
ALTER TABLE tasks 
ALTER COLUMN status TYPE VARCHAR(20);

-- Add constraint for valid status values including backlog
ALTER TABLE tasks 
ADD CONSTRAINT tasks_status_check 
CHECK (status IN ('aguardando', 'em_progresso', 'concluido', 'backlog'));

-- Create index for backlog queries optimization
CREATE INDEX IF NOT EXISTS idx_tasks_status_backlog 
ON tasks(status) 
WHERE status = 'backlog';