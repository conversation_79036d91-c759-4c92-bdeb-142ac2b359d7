-- Function to ensure a user has a profile and at least one workspace
-- This handles the case where a profile doesn't exist for a user
CREATE OR REPLACE FUNCTION public.ensure_user_has_workspace()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_has_profile boolean;
  user_has_workspace boolean;
  default_workspace_id uuid;
BEGIN
  -- Check if user has a profile
  SELECT EXISTS (
    SELECT 1 FROM public.profiles WHERE id = auth.uid()
  ) INTO user_has_profile;
  
  -- If user doesn't have a profile, create one
  IF NOT user_has_profile THEN
    INSERT INTO public.profiles (id, email, created_at)
    VALUES (
      auth.uid(),
      (SELECT email FROM auth.users WHERE id = auth.uid()),
      NOW()
    );
  END IF;
  
  -- Check if user has at least one workspace
  SELECT EXISTS (
    SELECT 1 FROM public.workspace_members WHERE user_id = auth.uid()
  ) INTO user_has_workspace;
  
  -- If user doesn't have a workspace, create one
  IF NOT user_has_workspace THEN
    -- Create a default workspace
    INSERT INTO public.workspaces (name, owner_id, is_free_tier)
    VALUES ('Meu Espaço', auth.uid(), true)
    RETURNING id INTO default_workspace_id;
    
    -- Add user as owner of the workspace
    INSERT INTO public.workspace_members (workspace_id, user_id, role)
    VALUES (default_workspace_id, auth.uid(), 'owner');
  END IF;
END;
$$; 