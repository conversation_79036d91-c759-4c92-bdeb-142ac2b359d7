-- Create sticky notes table
CREATE TABLE IF NOT EXISTS public.sticky_notes (
  id UUID PRIMARY KEY,
  content TEXT NOT NULL,
  position JSONB NOT NULL DEFAULT '{"x": 0, "y": 0}',
  color TEXT NOT NULL DEFAULT '#FFD133',
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  page_id UUID NOT NULL REFERENCES public.pages(id) ON DELETE CASCADE,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE
);

-- Enable Row Level Security
ALTER TABLE public.sticky_notes ENABLE ROW LEVEL SECURITY;

-- Create policy to allow read access to workspace members
CREATE POLICY "Workspace members can view sticky notes" 
  ON public.sticky_notes 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.workspace_members
      WHERE workspace_members.workspace_id = sticky_notes.workspace_id
      AND workspace_members.user_id = auth.uid()
    )
  );

-- Create policy to allow insert for workspace members
CREATE POLICY "Workspace members can create sticky notes" 
  ON public.sticky_notes 
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.workspace_members
      WHERE workspace_members.workspace_id = sticky_notes.workspace_id
      AND workspace_members.user_id = auth.uid()
    )
  );

-- Create policy to allow update for owner or admin
CREATE POLICY "Users can update their own sticky notes" 
  ON public.sticky_notes 
  FOR UPDATE
  USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.workspace_members
      WHERE workspace_members.workspace_id = sticky_notes.workspace_id
      AND workspace_members.user_id = auth.uid()
      AND workspace_members.role IN ('owner', 'admin')
    )
  );

-- Create policy to allow delete for owner or admin
CREATE POLICY "Users can delete their own sticky notes" 
  ON public.sticky_notes 
  FOR DELETE
  USING (
    created_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.workspace_members
      WHERE workspace_members.workspace_id = sticky_notes.workspace_id
      AND workspace_members.user_id = auth.uid()
      AND workspace_members.role IN ('owner', 'admin')
    )
  );

-- Enable realtime subscriptions on the sticky_notes table
ALTER PUBLICATION supabase_realtime ADD TABLE public.sticky_notes;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS sticky_notes_page_id_idx ON public.sticky_notes (page_id);
CREATE INDEX IF NOT EXISTS sticky_notes_workspace_id_idx ON public.sticky_notes (workspace_id);
CREATE INDEX IF NOT EXISTS sticky_notes_created_by_idx ON public.sticky_notes (created_by); 