
-- This migration creates the tasks and task_subtasks tables
-- Run this in the Supabase SQL Editor

-- Create tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    assignee_id UUID REFERENCES auth.users(id),
    status TEXT NOT NULL,
    difficulty TEXT NOT NULL,
    category TEXT,
    organization TEXT,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    estimated_time TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    parent_id UUID REFERENCES public.tasks(id),
    workspace_id UUID REFERENCES public.workspaces(id) NOT NULL
);

-- Create task_subtasks table
CREATE TABLE IF NOT EXISTS public.task_subtasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    completed BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies for tasks
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Policy: Users can select tasks in workspaces they belong to
CREATE POLICY "Users can view tasks in their workspaces" 
ON public.tasks 
FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.workspace_members wm
        WHERE wm.workspace_id = tasks.workspace_id
        AND wm.user_id = auth.uid()
    )
);

-- Policy: Users can insert tasks in workspaces they belong to
CREATE POLICY "Users can insert tasks in their workspaces" 
ON public.tasks 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.workspace_members wm
        WHERE wm.workspace_id = workspace_id
        AND wm.user_id = auth.uid()
    )
);

-- Policy: Users can update tasks in workspaces they belong to
CREATE POLICY "Users can update tasks in their workspaces" 
ON public.tasks 
FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.workspace_members wm
        WHERE wm.workspace_id = tasks.workspace_id
        AND wm.user_id = auth.uid()
    )
);

-- Policy: Users can delete tasks in workspaces they belong to
CREATE POLICY "Users can delete tasks in their workspaces" 
ON public.tasks 
FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.workspace_members wm
        WHERE wm.workspace_id = tasks.workspace_id
        AND wm.user_id = auth.uid()
    )
);

-- Add RLS policies for task_subtasks
ALTER TABLE public.task_subtasks ENABLE ROW LEVEL SECURITY;

-- Policy: Users can select subtasks for tasks in workspaces they belong to
CREATE POLICY "Users can view subtasks for tasks in their workspaces" 
ON public.task_subtasks 
FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.tasks t
        JOIN public.workspace_members wm ON wm.workspace_id = t.workspace_id
        WHERE t.id = task_subtasks.task_id
        AND wm.user_id = auth.uid()
    )
);

-- Policy: Users can insert subtasks for tasks in workspaces they belong to
CREATE POLICY "Users can insert subtasks for tasks in their workspaces" 
ON public.task_subtasks 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.tasks t
        JOIN public.workspace_members wm ON wm.workspace_id = t.workspace_id
        WHERE t.id = task_id
        AND wm.user_id = auth.uid()
    )
);

-- Policy: Users can update subtasks for tasks in workspaces they belong to
CREATE POLICY "Users can update subtasks for tasks in their workspaces" 
ON public.task_subtasks 
FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.tasks t
        JOIN public.workspace_members wm ON wm.workspace_id = t.workspace_id
        WHERE t.id = task_subtasks.task_id
        AND wm.user_id = auth.uid()
    )
);

-- Policy: Users can delete subtasks for tasks in workspaces they belong to
CREATE POLICY "Users can delete subtasks for tasks in their workspaces" 
ON public.task_subtasks 
FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.tasks t
        JOIN public.workspace_members wm ON wm.workspace_id = t.workspace_id
        WHERE t.id = task_subtasks.task_id
        AND wm.user_id = auth.uid()
    )
);
