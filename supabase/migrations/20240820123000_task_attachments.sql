-- Migration para criar a tabela de anexos de tarefas
CREATE TABLE IF NOT EXISTS task_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Índice para acelerar buscas por task_id
CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> RLS (Row Level Security)
ALTER TABLE task_attachments ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON>r polí<PERSON>s de acesso para a tabela task_attachments
CREATE POLICY "Memb<PERSON> do workspace podem visualizar anexos" ON task_attachments
FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM tasks t
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE t.id = task_attachments.task_id
    AND wm.user_id = auth.uid()
  )
);

CREATE POLICY "Membros do workspace podem inserir anexos" ON task_attachments
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM tasks t
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE t.id = task_attachments.task_id
    AND wm.user_id = auth.uid()
  )
);

CREATE POLICY "Membros do workspace podem excluir anexos" ON task_attachments
FOR DELETE
USING (
  EXISTS (
    SELECT 1
    FROM tasks t
    JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
    WHERE t.id = task_attachments.task_id
    AND wm.user_id = auth.uid()
  )
); 