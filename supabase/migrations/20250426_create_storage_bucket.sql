
-- Create the public bucket for file uploads
INSERT INTO storage.buckets (id, name, public) 
VALUES ('public', 'public', true)
ON CONFLICT (id) DO NOTHING;

-- Create a policy to allow public access to files
CREATE POLICY "Give public access to all objects"
  ON storage.objects
  FOR SELECT
  TO public
  USING (bucket_id = 'public');

-- Create policies for uploads and deletes (limited to authenticated users)
CREATE POLICY "Allow authenticated users to upload files"
  ON storage.objects
  FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'public');

CREATE POLICY "Allow authenticated users to update their files"
  ON storage.objects
  FOR UPDATE
  TO authenticated
  USING (bucket_id = 'public' AND auth.uid() = owner);

CREATE POLICY "Allow authenticated users to delete their files"
  ON storage.objects
  FOR DELETE
  TO authenticated
  USING (bucket_id = 'public' AND auth.uid() = owner);
