-- Migration: Add task creator tracking
-- Date: 2025-01-06
-- Description: Adds creator identification columns and helper function

-- Add creator tracking columns
ALTER TABLE tasks 
ADD COLUMN created_by_id UUID REFERENCES auth.users(id),
ADD COLUMN created_by_name VARCHAR(255);

-- Add comments for documentation
COMMENT ON COLUMN tasks.created_by_id IS 'ID do usuário que criou a tarefa';
COMMENT ON COLUMN tasks.created_by_name IS 'Nome do usuário que criou a tarefa (snapshot)';

-- Update existing tasks with creator data where possible
-- Using assignee_id as fallback for creator (adjust as needed)
UPDATE tasks 
SET created_by_id = assignee_id,
    created_by_name = (
        SELECT COALESCE(
            raw_user_meta_data->>'full_name',
            raw_user_meta_data->>'name',
            email
        ) 
        FROM auth.users au
        JOIN profiles p ON p.id = au.id
        WHERE p.id = tasks.assignee_id
    )
WHERE created_by_id IS NULL AND assignee_id IS NOT NULL;

-- Create helper function to get user display name
CREATE OR REPLACE FUNCTION get_user_display_name(user_id UUID)
RETURNS VARCHAR(255)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    display_name VARCHAR(255);
BEGIN
    SELECT COALESCE(
        raw_user_meta_data->>'full_name',
        raw_user_meta_data->>'name', 
        email
    ) INTO display_name
    FROM auth.users 
    WHERE id = user_id;
    
    RETURN COALESCE(display_name, 'Usuário Desconhecido');
END;
$$;