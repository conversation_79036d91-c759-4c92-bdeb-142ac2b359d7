name: CI_CD

permissions:
  contents: write
  issues: write
  pull-requests: write
  id-token: write
  deployments: write
  actions: write
  checks: write
  statuses: write

on:
  pull_request:
    types: [closed]
    branches:
      - main
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  generate_version:
    # Executa em push para main ou quando um PR para main for merged
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.tag_version.outputs.new_tag }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}

      - name: Get current branch
        id: branch
        run: echo "branch=$(echo ${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}})" >> $GITHUB_OUTPUT

      - name: Generate SEMVER tag
        id: tag_version
        run: |
          # Define default versions for each environment
          if [[ "${{ github.event_name }}" == "pull_request" && "${{ github.event.pull_request.base.ref }}" == "dev" ]]; then
            default_version="0.0.5"
            env_prefix="dev-v"
          elif [[ "${{ github.event_name }}" == "pull_request" && "${{ github.event.pull_request.base.ref }}" == "beta" ]]; then
            default_version="0.0.6"
            env_prefix="beta-v"
          else
            default_version="1.0.0"
            env_prefix="v"
          fi

          # Fetch all tags
          git fetch --tags

          # Get latest tag for this environment
          latest_tag=$(git tag -l "${env_prefix}*" | sort -V | tail -n 1)

          if [ -z "$latest_tag" ]; then
            # No tags found for this environment, use default
            latest_tag="${env_prefix}${default_version}"
          fi

          echo "Found latest tag for environment: $latest_tag"

          # Remove prefix to get just the version number
          version_number="${latest_tag#${env_prefix}}"

          # Extract version components
          IFS='.' read -r -a version_parts <<< "$version_number"
          major="${version_parts[0]}"
          minor="${version_parts[1]}"
          patch="${version_parts[2]:-0}"

          # Increment patch version
          new_patch=$((patch + 1))
          if [[ $new_patch -gt 9 ]]; then
            new_patch=0
            new_minor=$((minor + 1))
            if [[ $new_minor -gt 9 ]]; then
              new_minor=0
              new_major=$((major + 1))
            else
              new_major=$major
            fi
          else
            new_major=$major
            new_minor=$minor
          fi

          # Format new version with environment prefix
          new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"

          # Verify if new tag already exists
          while git rev-parse "$new_tag" >/dev/null 2>&1; do
            echo "Tag $new_tag already exists, incrementing..."
            new_patch=$((new_patch + 1))
            new_tag="${env_prefix}${new_major}.${new_minor}.${new_patch}"
          done

          # Debug output
          echo "Current version: $latest_tag"
          echo "New version: $new_tag"

          # Set output
          echo "new_tag=${new_tag}" >> $GITHUB_OUTPUT

          # Configure git
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

          # Create and push tag
          git tag $new_tag
          git push origin $new_tag

  build_prod:
    needs: generate_version
    # Executa em push para main ou quando um PR para main for merged
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist

  deploy_prod:
    needs: [build_prod]
    # Executa em push para main ou quando um PR para main for merged
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist

      - name: Install sshpass
        run: sudo apt-get install -y sshpass

      - name: Deploy to Hostinger via SSH
        run: |
          # Compacta a pasta dist para transferir mais rapidamente
          tar -czf dist.tar.gz -C dist .

          # Transfere o arquivo compactado para o servidor (porta 65002)
          export SSHPASS="${{ secrets.SERVER_PASS }}"
          sshpass -e scp -P 65002 -o ConnectTimeout=60 -o StrictHostKeyChecking=no dist.tar.gz ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/tmp/

          # Extrai os arquivos no servidor de destino (porta 65002)
          sshpass -e ssh -p 65002 -o ConnectTimeout=60 -o StrictHostKeyChecking=no -v ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
            # Limpa o diretório de destino
            mkdir -p /domains/e-manager.apiservices.app.br/public_html
            rm -rf /domains/e-manager.apiservices.app.br/public_html/*
            
            # Extrai os arquivos para o diretório de destino
            tar -xzf /tmp/dist.tar.gz -C /domains/e-manager.apiservices.app.br/public_html
            
            # Remove o arquivo temporário
            rm /tmp/dist.tar.gz
            
            echo "Deployment completed successfully!"
          EOF
