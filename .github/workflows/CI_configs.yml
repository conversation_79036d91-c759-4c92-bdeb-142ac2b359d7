name: Direct SSH Deploy para Hostinger

on:
  workflow_dispatch:

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22"
          cache: "npm"

      - name: Install dependencies
        run: npm ci -f

      - name: Build project
        run: npm run build

      - name: Install sshpass
        run: sudo apt-get install -y sshpass

      - name: Deploy via SSH with password
        run: |
          # Compacta a pasta dist para transferir mais rapidamente
          tar -czf dist.tar.gz -C dist .

          # Transfere o arquivo compactado para o servidor (porta 65002)
          export SSHPASS="${{ secrets.SERVER_PASS }}"
          sshpass -e scp -P 65002 -o ConnectTimeout=60 -o StrictHostKeyChecking=no dist.tar.gz ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/tmp/

          # Extrai os arquivos no servidor de destino (porta 65002)
          sshpass -e ssh -p 65002 -o ConnectTimeout=60 -o StrictHostKeyChecking=no -v ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'

            ls -la
         
            rm -rf domains/e-manager.apiservices.app.br/public_html/*
            
            # Extrai os arquivos para o diretório de destino
            tar -xzf /tmp/dist.tar.gz -C domains/e-manager.apiservices.app.br/public_html
            
            # Remove o arquivo temporário
            rm /tmp/dist.tar.gz
            
            echo "Deployment completed successfully!"
          EOF
