# Stripe Webhook Server

Este é um servidor webhook simples para processar pagamentos do Stripe sem precisar de Edge Functions.

## 🚀 Deploy Rápido

### Opção 1: Vercel (Recomendado)

1. Crie uma conta no [Vercel](https://vercel.com)
2. Instale o CLI: `npm i -g vercel`
3. Na pasta `webhook/`, execute: `vercel`
4. Configure as variáveis de ambiente no dashboard da Vercel

### Opção 2: Railway

1. Crie uma conta no [Railway](https://railway.app)
2. Conecte seu repositório
3. Configure as variáveis de ambiente
4. Deploy automático

### Opção 3: Render

1. Crie uma conta no [Render](https://render.com)
2. Conecte seu repositório
3. Configure como Web Service
4. Configure as variáveis de ambiente

## 🔧 Configuração

### Variáveis de Ambiente

Configure estas variáveis no seu serviço de hosting:

```env
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_... (obtido no Stripe Dashboard)
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_SERVICE_KEY=sua-service-key-do-supabase
PORT=3001
```

### Configuração no Stripe

1. Acesse o [Stripe Dashboard](https://dashboard.stripe.com)
2. Vá para **Developers** → **Webhooks**
3. Clique em **Add endpoint**
4. URL: `https://seu-webhook-url.vercel.app/webhook`
5. Eventos para escutar:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
6. Copie o **Signing secret** e configure como `STRIPE_WEBHOOK_SECRET`

## 🧪 Teste Local

```bash
cd webhook
npm install
npm run dev
```

Para testar webhooks localmente, use o [Stripe CLI](https://stripe.com/docs/stripe-cli):

```bash
stripe listen --forward-to localhost:3001/webhook
```

## 📋 Endpoints

- `POST /webhook` - Recebe webhooks do Stripe
- `GET /health` - Health check

## 🔒 Segurança

- ✅ Verificação de assinatura do webhook
- ✅ Validação de eventos
- ✅ Logs detalhados
- ✅ Tratamento de erros

## 📝 Logs

O servidor registra todos os eventos importantes:
- Webhooks recebidos
- Atualizações de assinatura
- Erros de processamento

## 🚨 Importante

1. **Configure o webhook no Stripe** apontando para sua URL
2. **Configure todas as variáveis de ambiente**
3. **Teste com dados reais** antes de usar em produção
4. **Monitore os logs** para garantir que está funcionando

## 🆘 Troubleshooting

### Webhook não está sendo chamado
- Verifique se a URL está correta no Stripe
- Verifique se o servidor está rodando
- Verifique os logs do Stripe Dashboard

### Erro de assinatura
- Verifique se `STRIPE_WEBHOOK_SECRET` está correto
- Certifique-se de usar `express.raw()` para o body

### Erro de banco de dados
- Verifique se `SUPABASE_URL` e `SUPABASE_SERVICE_KEY` estão corretos
- Verifique se a tabela `subscribers` existe
