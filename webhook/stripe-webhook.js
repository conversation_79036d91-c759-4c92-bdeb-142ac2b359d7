// Webhook para processar pagamentos do Stripe
// Pode ser hospedado em Vercel, Netlify, Railway, etc.

const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const Stripe = require('stripe');

const app = express();

// Configurações
const STRIPE_SECRET_KEY = '***********************************************************************************************************';
const STRIPE_WEBHOOK_SECRET = 'whsec_...'; // Configure no Stripe Dashboard
const SUPABASE_URL = 'sua-url-do-supabase';
const SUPABASE_SERVICE_KEY = 'sua-service-key-do-supabase';

const stripe = new Stripe(STRIPE_SECRET_KEY);
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Middleware para raw body (necessário para verificar assinatura do webhook)
app.use('/webhook', express.raw({ type: 'application/json' }));
app.use(express.json());

// Endpoint do webhook
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    // Verificar assinatura do webhook
    event = stripe.webhooks.constructEvent(req.body, sig, STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('Received event:', event.type);

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;
      
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Função para processar checkout completado
async function handleCheckoutCompleted(session) {
  console.log('Processing checkout completed:', session.id);
  
  const customerId = session.customer;
  const clientReferenceId = session.client_reference_id; // user_id
  
  if (!clientReferenceId) {
    console.error('No client_reference_id found in session');
    return;
  }

  // Buscar a subscription criada
  const subscriptions = await stripe.subscriptions.list({
    customer: customerId,
    limit: 1,
  });

  if (subscriptions.data.length > 0) {
    const subscription = subscriptions.data[0];
    await updateSubscriberStatus(clientReferenceId, subscription, true);
  }
}

// Função para processar subscription criada
async function handleSubscriptionCreated(subscription) {
  console.log('Processing subscription created:', subscription.id);
  
  const customerId = subscription.customer;
  const userId = subscription.metadata.user_id;
  
  if (userId) {
    await updateSubscriberStatus(userId, subscription, true);
  } else {
    // Buscar pelo customer_id se não tiver metadata
    const { data: subscriber } = await supabase
      .from('subscribers')
      .select('user_id')
      .eq('stripe_customer_id', customerId)
      .single();
    
    if (subscriber) {
      await updateSubscriberStatus(subscriber.user_id, subscription, true);
    }
  }
}

// Função para processar subscription atualizada
async function handleSubscriptionUpdated(subscription) {
  console.log('Processing subscription updated:', subscription.id);
  
  const userId = subscription.metadata.user_id;
  const isActive = subscription.status === 'active';
  
  if (userId) {
    await updateSubscriberStatus(userId, subscription, isActive);
  }
}

// Função para processar subscription cancelada
async function handleSubscriptionDeleted(subscription) {
  console.log('Processing subscription deleted:', subscription.id);
  
  const userId = subscription.metadata.user_id;
  
  if (userId) {
    await updateSubscriberStatus(userId, subscription, false);
  }
}

// Função para processar pagamento bem-sucedido
async function handlePaymentSucceeded(invoice) {
  console.log('Processing payment succeeded:', invoice.id);
  
  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
    const userId = subscription.metadata.user_id;
    
    if (userId) {
      await updateSubscriberStatus(userId, subscription, true);
    }
  }
}

// Função para processar pagamento falhado
async function handlePaymentFailed(invoice) {
  console.log('Processing payment failed:', invoice.id);
  
  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
    const userId = subscription.metadata.user_id;
    
    if (userId) {
      // Não cancelar imediatamente, dar tempo para retry
      console.log(`Payment failed for user ${userId}, subscription ${subscription.id}`);
    }
  }
}

// Função para atualizar status do subscriber
async function updateSubscriberStatus(userId, subscription, isActive) {
  try {
    const updateData = {
      subscribed: isActive,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer,
      subscription_status: subscription.status,
      updated_at: new Date().toISOString(),
    };

    // Determinar o tier baseado no price_id
    if (subscription.items && subscription.items.data.length > 0) {
      const priceId = subscription.items.data[0].price.id;
      
      // Mapear price_id para tier
      const priceToTier = {
        'price_1RREVbIxqQbmbZWPxURraZMM': 'individual',
        'price_1RREVhIxqQbmbZWPznPRIxZN': 'shared',
        'price_1RREVoIxqQbmbZWPX8yDVRdX': 'business',
      };
      
      updateData.subscription_tier = priceToTier[priceId] || 'individual';
    }

    const { error } = await supabase
      .from('subscribers')
      .update(updateData)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating subscriber:', error);
    } else {
      console.log(`Updated subscriber ${userId} with status ${isActive}`);
    }
  } catch (error) {
    console.error('Error in updateSubscriberStatus:', error);
  }
}

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Webhook server running on port ${PORT}`);
});

module.exports = app;
