import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "./contexts/AuthContext";
import { WorkspaceProvider } from "./contexts/WorkspaceContext";
import { SubscriptionProvider } from "./contexts/SubscriptionProvider";
import { TasksProvider } from "./contexts/TasksContext";
import { RealtimeProvider } from "./contexts/RealtimeContext";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import AuthPage from "./pages/AuthPage";
import PaymentSuccessPage from "./pages/settings/payment/success";

const App = () => (
  <AuthProvider>
    <WorkspaceProvider>
      <SubscriptionProvider>
        <RealtimeProvider>
          <TasksProvider>
            <TooltipProvider>
              <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/workspace/:id" element={<Index />} />
                  <Route path="/settings/payment/success" element={<PaymentSuccessPage />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
              <Sonner position="top-right" />
              <Toaster />
            </TooltipProvider>
          </TasksProvider>
        </RealtimeProvider>
      </SubscriptionProvider>
    </WorkspaceProvider>
  </AuthProvider>
);

export default App;
