@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;

    --notion-page: 0 0% 100%;
    --notion-sidebar: 0 0% 97%;
    --notion-text: 0 0% 10%;
    --notion-muted: 0 0% 45%;
    --notion-hover: 0 0% 95%;
    --notion-border: 0 0% 90%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;

    --notion-page: 0 0% 10%;
    --notion-sidebar: 0 0% 13%;
    --notion-text: 0 0% 90%;
    --notion-muted: 0 0% 60%;
    --notion-hover: 0 0% 16%;
    --notion-border: 0 0% 20%;
  }

  html,
  body {
    @apply bg-notion-page text-notion-text;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-notion-page text-notion-text;
  }

  /* Notion editor styles */
  .notion-editor {
    @apply text-notion-text;
  }

  .notion-editor h1 {
    @apply text-3xl font-bold mt-6 mb-2 text-notion-text;
  }

  .notion-editor h2 {
    @apply text-2xl font-bold mt-5 mb-2 text-notion-text;
  }

  .notion-editor h3 {
    @apply text-xl font-bold mt-4 mb-2 text-notion-text;
  }

  .notion-editor p {
    @apply my-1.5 min-h-6 text-notion-text;
  }

  .notion-editor pre {
    @apply bg-gray-900 text-gray-100 p-4 rounded-md my-4 overflow-auto;
  }

  .notion-editor code {
    @apply font-mono text-sm;
  }

  .notion-editor ul {
    @apply list-disc ml-6 my-2;
  }

  .notion-editor ol {
    @apply list-decimal ml-6 my-2;
  }

  .notion-editor li {
    @apply my-1;
  }

  .notion-editor details {
    @apply my-2 bg-notion-hover p-2 rounded-md;
  }

  .notion-editor details summary {
    @apply cursor-pointer;
  }

  .notion-editor details > div {
    @apply pl-6 mt-2;
  }

  .notion-editor input[type="checkbox"] {
    @apply w-4 h-4 mr-2 rounded bg-transparent border border-notion-border;
  }

  /* Handle placeholder text in contenteditable elements */
  [contenteditable]:empty:before {
    content: attr(placeholder);
    @apply text-notion-muted pointer-events-none;
    position: absolute;
  }

  [contenteditable]:focus:empty:before {
    content: "";
  }

  .checkbox-todo {
    @apply flex items-center gap-2;
  }

  .checkbox-todo input[type="checkbox"] {
    @apply w-4 h-4 border border-notion-border rounded bg-transparent;
  }

  /* Content block styling */
  .content-block {
    position: relative;
    margin-bottom: 0.25rem;
  }

  .content-block:hover .block-controls {
    opacity: 1;
  }

  .block-controls {
    opacity: 0;
    transition: opacity 0.2s;
  }

  /* Style for code blocks */
  .code-block {
    position: relative;
    border-radius: 0.375rem;
    overflow: hidden;
    margin: 0.5rem 0;
  }

  .code-block pre {
    font-family: "Menlo", "Monaco", "Courier New", monospace;
    padding: 1rem;
    margin: 0;
    overflow-x: auto;
    background-color: #1e1e1e;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }

  .code-block pre div {
    min-height: 1.5rem;
    line-height: 1.5;
    font-size: 0.875rem;
    white-space: pre;
    word-break: normal;
    overflow-wrap: normal;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }

  .user-select-text {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    cursor: text !important;
  }

  .code-block .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #2d2d2d;
    border-bottom: 1px solid #3e3e3e;
  }

  .code-block .toolbar select {
    background-color: #3e3e3e;
    color: #e0e0e0;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    outline: none;
  }

  .code-block .toolbar button {
    background: transparent;
    border: none;
    color: #c0c0c0;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .code-block .toolbar button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .code-block [contenteditable] {
    outline: none;
    cursor: text;
    caret-color: white;
  }

  /* Style for toggle blocks */
  .toggle-block {
    @apply my-2;
  }

  .toggle-header {
    @apply flex items-center cursor-pointer;
  }

  .toggle-content {
    @apply pl-6 mt-1;
  }

  /* Style for columns layout */
  .columns-layout {
    @apply flex gap-4 my-2;
  }

  .column {
    @apply flex-1 p-2;
  }
}

.code-block pre,
.code-block pre div {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}
