
# Tasks Functionality Setup

To properly set up the tasks functionality, you need to:

1. Run the SQL migration script in your Supabase SQL Editor:
   - Go to your Supabase project dashboard
   - Click on "SQL Editor"
   - Copy the contents of `supabase/migrations/20250506_create_tasks_tables.sql`
   - Paste it into the SQL Editor and run it

2. After running the SQL script, refresh your application

The tasks functionality should now work properly. If you encounter any issues, check that:
- The tables `tasks` and `task_subtasks` exist in your Supabase database
- The RLS policies are properly set up
- Your user has the proper permissions to access workspaces

