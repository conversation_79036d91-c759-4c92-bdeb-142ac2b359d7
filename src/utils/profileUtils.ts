import { Profile } from "@/types";

// Verifica se o perfil está pendente (convite enviado, não aceito)
export const isProfilePending = (profile: Profile): boolean => {
  return profile && typeof profile.isPending === "boolean" ? profile.isPending : false;
};

// Verifica se o perfil está inativo
export const isProfileInactive = (profile: Profile): boolean => {
  return profile && typeof profile.isInactive === "boolean" ? profile.isInactive : false;
};

// Verifica se o perfil aceitou o convite
export const isProfileAccepted = (profile: Profile): boolean => {
  return profile && typeof profile.accepted === "boolean" ? profile.accepted : false;
};

// Verifica se o ID de perfil corresponde a um convite (começa com "invite_")
export const isInviteProfile = (profile: Profile): boolean => {
  return profile.id.toString().startsWith("invite_");
};

// Verifica se o ID de perfil é temporário (começa com "tmp_")
export const isTempProfile = (profile: Profile): boolean => {
  return profile.id.toString().startsWith("tmp_");
};

// Determina se um perfil pode ser atribuído a uma tarefa
export const canBeAssignedToTask = (profile: Profile): boolean => {
  // Não permitir atribuição se não tiver ID válido
  if (!profile || !profile.id) return false;

  // Não permitir atribuição para convites
  if (isInviteProfile(profile) || isTempProfile(profile)) return false;

  return true;
};

// Extrai o nome de exibição mais apropriado para o perfil
export const getDisplayName = (profile: Profile): string => {
  if (!profile) return "Não atribuído";

  // Se tiver nome definido e não vazio, usar
  if (profile.name && profile.name.trim() !== "") {
    // Remover qualquer parte que pareça um ID UUID
    if (profile.name.includes("Usuário")) {
      return "Usuário";
    }
    return profile.name;
  }

  // Se tiver email, usar a parte antes do @
  if (profile.email && profile.email.trim() !== "") {
    const emailName = profile.email.split("@")[0];
    // Capitalizar primeira letra
    return emailName.charAt(0).toUpperCase() + emailName.slice(1);
  }

  // Se tiver displayName definido, usar
  if (profile.displayName && profile.displayName.trim() !== "") {
    // Limpar também IDs do displayName
    if (profile.displayName.includes("Usuário")) {
      return "Usuário";
    }
    return profile.displayName;
  }

  // Caso não tenha nem nome nem email
  return "Usuário";
};

// Retorna informação de status para mostrar junto ao nome
export const getProfileStatus = (profile: Profile): string => {
  // Apenas mostrar "(Pendente)" para convites
  if (isInviteProfile(profile)) {
    return " (Pendente)";
  }

  return "";
};

// Renderiza o nome completo do perfil (nome + status)
export const renderProfileName = (profile: Profile | undefined): string => {
  if (!profile) return "Não atribuído";

  const displayName = getDisplayName(profile);
  const status = getProfileStatus(profile);

  return `${displayName}${status}`;
};
