import { supabase } from "@/integrations/supabase/client";
import axios from "axios";

interface EmailResponse {
  success: boolean;
  message?: string;
  emailSent?: boolean;
  error?: string;
}

export async function sendEmailService(options: any, key: string) {
  const url = "https://api.brevo.com/v3/smtp/email";
  const headers = {
    accept: "application/json",
    "api-key": key,
    "content-type": "application/json",
  };

  try {
    const response = await axios.post(url, options, { headers });
    if (response && response.status === 201) {
      console.info("Email sent successfully.");
      return { success: true };
    } else {
      throw new Error("Email sending failed.");
    }
  } catch (error) {
    console.error("Error during sending email:", error);
    return { success: false, error: `Failed to send email due to ${error}` };
  }
}

export async function sendInvitationEmail(email: string, workspaceName: string, inviterName: string, token: string): Promise<EmailResponse> {
  try {
    const inviteUrl = `https://e-manager.apiservices.app.br/invitation?token=${token}`;
    const apiKey = process.env.BREVO_API_KEY || "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";
    const subject = `Convite para participar do workspace ${workspaceName}`;
    const htmlContent = `
      <p>Olá, você recebeu o convite de <b>${inviterName}</b> para participar do workspace <b>${workspaceName}</b>.</p>
      <p>Crie agora a sua conta grátis e tenha acesso ao conteúdo colaborativo!</p>
      <p><a href="${inviteUrl}" target="_blank">Clique aqui para aceitar o convite</a></p>
      <p>Ou acesse: <a href="https://e-manager.apiservices.app.br/" target="_blank">https://e-manager.apiservices.app.br/</a></p>
    `;
    const options = {
      sender: { name: "E-Manager", email: "<EMAIL>" },
      to: [{ email }],
      subject,
      htmlContent,
    };
    if (!apiKey) {
      throw new Error("API Key do Brevo não configurada");
    }
    return await sendEmailService(options, apiKey);
  } catch (error) {
    console.error("Error sending invitation email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send invitation email",
      emailSent: false,
    };
  }
}
