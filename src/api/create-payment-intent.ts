import Stripe from "stripe";

// Inicializar Stripe com a chave secreta do ambiente ou usar a chave de teste
const stripeSecretKey = "sk_test_51PkVaQIxqQbmbZWPCBIzL2xCnseBLTpFDqJXi69c42nldk1qlO7iJZ2LNYo4uBSbc7cAjTnT5WVceJ0Q9tyzXP2r00trRZtDUb";
const stripe = new Stripe(stripeSecretKey, { apiVersion: "2023-10-16" as any });

// IDs dos preços no Stripe
const PRICE_IDS = {
  individual: "price_1PHxJ4ALvhA5CrMQMKv4Z1F0",
  shared: "price_1PHxJTALvhA5CrMQfULx4xRQ",
  business: "price_1PHxJkALvhA5CrMQBPe66nKB",
  additional_license_shared: "price_1PHxJTALvhA5CrMQfULx4xRQ", // Preço para licença adicional no plano compartilhado
  additional_license_business: "price_1PHxJkALvhA5CrMQBPe66nKB", // Preço para licença adicional no plano empresarial
};

interface CreatePaymentIntentParams {
  amount: number;
  planId: string;
  userId: string;
  email: string;
  additionalLicenses?: number; // Número de licenças adicionais a serem adquiridas
}

export async function createPaymentIntent(params: CreatePaymentIntentParams) {
  try {
    const { amount, planId, userId, email, additionalLicenses } = params;

    if (!amount || !planId || !userId || !email) {
      throw new Error("Parâmetros obrigatórios ausentes");
    }

    // Verificar se o usuário já tem assinatura
    const { data: subscriber } = await stripe.customers.list({
      email,
      limit: 1,
    });

    // Criar um Customer ou usar um existente
    let customerId;
    const customerList = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (customerList.data.length > 0) {
      customerId = customerList.data[0].id;
    } else {
      const customer = await stripe.customers.create({
        email,
        metadata: {
          user_id: userId,
        },
      });
      customerId = customer.id;
    }

    // Verificar se é uma compra de licenças adicionais ou nova assinatura
    if (additionalLicenses && additionalLicenses > 0) {
      // Buscar a assinatura atual do usuário
      const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        limit: 1,
        status: "active",
      });

      // Se o usuário tem uma assinatura ativa, adicionar licenças adicionais
      if (subscriptions.data.length > 0) {
        const subscription = subscriptions.data[0];

        // Determinar o preço correto com base no plano atual
        const subscriptionPlan = subscription.items.data[0].price.product;
        let additionalLicensePrice;

        // Verificar o produto para determinar o plano
        const product = await stripe.products.retrieve(subscriptionPlan as string);

        if (product.name.toLowerCase().includes("compartilhado")) {
          additionalLicensePrice = PRICE_IDS.additional_license_shared;
        } else if (product.name.toLowerCase().includes("empresarial")) {
          additionalLicensePrice = PRICE_IDS.additional_license_business;
        } else {
          // Se for plano individual, redirecionar para upgrade
          additionalLicensePrice = PRICE_IDS.shared;
        }

        // Criar uma sessão de checkout para adição de licenças
        const session = await stripe.checkout.sessions.create({
          customer: customerId,
          payment_method_types: ["card"],
          line_items: [
            {
              price: additionalLicensePrice,
              quantity: additionalLicenses,
            },
          ],
          mode: "subscription",
          success_url: `${process.env.NEXT_PUBLIC_URL || window.location.origin}/settings/payment/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${process.env.NEXT_PUBLIC_URL || window.location.origin}/settings`,
          metadata: {
            planId,
            userId,
            email,
            additionalLicenses: additionalLicenses.toString(),
          },
        });

        return {
          clientSecret: session.id,
          checkoutUrl: session.url,
        };
      }
    }

    // Se não for adição de licenças ou não tiver assinatura, criar uma nova assinatura normal
    // Obter o priceId com base no planId
    const priceId = PRICE_IDS[planId] || PRICE_IDS.individual;
    if (!priceId) {
      throw new Error("Plano inválido");
    }

    try {
      // Tentar criar uma sessão de checkout para assinatura
      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription", // Marca como uma assinatura recorrente
        success_url: `${process.env.NEXT_PUBLIC_URL || window.location.origin}/settings/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_URL || window.location.origin}/settings`,
        metadata: {
          planId,
          userId,
          email,
        },
      });

      return {
        clientSecret: session.id, // Usar session.id como clientSecret para redirecionamento
        checkoutUrl: session.url, // URL para redirecionamento direto para checkout
      };
    } catch (checkoutError) {
      console.error("Erro ao criar sessão de checkout:", checkoutError);

      // Fallback para PaymentIntent padrão se o checkout falhar
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency: "brl",
        customer: customerId,
        setup_future_usage: "off_session", // Permite reutilização futura
        automatic_payment_methods: {
          enabled: true,
        },
        metadata: {
          planId,
          userId,
          email,
        },
      });

      return {
        clientSecret: paymentIntent.client_secret,
      };
    }
  } catch (error) {
    console.error("Erro ao criar intenção de pagamento:", error);
    throw new Error("Erro ao criar intenção de pagamento");
  }
}
