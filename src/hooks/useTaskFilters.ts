
import { Task, TaskStatus } from "@/types";

export const useTaskFilters = (tasks: Task[]) => {
  // Filter tasks by date range
  const getTasksByDateRange = (startDate: Date, endDate: Date): Task[] => {
    return tasks.filter(task => {
      const taskStart = new Date(task.startDate);
      const taskEnd = new Date(task.endDate);
      
      // Check if the task is within the period
      return (
        (taskStart >= startDate && taskStart <= endDate) || 
        (taskEnd >= startDate && taskEnd <= endDate) ||
        (taskStart <= startDate && taskEnd >= endDate)
      );
    });
  };

  // Filter tasks by status
  const getTasksByStatus = (status: TaskStatus): Task[] => {
    return tasks.filter(task => task.status === status);
  };

  // Filter tasks by assignee
  const getTasksByAssignee = (assigneeId: string): Task[] => {
    return tasks.filter(task => task.assigneeId === assigneeId);
  };

  return {
    getTasksByDateRange,
    getTasksByStatus,
    getTasksByAssignee
  };
};
