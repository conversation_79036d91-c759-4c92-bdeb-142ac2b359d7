import { useState, useEffect } from "react";
import { supabase } from "../integrations/supabase/client";
import { toast } from "sonner";

export const useUserPreferences = (userId: string | undefined) => {
  const [lastWorkspaceId, setLastWorkspaceId] = useState<string | null>(null);
  const [sidebarWidth, setSidebarWidth] = useState<number>(256);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved preferences when userId changes
  useEffect(() => {
    if (!userId) {
      setIsLoading(false);
      return;
    }

    const loadPreferences = async () => {
      try {
        setIsLoading(true);
        // Try to load from localStorage first for faster loading
        const storedWorkspaceId = localStorage.getItem(`last_workspace_${userId}`);
        if (storedWorkspaceId) {
          setLastWorkspaceId(storedWorkspaceId);
        }

        // Then fetch from database to ensure consistency
        const { data, error } = await supabase.from("user_preferences").select("last_workspace_id").eq("user_id", userId).maybeSingle();

        if (error && error.code !== "PGRST116") throw error;

        if (data?.last_workspace_id) {
          setLastWorkspaceId(data.last_workspace_id);
          // Update localStorage if different
          if (data.last_workspace_id !== storedWorkspaceId) {
            localStorage.setItem(`last_workspace_${userId}`, data.last_workspace_id);
          }
        }

        // Load sidebar width apenas do localStorage
        const storedSidebarWidth = localStorage.getItem(`sidebar_width_${userId}`);
        if (storedSidebarWidth) {
          const parsedWidth = parseInt(storedSidebarWidth);
          // Validar se o valor está dentro dos limites aceitáveis
          if (parsedWidth >= 240 && parsedWidth <= 480) {
            setSidebarWidth(parsedWidth);
          } else {
            // Se valor inválido, usar padrão e corrigir localStorage
            setSidebarWidth(256);
            localStorage.setItem(`sidebar_width_${userId}`, "256");
          }
        } else {
          // Valor padrão se não houver no localStorage
          setSidebarWidth(256);
          localStorage.setItem(`sidebar_width_${userId}`, "256");
        }
      } catch (error) {
        console.error("Error loading preferences:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, [userId]);

  const updateLastWorkspace = async (workspaceId: string) => {
    if (!userId) return;

    try {
      // Update localStorage immediately for faster access
      localStorage.setItem(`last_workspace_${userId}`, workspaceId);

      // Update state
      setLastWorkspaceId(workspaceId);

      // Check if preference already exists
      const { data: existingPreference } = await supabase.from("user_preferences").select("id").eq("user_id", userId).maybeSingle();

      if (existingPreference) {
        // Update existing preference
        const { error: updateError } = await supabase
          .from("user_preferences")
          .update({
            last_workspace_id: workspaceId,
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", userId);

        if (updateError) throw updateError;
      } else {
        // Create new preference
        const { error: insertError } = await supabase.from("user_preferences").insert({
          user_id: userId,
          last_workspace_id: workspaceId,
          updated_at: new Date().toISOString(),
        });

        if (insertError) throw insertError;
      }
    } catch (error: any) {
      console.error("Error updating last workspace:", error);
      toast.error("Erro ao salvar preferência de workspace");
    }
  };

  const updateSidebarWidth = (width: number) => {
    if (!userId) return;

    try {
      // Update localStorage immediately
      localStorage.setItem(`sidebar_width_${userId}`, width.toString());

      // Update state
      setSidebarWidth(width);
    } catch (error: any) {
      console.error("Error updating sidebar width in localStorage:", error);
      // Falha silenciosa - não mostra toast de erro para o usuário
    }
  };

  return {
    lastWorkspaceId,
    sidebarWidth,
    isLoading,
    updateLastWorkspace,
    updateSidebarWidth,
  };
};
