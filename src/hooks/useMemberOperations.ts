import { v4 as uuidv4 } from "uuid";
import { supabase } from "../integrations/supabase/client";
import { toast } from "sonner";
import { Workspace, WorkspaceMember } from "../types";
import { sendInvitationEmail } from "../utils/emailUtils";

// Definir limites por plano
const PLAN_MEMBER_LIMITS = {
  individual: 1, // Apenas o próprio usuário (0 convites)
  shared: 5, // Até 4 convites + o próprio usuário
  business: 11, // Até 10 convites + o próprio usuário
};

// Definir custos por usuário adicional por plano
const EXTRA_USER_COSTS = {
  shared: 20.0, // R$ 20,00 por usuário adicional no plano compartilhado
  business: 17.0, // R$ 17,00 por usuário adicional no plano empresarial
};

export const useMemberOperations = (user: any, setWorkspaces: (updater: (prev: Workspace[]) => Workspace[]) => void) => {
  const inviteMember = async (workspaceId: string, email: string, role: WorkspaceMember["role"]): Promise<boolean> => {
    if (!user) return false;

    try {
      // Verificar assinatura e limites do usuário
      const { data: subscriber, error: subscriberError } = await supabase
        .from("subscribers")
        .select("subscribed, subscription_tier, is_admin")
        .eq("user_id", user.id)
        .maybeSingle();

      if (subscriberError) {
        console.error("Erro ao verificar assinatura:", subscriberError);
        toast.error("Falha ao verificar status de assinatura");
        return false;
      }

      // Se não for admin, verificar limites de convites
      const isAdmin = subscriber?.is_admin === true;
      const isSubscribed = subscriber?.subscribed === true;

      if (!isAdmin) {
        // Verificar o plano atual
        const currentPlan = isSubscribed ? subscriber?.subscription_tier || "individual" : "individual";

        // Plano individual não permite convites
        if (currentPlan === "individual") {
          toast.error("Seu plano atual não permite convidar usuários. Faça upgrade para o plano Compartilhado ou Empresarial.");
          return false;
        }

        // Verificar limites de usuários em todos os workspaces do proprietário
        const ownerWorkspaces = await getUserWorkspaceIds(user.id);

        // Buscar todos os membros dos workspaces do usuário
        let totalMembers = 0;
        for (const wsId of ownerWorkspaces) {
          const { count, error: countError } = await supabase
            .from("workspace_members")
            .select("*", { count: "exact", head: true })
            .eq("workspace_id", wsId);

          if (!countError && count !== null) {
            totalMembers += count;
          }

          // Também contar convites pendentes
          const { count: inviteCount, error: inviteCountError } = await supabase
            .from("workspace_invitations")
            .select("*", { count: "exact", head: true })
            .eq("workspace_id", wsId)
            .eq("accepted", false);

          if (!inviteCountError && inviteCount !== null) {
            totalMembers += inviteCount;
          }
        }

        // Verificar se já atingiu o limite
        const memberLimit = PLAN_MEMBER_LIMITS[currentPlan];

        if (totalMembers >= memberLimit) {
          if (currentPlan === "shared") {
            toast.error(
              `Você atingiu o limite de 4 usuários do plano Compartilhado. Faça upgrade para o plano Empresarial ou adquira licenças adicionais por R$${EXTRA_USER_COSTS.shared.toFixed(
                2
              )}/mês por usuário.`,
              {
                action: {
                  label: "Ver planos",
                  onClick: () => {
                    window.dispatchEvent(new CustomEvent("open-subscription-modal"));
                  },
                },
                duration: 8000,
              }
            );
          } else if (currentPlan === "business") {
            toast.error(
              `Você atingiu o limite de 10 usuários do plano Empresarial. Adquira licenças adicionais por R$${EXTRA_USER_COSTS.business.toFixed(
                2
              )}/mês por usuário.`,
              {
                action: {
                  label: "Ver planos",
                  onClick: () => {
                    window.dispatchEvent(new CustomEvent("open-subscription-modal"));
                  },
                },
                duration: 8000,
              }
            );
          }
          return false;
        }
      }

      // Generate a token for the invitation
      const token = uuidv4();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Check if user with this email exists in the workspace
      // First get the user id associated with this email (if any)
      const { data: profileData, error: profileError } = await supabase.from("profiles").select("id").eq("email", email.toLowerCase());

      if (profileError) {
        console.error("Error checking profile:", profileError);
        // Continue with invitation even if profile check fails
      }

      // If user exists, check if they're already a member
      if (profileData && profileData.length > 0) {
        const { data: existingMember, error: memberError } = await supabase
          .from("workspace_members")
          .select("user_id")
          .eq("workspace_id", workspaceId)
          .eq("user_id", profileData[0].id);

        if (memberError) {
          console.error("Error checking existing member:", memberError);
          // Continue with invitation even if member check fails
        }

        if (existingMember && existingMember.length > 0) {
          toast.error("Este usuário já é membro do workspace");
          return false;
        }
      }

      console.log("Checking for existing invitations for:", email.toLowerCase());

      // Check if there's already ANY invitation for this email in this workspace
      const { data: anyExistingInvitation, error: invitationCheckError } = await supabase
        .from("workspace_invitations")
        .select("id, accepted") // Select 'accepted' to potentially give a better message
        .eq("workspace_id", workspaceId)
        .eq("email", email.toLowerCase())
        .limit(1); // We only need to know if at least one exists

      if (invitationCheckError) {
        console.error("Error checking existing invitations:", invitationCheckError);
        toast.error("Falha ao verificar convites existentes");
        return false;
      }

      if (anyExistingInvitation && anyExistingInvitation.length > 0) {
        if (anyExistingInvitation[0].accepted) {
          toast.error("Este usuário já aceitou um convite para este workspace.");
        } else {
          toast.error("Já existe um convite pendente/expirado para este email neste workspace.");
        }
        return false;
      }

      // Create the invitation
      const invitation = {
        workspace_id: workspaceId,
        email: email.toLowerCase(),
        role,
        invited_by: user.id,
        token,
        expires_at: expiresAt.toISOString(),
        accepted: false,
      };

      console.log("Creating invitation:", invitation);

      const { data: invitationData, error } = await supabase.from("workspace_invitations").insert(invitation).select("id").single();

      if (error) {
        console.error("Error inviting member:", error);
        toast.error("Falha ao enviar convite");
        return false;
      }

      console.log("Invitation created successfully:", invitationData);

      // Get workspace and inviter details for the email
      const { data: workspaceData, error: workspaceError } = await supabase.from("workspaces").select("name").eq("id", workspaceId).single();

      if (workspaceError) {
        console.error("Error fetching workspace:", workspaceError);
        toast.warning("Convite criado, mas houve um erro ao obter detalhes do workspace");
        return true; // Invitation was created, so return true
      }

      const { data: inviterData, error: inviterError } = await supabase.from("profiles").select("name").eq("id", user.id).single();

      if (inviterError) {
        console.error("Error fetching inviter profile:", inviterError);
        toast.warning("Convite criado, mas houve um erro ao obter detalhes do convidador");
        return true; // Invitation was created, so return true
      }

      // Send invitation email
      if (workspaceData && inviterData) {
        console.log("Sending invitation email to:", email);
        const emailResult = await sendInvitationEmail(email, workspaceData.name, inviterData.name, token);

        console.log("Email sending result:", emailResult);

        if (!emailResult.success) {
          console.error("Detailed Email Sending Error:", emailResult.error);
          toast.warning(`Convite criado, mas falha ao enviar e-mail: ${emailResult.error || "Erro desconhecido"}`);
        }
      }

      toast.success(`Convite enviado para ${email}`);
      return true;
    } catch (error: any) {
      console.error("Error inviting member:", error);
      toast.error(error.message || "Falha ao enviar convite");
      return false;
    }
  };

  // Função auxiliar para obter os IDs de todos os workspaces do usuário
  const getUserWorkspaceIds = async (userId: string): Promise<string[]> => {
    const { data, error } = await supabase.from("workspaces").select("id").eq("owner_id", userId);

    if (error || !data) {
      console.error("Erro ao buscar workspaces do usuário:", error);
      return [];
    }

    return data.map((ws) => ws.id);
  };

  const updateMemberRole = async (workspaceId: string, userId: string, role: WorkspaceMember["role"]): Promise<void> => {
    try {
      const { error } = await supabase.from("workspace_members").update({ role }).eq("workspace_id", workspaceId).eq("user_id", userId);

      if (error) {
        console.error("Error updating member role:", error);
        toast.error("Falha ao atualizar função do membro");
        return;
      }

      setWorkspaces((prev) =>
        prev.map((workspace) => {
          if (workspace.id !== workspaceId) return workspace;

          const updatedMembers = workspace.members.map((member) => (member.userId === userId ? { ...member, role } : member));

          return { ...workspace, members: updatedMembers };
        })
      );

      toast.success("Função do membro atualizada");
    } catch (error: any) {
      console.error("Error updating member role:", error);
      toast.error(error.message || "Falha ao atualizar função do membro");
    }
  };

  const removeMember = async (workspaceId: string, userId: string): Promise<void> => {
    try {
      const { error } = await supabase.from("workspace_members").delete().eq("workspace_id", workspaceId).eq("user_id", userId);

      if (error) {
        console.error("Error removing member:", error);
        toast.error("Falha ao remover membro");
        return;
      }

      setWorkspaces((prev) =>
        prev.map((workspace) => {
          if (workspace.id !== workspaceId) return workspace;

          const updatedMembers = workspace.members.filter((member) => member.userId !== userId);
          return { ...workspace, members: updatedMembers };
        })
      );

      toast.success("Membro removido do workspace");
    } catch (error: any) {
      console.error("Error removing member:", error);
      toast.error(error.message || "Falha ao remover membro");
    }
  };

  return {
    inviteMember,
    updateMemberRole,
    removeMember,
  };
};
