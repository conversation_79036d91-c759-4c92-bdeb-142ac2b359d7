
import { useEffect } from "react";
import { Page } from "@/types";

const LAST_PAGE_KEY = "notion_clone_last_page";
const LAST_WORKSPACE_PAGE_KEY = "notion_clone_last_workspace_page_";
const LAST_CONTENT_TYPE_KEY = "notion_clone_last_content_type_";

export const useLastPageVisited = (currentPage: Page | null, setCurrentPage: (page: Page | null) => void, pages: Page[], contentType: string | null = null, setContentType: ((type: any) => void) | null = null) => {
  // Salvar a página atual no localStorage quando ela mudar, com referência ao workspace
  useEffect(() => {
    if (currentPage) {
      // Salvar o ID da página global
      localStorage.setItem(LAST_PAGE_KEY, currentPage.id);

      // Salvar o ID da página específico para o workspace atual
      if (currentPage.workspaceId) {
        localStorage.setItem(`${LAST_WORKSPACE_PAGE_KEY}${currentPage.workspaceId}`, currentPage.id);
      }
    }
  }, [currentPage?.id, currentPage?.workspaceId]);

  // Salvar o tipo de conteúdo atual (page, dashboard, kanban) com referência ao workspace
  useEffect(() => {
    if (contentType && currentPage?.workspaceId) {
      console.log("Salvando tipo de conteúdo:", contentType, "para workspace", currentPage.workspaceId);
      localStorage.setItem(`${LAST_CONTENT_TYPE_KEY}${currentPage.workspaceId}`, contentType);
    } else if (contentType && pages.length > 0 && pages[0].workspaceId) {
      // Se não temos uma página atual mas temos páginas com um workspace
      console.log("Salvando tipo de conteúdo (fallback):", contentType, "para workspace", pages[0].workspaceId);
      localStorage.setItem(`${LAST_CONTENT_TYPE_KEY}${pages[0].workspaceId}`, contentType);
    }
  }, [contentType, currentPage?.workspaceId, pages]);

  // Carregar o último tipo de conteúdo visitado
  useEffect(() => {
    if (setContentType && pages.length > 0) {
      // Tentar obter o tipo de conteúdo para o workspace atual
      const firstPage = pages[0];
      if (firstPage && firstPage.workspaceId) {
        const lastContentType = localStorage.getItem(`${LAST_CONTENT_TYPE_KEY}${firstPage.workspaceId}`);
        console.log("Carregando tipo de conteúdo salvo:", lastContentType);
        if (lastContentType) {
          setContentType(lastContentType);
        }
      }
    }
  }, [pages, setContentType]);

  // Carregar a última página visitada na inicialização
  useEffect(() => {
    if (pages.length > 0 && !currentPage) {
      let lastPageId = null;

      // Verificar se existe uma página específica para o workspace atual
      const firstPage = pages[0];
      if (firstPage && firstPage.workspaceId) {
        lastPageId = localStorage.getItem(`${LAST_WORKSPACE_PAGE_KEY}${firstPage.workspaceId}`);
      }

      // Se não encontrou uma página específica para o workspace, tenta obter a última página global
      if (!lastPageId) {
        lastPageId = localStorage.getItem(LAST_PAGE_KEY);
      }

      if (lastPageId) {
        const lastPage = pages.find((page) => page.id === lastPageId);
        if (lastPage) {
          setCurrentPage(lastPage);
          return;
        }
      }

      // Se não encontrar a última página ou não houver um ID salvo, use a primeira página
      setCurrentPage(pages[0]);
    }
  }, [pages, currentPage, setCurrentPage]);

  return null;
};
