import { useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import { supabase } from "../integrations/supabase/client";
import { toast } from "sonner";
import { Page, Workspace, convertToPage } from "../types";

export const usePageOperations = (user: any, currentWorkspace: Workspace | null) => {
  const [pages, setPages] = useState<Page[]>([]);
  const [currentPage, setCurrentPage] = useState<Page | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const fetchPagesForWorkspace = async (workspaceId: string) => {
    try {
      console.log("Fetching pages for workspace:", workspaceId);

      const { data: pagesData, error: pagesError } = await supabase
        .from("pages")
        .select("*")
        .eq("workspace_id", workspaceId)
        .eq("is_archived", false)
        .order("created_at", { ascending: false });

      if (pagesError) {
        console.error("Error fetching pages:", pagesError);
        toast.error("Falha ao carregar páginas");
        return;
      }

      console.log("Fetched pages:", pagesData);

      if (pagesData && pagesData.length > 0) {
        // Converter os dados com a função auxiliar existente
        const typedPages = pagesData
          .map((page) => {
            if (!page) return null;
            return convertToPage(page);
          })
          .filter(Boolean) as Page[];

        setPages(typedPages);
        console.log("Processed pages with icons:", typedPages);

        if (typedPages.length > 0) {
          setCurrentPage(typedPages[0]);
        }
      } else {
        setPages([]);
        setCurrentPage(null);
      }
    } catch (error) {
      console.error("Error fetching pages:", error);
      toast.error("Falha ao carregar páginas");
    }
  };

  const createPage = async (title: string, parentId?: string | null): Promise<Page | null> => {
    if (!user || !currentWorkspace) return null;

    try {
      // Criar nova página independentemente do status da assinatura
      const newPageId = uuidv4();
      const now = new Date();
      const initialContent = JSON.stringify({
        metadata: { icon: "📄" }, // Default icon for new pages
        type: "text",
        content: [],
      });

      const newPage = {
        id: newPageId,
        created_by: user.id,
        created_at: now.toISOString(),
        updated_at: now.toISOString(),
        workspace_id: currentWorkspace.id,
        title,
        content: initialContent,
        parent_id: parentId || null,
        cover: null,
        is_archived: false,
      };

      const { data, error } = await supabase.from("pages").insert([newPage]).select("*");

      if (error) {
        console.error("Error creating page:", error);
        toast.error("Falha ao criar página");
        return null;
      }

      const createdPage = data ? data[0] : null;

      if (createdPage) {
        // Usar a função auxiliar para converter
        const typedCreatedPage = convertToPage(createdPage);
        setPages((prevPages) => [typedCreatedPage, ...prevPages]);
        setCurrentPage(typedCreatedPage);
        return typedCreatedPage;
      } else {
        toast.error("Falha ao criar página");
        return null;
      }
    } catch (error) {
      console.error("Error creating page:", error);
      toast.error("Falha ao criar página");
      return null;
    }
  };

  const updatePage = async (id: string, data: Partial<Page>): Promise<void> => {
    try {
      const now = new Date();
      const updateData: any = {
        ...data,
        updated_at: now.toISOString(), // Convert to string for Supabase
        updated_by: user?.id, // Adicionar o usuário que fez a atualização
      };

      // Remove fields that don't match the database schema
      delete updateData.workspaceId;
      delete updateData.parentId;
      delete updateData.createdAt;
      delete updateData.updatedAt;
      delete updateData.isArchived;
      delete updateData.icon; // Remove icon as it's stored in content

      // Map fields to database column names
      if (updateData.parentId !== undefined) {
        updateData.parent_id = updateData.parentId;
      }
      if (updateData.workspaceId !== undefined) {
        updateData.workspace_id = updateData.workspaceId;
      }
      if (updateData.isArchived !== undefined) {
        updateData.is_archived = updateData.isArchived;
      }

      // Verificar se precisamos atualizar
      const { data: currentPageData, error: fetchError } = await supabase.from("pages").select("content, title").eq("id", id).single();

      if (fetchError) {
        console.error("Error fetching current page data:", fetchError);
        // Continuar com a atualização mesmo sem conseguir verificar
      } else {
        // Verificar se o conteúdo realmente mudou antes de atualizar
        let hasChanges = false;

        if (updateData.content && currentPageData.content !== updateData.content) {
          hasChanges = true;
        }

        if (updateData.title && currentPageData.title !== updateData.title) {
          hasChanges = true;
        }

        // Se não houver mudanças, retorne sem atualizar
        if (!hasChanges) {
          console.log("Sem alterações detectadas, pulando atualização");
          setLastSaved(now); // Atualizar o estado como se tivesse salvo
          return;
        }
      }

      const { error } = await supabase.from("pages").update(updateData).eq("id", id);

      if (error) {
        console.error("Error updating page:", error);
        toast.error("Falha ao atualizar página");
        return;
      }

      setPages((prevPages) =>
        prevPages.map((page) => {
          if (page.id === id) {
            return { ...page, ...data, updatedAt: now };
          }
          return page;
        })
      );
      setCurrentPage((prev) => (prev?.id === id ? { ...prev, ...data, updatedAt: now } : prev));
      setLastSaved(now);
    } catch (error) {
      console.error("Error updating page:", error);
      toast.error("Falha ao atualizar página");
    }
  };

  const deletePage = async (id: string): Promise<void> => {
    try {
      const { error } = await supabase.from("pages").delete().eq("id", id);

      if (error) {
        console.error("Error deleting page:", error);
        toast.error("Falha ao excluir página");
        return;
      }

      setPages((prevPages) => prevPages.filter((page) => page.id !== id));
      setCurrentPage((prev) => (prev?.id === id ? null : prev));
    } catch (error) {
      console.error("Error deleting page:", error);
      toast.error("Falha ao excluir página");
    }
  };

  const reorderPages = async (pageId: string, newParentId: string | null): Promise<void> => {
    try {
      const { error } = await supabase.from("pages").update({ parent_id: newParentId }).eq("id", pageId);

      if (error) {
        console.error("Error reordering page:", error);
        toast.error("Falha ao reordenar página");
        return;
      }

      setPages((prevPages) =>
        prevPages.map((page) => {
          if (page.id === pageId) {
            return { ...page, parent_id: newParentId };
          }
          return page;
        })
      );
    } catch (error) {
      console.error("Error reordering page:", error);
      toast.error("Falha ao reordenar página");
    }
  };

  useEffect(() => {
    if (currentWorkspace) {
      fetchPagesForWorkspace(currentWorkspace.id);
    }
  }, [currentWorkspace]);

  return {
    pages,
    setPages,
    currentPage,
    setCurrentPage,
    createPage,
    updatePage,
    deletePage,
    reorderPages,
    lastSaved,
  };
};
