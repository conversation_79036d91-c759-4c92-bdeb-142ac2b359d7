
import { useEffect } from 'react';
import { supabase } from "../integrations/supabase/client";
import { toast } from "sonner";
import { convertToWorkspace } from "../types";

export const useWorkspaceInitialization = (
  user: any,
  setWorkspaces: any,
  setCurrentWorkspace: any,
  currentWorkspace: any,
  fetchPagesForWorkspace: any,
  setIsLoading: any
) => {
  useEffect(() => {
    const fetchWorkspaces = async () => {
      if (!user) {
        setWorkspaces([]);
        setCurrentWorkspace(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Make sure user has at least one workspace (this will handle RLS automatically)
        const { error: ensureError } = await supabase.rpc('ensure_user_has_workspace');
        if (ensureError) {
          console.error('Error ensuring workspace:', ensureError);
        }

        // Fetch workspaces where the current user is a member
        const { data: workspaceData, error: workspaceError } = await supabase
          .from('workspace_members')
          .select(`
            workspaces (
              *
            ),
            role
          `)
          .eq('user_id', user.id);

        if (workspaceError) {
          console.error('Error fetching workspaces:', workspaceError);
          toast.error('Falha ao carregar workspaces');
          setIsLoading(false);
          return;
        }

        if (workspaceData && workspaceData.length > 0) {
          const fetchedWorkspaces = workspaceData.map(item => {
            const workspace = item.workspaces;
            return convertToWorkspace({
              ...workspace,
              role: item.role
            });
          });

          console.log('Fetched workspaces:', fetchedWorkspaces);
          setWorkspaces(fetchedWorkspaces);
        } else {
          console.log('No workspaces found for user');
        }
      } catch (error) {
        console.error('Error fetching workspaces:', error);
        toast.error('Falha ao carregar workspaces');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkspaces();
  }, [user]);
};
