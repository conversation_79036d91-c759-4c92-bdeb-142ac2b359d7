import React, { useState } from "react";
import { TaskAttachment } from "@/types";
import { uploadTaskAttachment, deleteTaskAttachment, loadTaskAttachments, getAttachmentUrl } from "@/services/taskAttachmentService";
import { toast } from "sonner";

export const useTaskAttachments = (taskId: string, workspaceId: string) => {
  const [attachments, setAttachments] = useState<TaskAttachment[]>([]);
  const [loading, setLoading] = useState(false);
  

  // Carregar anexos de uma tarefa
  const loadAttachments = async () => {
    if (!taskId) {
      setAttachments([]);
      return;
    }

    setLoading(true);
    try {
      const taskAttachments = await loadTaskAttachments(taskId);
      setAttachments(taskAttachments);
    } catch (error) {
      console.error("Erro ao carregar anexos:", error);
      toast.error("Falha ao carregar anexos");
    } finally {
      setLoading(false);
    }
  };

  // Fazer upload de um novo anexo
  const uploadAttachment = async (file: File): Promise<TaskAttachment | null> => {
    if (!taskId || !workspaceId) {
      toast.error("Informações da tarefa incompletas");
      return null;
    }

    setLoading(true);
    try {
      const newAttachment = await uploadTaskAttachment(taskId, file, workspaceId);
      if (newAttachment) {
        setAttachments((prev) => [...prev, newAttachment]);
        toast.success("Arquivo anexado com sucesso");
        return newAttachment;
      }
      return null;
    } catch (error) {
      console.error("Erro ao fazer upload:", error);
      toast.error("Falha ao anexar arquivo");
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Remover um anexo
  const removeAttachment = async (attachmentId: string) => {
    const attachment = attachments.find((a) => a.id === attachmentId);
    if (!attachment) return false;

    setLoading(true);
    try {
      const success = await deleteTaskAttachment(attachment);
      if (success) {
        setAttachments((prev) => prev.filter((a) => a.id !== attachmentId));
        toast.success("Anexo removido com sucesso");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Erro ao remover anexo:", error);
      toast.error("Falha ao remover anexo");
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Obter URL para visualizar um anexo
  const getAttachmentViewUrl = async (attachmentId: string): Promise<string | null> => {
    const attachment = attachments.find((a) => a.id === attachmentId);
    if (!attachment) return null;

    try {
      // Se já tiver URL e ainda for válida, usa ela
      if (attachment.url) {
        return attachment.url;
      }

      // Senão, gera uma nova URL
      const url = await getAttachmentUrl(attachment.filePath);

      // Atualiza o anexo localmente com a nova URL
      if (url) {
        setAttachments((prev) => prev.map((a) => (a.id === attachmentId ? { ...a, url } : a)));
      }

      return url;
    } catch (error) {
      console.error("Erro ao obter URL do anexo:", error);
      return null;
    }
  };

  // Auto-carregar anexos quando taskId mudar
  React.useEffect(() => {
    if (taskId) {
      loadAttachments();
    } else {
      setAttachments([]);
    }
  }, [taskId]);

  return {
    attachments,
    loading,
    loadAttachments,
    uploadAttachment,
    removeAttachment,
    getAttachmentViewUrl,
  };
};
