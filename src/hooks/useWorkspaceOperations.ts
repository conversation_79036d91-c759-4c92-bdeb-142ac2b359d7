import { useState } from "react";
import { supabase } from "../integrations/supabase/client";
import { toast } from "sonner";
import { Workspace, Profile, WorkspaceMember } from "../types";
import { v4 as uuidv4 } from "uuid";

export const useWorkspaceOperations = (user: any, profile: Profile | null) => {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [lastUpdateTimestamp, setLastUpdateTimestamp] = useState<number>(0);

  const loadWorkspaces = async () => {
    if (!user) return [];

    try {
      // Get all workspaces where the user is a member
      const { data: memberships, error: membershipError } = await supabase
        .from("workspace_members")
        .select("workspace_id, role")
        .eq("user_id", user.id);

      if (membershipError) {
        console.error("Error fetching memberships:", membershipError);
        toast.error("Falha ao carregar workspaces");
        return [];
      }

      if (!memberships || memberships.length === 0) return [];

      const workspaceIds = memberships.map((m) => m.workspace_id);

      // Get workspace details
      const { data: workspacesData, error: workspacesError } = await supabase.from("workspaces").select("*").in("id", workspaceIds);

      if (workspacesError) {
        console.error("Error fetching workspaces:", workspacesError);
        toast.error("Falha ao carregar workspaces");
        return [];
      }

      if (!workspacesData || workspacesData.length === 0) return [];

      // For each workspace, get the owner's info
      const validWorkspaces: Workspace[] = [];

      for (const workspace of workspacesData) {
        // Get the current user's role in this workspace
        const membership = memberships.find((m) => m.workspace_id === workspace.id);
        const role = membership ? (membership.role as WorkspaceMember["role"]) : "member";

        const userWorkspace: Workspace = {
          id: workspace.id,
          name: workspace.name,
          ownerId: workspace.owner_id,
          icon: workspace.icon,
          members: [],
          role,
          isFreeTier: workspace.is_free_tier || false,
          createdAt: new Date(workspace.created_at),
          updatedAt: new Date(workspace.updated_at),
        };

        validWorkspaces.push(userWorkspace);
      }

      setWorkspaces(validWorkspaces);

      // If there's at least one workspace and no current workspace is set,
      // set the first one as current
      if (validWorkspaces.length > 0 && !currentWorkspace) {
        setCurrentWorkspace(validWorkspaces[0]);
      }

      return validWorkspaces;
    } catch (error) {
      console.error("Error loading workspaces:", error);
      toast.error("Falha ao carregar workspaces");
      return [];
    }
  };

  const createWorkspace = async (name: string): Promise<Workspace | null> => {
    if (!user) return null;
    try {
      // Verificar se o usuário tem assinatura ativa ou está no período de teste
      const { data: subscriber, error: subscriberError } = await supabase
        .from("subscribers")
        .select("subscribed, trial_end_date, is_admin")
        .eq("user_id", user.id)
        .maybeSingle();

      if (subscriberError) {
        console.error("Erro ao verificar assinatura:", subscriberError);
        toast.error("Erro ao verificar assinatura");
        return null;
      }

      // Se o usuário for admin ou tiver assinatura ativa, permitir criar sem restrições
      const isAdmin = subscriber?.is_admin === true;
      const isSubscribed = subscriber?.subscribed === true;

      // Se o usuário não for admin ou não tiver assinatura ativa, verificar as limitações
      if (!isAdmin && !isSubscribed) {
        // Verificar se ainda está no período de teste
        const isInTrial = subscriber?.trial_end_date && new Date(subscriber.trial_end_date) > new Date();

        // Se o usuário já tiver workspaces e não estiver assinante ou em período de teste
        if (workspaces.length > 0 && !isInTrial) {
          // Verificar se algum workspace é de propriedade do usuário
          const ownedWorkspaces = workspaces.filter((ws) => ws.ownerId === user.id);

          if (ownedWorkspaces.length > 0) {
            toast.error("Atualize o plano de pagamento para desbloquear os recursos ilimitados");
            return null;
          }
        }
      }

      // First ensure the user has a workspace
      const { error: ensureError } = await supabase.rpc("ensure_user_has_workspace");
      if (ensureError) {
        console.error("Error ensuring workspace:", ensureError);
        toast.error("Erro ao verificar workspaces");
        return null;
      }

      // Now create a new workspace via our updated database function
      const { data: workspaceData, error: workspaceError } = await supabase.rpc("create_workspace", {
        workspace_name: name,
      });

      if (workspaceError) {
        console.error("Error creating workspace:", workspaceError);
        toast.error("Falha ao criar workspace");
        return null;
      }

      // Extract workspace information from the returned JSON
      if (workspaceData) {
        // Ensure correct typing for workspaceId when accessing it from workspaceData
        const workspaceId = typeof workspaceData === "object" && workspaceData !== null ? (workspaceData as any).id : null;

        if (!workspaceId) {
          console.error("Invalid workspace data returned:", workspaceData);
          toast.error("Dados de workspace inválidos");
          return null;
        }

        // Fetch the created workspace details
        const { data: createdWorkspace, error: fetchError } = await supabase
          .from("workspace_members")
          .select(
            `
            workspaces (*),
            role
          `
          )
          .eq("workspace_id", workspaceId)
          .eq("user_id", user.id)
          .single();

        if (fetchError) {
          console.error("Error fetching created workspace:", fetchError);
          toast.error("Falha ao obter detalhes do workspace");
          return null;
        }

        // Create a properly formatted workspace object
        const workspace: Workspace = {
          id: createdWorkspace.workspaces.id,
          name: createdWorkspace.workspaces.name,
          ownerId: createdWorkspace.workspaces.owner_id,
          icon: createdWorkspace.workspaces.icon,
          isFreeTier: createdWorkspace.workspaces.is_free_tier,
          members: [],
          role: createdWorkspace.role as WorkspaceMember["role"],
          createdAt: new Date(createdWorkspace.workspaces.created_at),
          updatedAt: new Date(createdWorkspace.workspaces.updated_at),
        };

        setWorkspaces((prev) => [...prev, workspace]);
        setCurrentWorkspace(workspace);

        // Criar páginas padrão (Dashboard e Kanban) automaticamente
        await supabase.from("pages").insert([
          {
            id: uuidv4(),
            created_by: user.id,
            workspace_id: workspace.id,
            title: "Dashboard",
            content: JSON.stringify({ isDefaultDashboard: true }),
            is_archived: false,
          },
          {
            id: uuidv4(),
            created_by: user.id,
            workspace_id: workspace.id,
            title: "Kanban",
            content: JSON.stringify({ isDefaultKanban: true }),
            is_archived: false,
          },
        ]);

        toast.success("Workspace criado com sucesso");
        return workspace;
      }

      return null;
    } catch (error: any) {
      console.error("Error creating workspace:", error);
      toast.error(error.message || "Falha ao criar workspace");
      return null;
    }
  };

  const updateWorkspace = async (id: string, data: Partial<Workspace>): Promise<void> => {
    try {
      // Check if we need to throttle updates
      const now = Date.now();
      if (now - lastUpdateTimestamp < 1000) {
        console.log("Skipping workspace update, too frequent");
        return;
      }

      // Check user permissions first
      const { data: memberData, error: memberError } = await supabase
        .from("workspace_members")
        .select("role")
        .eq("workspace_id", id)
        .eq("user_id", user.id)
        .single();

      if (memberError || !memberData || !["owner", "admin"].includes(memberData.role)) {
        toast.error("Você não tem permissão para atualizar este workspace");
        return;
      }

      // Map to database fields
      const supabaseData: any = {};
      if (data.name) supabaseData.name = data.name;
      if (data.icon !== undefined) supabaseData.icon = data.icon;

      // Only proceed if we have data to update
      if (Object.keys(supabaseData).length === 0) {
        return;
      }

      console.log("Updating workspace with data:", supabaseData);

      const { error } = await supabase.from("workspaces").update(supabaseData).eq("id", id);

      if (error) {
        console.error("Error updating workspace:", error);
        toast.error("Falha ao atualizar workspace");
        return;
      }

      setWorkspaces((prev) => prev.map((workspace) => (workspace.id === id ? { ...workspace, ...data } : workspace)));

      if (currentWorkspace?.id === id) {
        setCurrentWorkspace((prev) => (prev ? { ...prev, ...data } : null));
      }

      setLastUpdateTimestamp(now);
      toast.success("Workspace atualizado");
    } catch (error: any) {
      console.error("Error updating workspace:", error);
      toast.error(error.message || "Falha ao atualizar workspace");
    }
  };

  const deleteWorkspace = async (id: string): Promise<void> => {
    try {
      const { data: memberData, error: memberError } = await supabase
        .from("workspace_members")
        .select("role")
        .eq("workspace_id", id)
        .eq("user_id", user.id)
        .single();

      if (memberError || !memberData || memberData.role !== "owner") {
        toast.error("Apenas o proprietário pode excluir um workspace");
        return;
      }

      const { error } = await supabase.from("workspaces").delete().eq("id", id);

      if (error) {
        console.error("Error deleting workspace:", error);
        toast.error("Falha ao excluir workspace");
        return;
      }

      setWorkspaces((prev) => prev.filter((workspace) => workspace.id !== id));

      if (currentWorkspace?.id === id) {
        const nextWorkspace = workspaces.find((w) => w.id !== id) || null;
        setCurrentWorkspace(nextWorkspace);
      }

      toast.success("Workspace excluído");
    } catch (error: any) {
      console.error("Error deleting workspace:", error);
      toast.error(error.message || "Falha ao excluir workspace");
    }
  };

  return {
    workspaces,
    setWorkspaces,
    currentWorkspace,
    setCurrentWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    loadWorkspaces,
  };
};
