import { useState } from "react";
import { Task, TaskSubtask } from "@/types";
import { toast } from "sonner";
import {
  addTaskToSupabase,
  updateTaskInSupabase,
  deleteTaskFromSupabase,
  addSubtaskToSupabase,
  updateSubtaskInSupabase,
  deleteSubtaskFromSupabase,
} from "@/services/taskService";

export const useTaskOperations = (initialTasks: Task[] = [], setTasks: React.Dispatch<React.SetStateAction<Task[]>>) => {
  // Add a new task
  const addTask = async (taskData: Omit<Task, "id" | "createdAt" | "updatedAt">): Promise<Task> => {
    try {
      // Garantir que as datas sejam objetos Date válidos antes de salvar
      const normalizedData = {
        ...taskData,
        // Converter para Date se for string
        startDate: typeof taskData.startDate === "string" ? new Date(taskData.startDate) : taskData.startDate,
        endDate: typeof taskData.endDate === "string" ? new Date(taskData.endDate) : taskData.endDate,
      };

      const newTask = await addTaskToSupabase(normalizedData);

      if (newTask) {
        setTasks((prev) => [...prev, newTask]);
        toast.success("Tarefa criada com sucesso");
        return newTask;
      }
      throw new Error("Falha ao criar tarefa");
    } catch (error) {
      console.error("Erro ao adicionar tarefa:", error);
      toast.error("Falha ao criar tarefa");
      throw error;
    }
  };

  // Update an existing task
  const updateTask = async (id: string, taskUpdate: Partial<Task>): Promise<Task | null> => {
    try {
      // Normalizar datas se estiverem presentes no objeto de atualização
      const updateData = { ...taskUpdate };

      if (updateData.startDate) {
        updateData.startDate = typeof updateData.startDate === "string" ? new Date(updateData.startDate) : updateData.startDate;
      }

      if (updateData.endDate) {
        updateData.endDate = typeof updateData.endDate === "string" ? new Date(updateData.endDate) : updateData.endDate;
      }

      const updatedTaskData = await updateTaskInSupabase(id, updateData);

      if (updatedTaskData) {
        // Find existing task to keep subtasks
        const existingTask = initialTasks.find((task) => task.id === id);
        if (!existingTask) return null;

        // Create updated task object with existing subtasks
        const updatedTask: Task = {
          ...existingTask,
          ...updateData,
          updatedAt: new Date(),
        };

        // Update local state
        setTasks((prev) => prev.map((task) => (task.id === id ? updatedTask : task)));
        toast.success("Tarefa atualizada com sucesso");
        return updatedTask;
      }
      return null;
    } catch (error) {
      console.error("Erro ao atualizar tarefa:", error);
      toast.error("Falha ao atualizar tarefa");
      return null;
    }
  };

  // Delete a task
  const deleteTask = async (id: string): Promise<boolean> => {
    try {
      const success = await deleteTaskFromSupabase(id);

      if (success) {
        setTasks((prev) => prev.filter((task) => task.id !== id));
        toast.success("Tarefa removida com sucesso");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Erro ao excluir tarefa:", error);
      toast.error("Falha ao remover tarefa");
      return false;
    }
  };

  // Duplicate a task
  const duplicateTask = async (id: string, newDates?: { startDate?: Date; endDate?: Date }): Promise<Task | null> => {
    try {
      const taskToDuplicate = initialTasks.find((task) => task.id === id);
      if (!taskToDuplicate) {
        toast.error("Tarefa não encontrada para duplicação");
        return null;
      }

      // Create new task data with normalized dates
      const newTaskData: Omit<Task, "id" | "createdAt" | "updatedAt"> = {
        ...taskToDuplicate,
        title: `${taskToDuplicate.title} (cópia)`,
        startDate: newDates?.startDate || taskToDuplicate.startDate,
        endDate: newDates?.endDate || taskToDuplicate.endDate,
        subtasks: taskToDuplicate.subtasks
          ? taskToDuplicate.subtasks.map((st) => ({
              ...st,
              id: "", // ID será gerado pelo banco de dados
              completed: false, // Reset completed status
            }))
          : [],
      };

      // Add task to database
      const newTask = await addTask(newTaskData);
      toast.success("Tarefa duplicada com sucesso");
      return newTask;
    } catch (error) {
      console.error("Erro ao duplicar tarefa:", error);
      toast.error("Falha ao duplicar tarefa");
      return null;
    }
  };

  // Subtask operations
  const addSubtask = async (taskId: string, subtaskTitle: string): Promise<TaskSubtask | null> => {
    try {
      const newSubtask = await addSubtaskToSupabase(taskId, subtaskTitle);

      if (newSubtask) {
        // Update local state
        setTasks((prev) =>
          prev.map((task) => {
            if (task.id === taskId) {
              return {
                ...task,
                subtasks: [...(task.subtasks || []), newSubtask],
                updatedAt: new Date(),
              };
            }
            return task;
          })
        );

        toast.success("Subtarefa adicionada com sucesso");
        return newSubtask;
      }
      return null;
    } catch (error) {
      console.error("Erro ao adicionar subtarefa:", error);
      toast.error("Falha ao adicionar subtarefa");
      return null;
    }
  };

  const updateSubtask = async (taskId: string, subtaskId: string, updates: Partial<TaskSubtask>): Promise<boolean> => {
    try {
      const success = await updateSubtaskInSupabase(subtaskId, updates);

      if (success) {
        // Update local state
        setTasks((prev) =>
          prev.map((task) => {
            if (task.id === taskId && task.subtasks) {
              const updatedSubtasks = task.subtasks.map((subtask) => {
                if (subtask.id === subtaskId) {
                  return { ...subtask, ...updates };
                }
                return subtask;
              });

              return {
                ...task,
                subtasks: updatedSubtasks,
                updatedAt: new Date(),
              };
            }
            return task;
          })
        );

        // Don't show toast for simple updates like marking/unmarking
        if (updates.title) {
          toast.success("Subtarefa atualizada com sucesso");
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error("Erro ao atualizar subtarefa:", error);
      toast.error("Falha ao atualizar subtarefa");
      return false;
    }
  };

  const deleteSubtask = async (taskId: string, subtaskId: string): Promise<boolean> => {
    try {
      const success = await deleteSubtaskFromSupabase(subtaskId);

      if (success) {
        // Update local state
        setTasks((prev) =>
          prev.map((task) => {
            if (task.id === taskId && task.subtasks) {
              return {
                ...task,
                subtasks: task.subtasks.filter((st) => st.id !== subtaskId),
                updatedAt: new Date(),
              };
            }
            return task;
          })
        );

        toast.success("Subtarefa removida com sucesso");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Erro ao remover subtarefa:", error);
      toast.error("Falha ao remover subtarefa");
      return false;
    }
  };

  return {
    addTask,
    updateTask,
    deleteTask,
    duplicateTask,
    addSubtask,
    updateSubtask,
    deleteSubtask,
  };
};
