import { supabase } from "@/integrations/supabase/client";
import { TaskRow, TaskSubtaskRow, TaskAttachmentRow } from "@/types/taskTypes";
import { Task, TaskStatus, TaskDifficulty, TaskType, TaskSubtask, TaskAttachment } from "@/types";
import { toast } from "sonner";
import { isValid, parseISO } from "date-fns";

// Função auxiliar para normalizar datas vindas do Supabase
const normalizeDate = (dateStr: string | null): Date | null => {
  if (!dateStr) return null;

  try {
    const date = parseISO(dateStr);
    return isValid(date) ? date : null;
  } catch (error) {
    console.error("Erro ao converter data:", error);
    return null;
  }
};

// Função auxiliar para preparar uma data para o Supabase
const formatDateForSupabase = (date: Date | string | null | undefined): string | null => {
  if (!date) return null;

  try {
    // Se for uma string, converter para Date primeiro
    const dateObj = typeof date === "string" ? new Date(date) : date;

    // Verificar se é uma data válida
    if (!isValid(dateObj)) return null;

    // Retornar no formato ISO que o Supabase espera
    return dateObj.toISOString();
  } catch (error) {
    console.error("Erro ao formatar data para Supabase:", error);
    return null;
  }
};

// Load tasks from Supabase
export const loadTasksFromSupabase = async (workspaceId: string): Promise<Task[]> => {
  try {
    // Use type assertion to bypass TypeScript validation for Supabase
    const { data: taskData, error: taskError } = await (supabase as any).from("tasks").select("*").eq("workspace_id", workspaceId);

    if (taskError) {
      throw taskError;
    }

    // Fetch subtasks for these tasks
    let subtaskData: TaskSubtaskRow[] = [];
    let attachmentData: TaskAttachmentRow[] = [];

    if (taskData && taskData.length > 0) {
      const taskIds = taskData.map((task: TaskRow) => task.id);

      // Buscar subtarefas
      const { data: fetchedSubtasks, error: subtaskError } = await (supabase as any).from("task_subtasks").select("*").in("task_id", taskIds);

      if (subtaskError) {
        throw subtaskError;
      }

      subtaskData = fetchedSubtasks || [];

      // Buscar anexos
      const { data: fetchedAttachments, error: attachmentError } = await (supabase as any)
        .from("task_attachments")
        .select("*")
        .in("task_id", taskIds);

      if (attachmentError) {
        console.error("Erro ao buscar anexos:", attachmentError);
        // Continua mesmo com erro para não impedir o carregamento das tarefas
      } else {
        attachmentData = fetchedAttachments || [];
      }
    }

    // Convert database format to app format
    const formattedTasks: Task[] = taskData
      ? taskData.map((task: TaskRow) => {
          // Find subtasks for this task
          const taskSubtasks = subtaskData
            .filter((subtask: TaskSubtaskRow) => subtask.task_id === task.id)
            .map((subtask: TaskSubtaskRow) => ({
              id: subtask.id,
              title: subtask.title,
              completed: subtask.completed,
              createdAt: new Date(subtask.created_at),
            }));

          // Find attachments for this task
          const taskAttachments = attachmentData
            .filter((attachment: TaskAttachmentRow) => attachment.task_id === task.id)
            .map((attachment: TaskAttachmentRow) => ({
              id: attachment.id,
              taskId: attachment.task_id,
              filename: attachment.filename,
              filePath: attachment.file_path,
              fileSize: attachment.file_size || undefined,
              mimeType: attachment.mime_type || undefined,
              createdAt: new Date(attachment.created_at),
              createdBy: attachment.created_by || undefined,
            }));

          // Normalizamos os dados, especialmente as datas
          const startDate = normalizeDate(task.start_date);
          const endDate = normalizeDate(task.end_date);

          return {
            id: task.id,
            title: task.title,
            description: task.description || "",
            assigneeId: task.assignee_id,
            status: task.status as TaskStatus,
            difficulty: task.difficulty as TaskDifficulty,
            category: task.category || undefined,
            organization: task.organization || undefined,
            startDate: startDate || new Date(),
            endDate: endDate || new Date(),
            estimatedTime: task.estimated_time || "",
            createdAt: new Date(task.created_at),
            updatedAt: new Date(task.updated_at),
            parentId: task.parent_id || undefined,
            workspaceId: task.workspace_id,
            subtasks: taskSubtasks,
            attachments: taskAttachments,
            createdBy: task.created_by || undefined,
            taskType: task.task_type as TaskType || null,
          };
        })
      : [];

    return formattedTasks;
  } catch (error) {
    console.error("Error loading tasks:", error);
    toast.error("Erro ao carregar tarefas");
    return [];
  }
};

// Add task to Supabase
export const addTaskToSupabase = async (taskData: Omit<Task, "id" | "createdAt" | "updatedAt">): Promise<Task | null> => {
  try {
    // Prepare task for database insertion
    const taskForDb = {
      title: taskData.title,
      description: taskData.description,
      assignee_id: taskData.assigneeId,
      status: taskData.status,
      difficulty: taskData.difficulty,
      category: taskData.category,
      organization: taskData.organization,
      start_date: formatDateForSupabase(taskData.startDate),
      end_date: formatDateForSupabase(taskData.endDate),
      estimated_time: taskData.estimatedTime,
      parent_id: taskData.parentId,
      workspace_id: taskData.workspaceId,
      task_type: taskData.taskType,
    };

    // Insert task into database using type assertion
    const { data, error } = await (supabase as any).from("tasks").insert(taskForDb).select().single();

    if (error) throw error;

    // Process subtasks if they exist
    const subtasks: TaskSubtask[] = [];
    if (taskData.subtasks && taskData.subtasks.length > 0) {
      const subtasksForDb = taskData.subtasks.map((subtask) => ({
        task_id: data.id,
        title: subtask.title,
        completed: subtask.completed,
      }));

      const { data: subtaskData, error: subtaskError } = await (supabase as any).from("task_subtasks").insert(subtasksForDb).select();

      if (subtaskError) {
        console.error("Error adding subtasks:", subtaskError);
      } else if (subtaskData) {
        subtaskData.forEach((st: TaskSubtaskRow) => {
          subtasks.push({
            id: st.id,
            title: st.title,
            completed: st.completed,
            createdAt: new Date(st.created_at),
          });
        });
      }
    }

    // Create the task object for the app
    const newTask: Task = {
      id: data.id,
      title: data.title,
      description: data.description || "",
      assigneeId: data.assignee_id,
      status: data.status as TaskStatus,
      difficulty: data.difficulty as TaskDifficulty,
      category: data.category || undefined,
      organization: data.organization || undefined,
      startDate: normalizeDate(data.start_date) || new Date(),
      endDate: normalizeDate(data.end_date) || new Date(),
      estimatedTime: data.estimated_time || "",
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      parentId: data.parent_id || undefined,
      workspaceId: data.workspace_id,
      subtasks: subtasks,
      createdBy: data.created_by || undefined,
      taskType: data.task_type as TaskType || null,
    };

    return newTask;
  } catch (error) {
    console.error("Erro ao adicionar tarefa:", error);
    toast.error("Falha ao criar tarefa");
    return null;
  }
};

// Update task in Supabase
export const updateTaskInSupabase = async (id: string, taskUpdate: Partial<Task>): Promise<Task | null> => {
  try {
    // Prepare update data for database
    const updateData: any = {};
    if (taskUpdate.title !== undefined) updateData.title = taskUpdate.title;
    if (taskUpdate.description !== undefined) updateData.description = taskUpdate.description;
    if (taskUpdate.assigneeId !== undefined) updateData.assignee_id = taskUpdate.assigneeId;
    if (taskUpdate.status !== undefined) updateData.status = taskUpdate.status;
    if (taskUpdate.difficulty !== undefined) updateData.difficulty = taskUpdate.difficulty;
    if (taskUpdate.category !== undefined) updateData.category = taskUpdate.category;
    if (taskUpdate.organization !== undefined) updateData.organization = taskUpdate.organization;
    if (taskUpdate.startDate !== undefined) {
      updateData.start_date = formatDateForSupabase(taskUpdate.startDate);
    }
    if (taskUpdate.endDate !== undefined) {
      updateData.end_date = formatDateForSupabase(taskUpdate.endDate);
    }
    if (taskUpdate.estimatedTime !== undefined) updateData.estimated_time = taskUpdate.estimatedTime;
    if (taskUpdate.parentId !== undefined) updateData.parent_id = taskUpdate.parentId;
    if (taskUpdate.taskType !== undefined) updateData.task_type = taskUpdate.taskType;
    updateData.updated_at = new Date().toISOString();

    // Update task in database using type assertion
    const { data, error } = await (supabase as any).from("tasks").update(updateData).eq("id", id).select().single();

    if (error) throw error;

    // Normalizar as datas no objeto retornado
    if (data) {
      // Converter datas para objetos Date
      const formattedTask: Task = {
        ...data,
        startDate: normalizeDate(data.start_date) || new Date(),
        endDate: normalizeDate(data.end_date) || new Date(),
        description: data.description || "",
        category: data.category || undefined,
        organization: data.organization || undefined,
        estimatedTime: data.estimated_time || "",
        assigneeId: data.assignee_id,
        parentId: data.parent_id || undefined,
        workspaceId: data.workspace_id,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        subtasks: [], // As subtasks são gerenciadas separadamente
        createdBy: data.created_by || undefined,
        taskType: data.task_type as TaskType || null,
      };

      return formattedTask;
    }

    return null;
  } catch (error) {
    console.error("Erro ao atualizar tarefa:", error);
    toast.error("Falha ao atualizar tarefa");
    return null;
  }
};

// Delete task from Supabase
export const deleteTaskFromSupabase = async (id: string): Promise<boolean> => {
  try {
    const { error } = await (supabase as any).from("tasks").delete().eq("id", id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Erro ao excluir tarefa:", error);
    toast.error("Falha ao remover tarefa");
    return false;
  }
};

// Subtask operations in Supabase
export const addSubtaskToSupabase = async (taskId: string, subtaskTitle: string): Promise<TaskSubtask | null> => {
  try {
    const subtaskData = {
      task_id: taskId,
      title: subtaskTitle,
      completed: false,
    };

    const { data, error } = await (supabase as any).from("task_subtasks").insert(subtaskData).select().single();

    if (error) throw error;

    return {
      id: data.id,
      title: data.title,
      completed: data.completed,
      createdAt: new Date(data.created_at),
    };
  } catch (error) {
    console.error("Erro ao adicionar subtarefa:", error);
    toast.error("Falha ao adicionar subtarefa");
    return null;
  }
};

export const updateSubtaskInSupabase = async (subtaskId: string, updates: Partial<TaskSubtask>): Promise<boolean> => {
  try {
    const updateData: any = {};
    if (updates.title !== undefined) updateData.title = updates.title;
    if (updates.completed !== undefined) updateData.completed = updates.completed;

    const { error } = await (supabase as any).from("task_subtasks").update(updateData).eq("id", subtaskId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Erro ao atualizar subtarefa:", error);
    toast.error("Falha ao atualizar subtarefa");
    return false;
  }
};

export const deleteSubtaskFromSupabase = async (subtaskId: string): Promise<boolean> => {
  try {
    const { error } = await (supabase as any).from("task_subtasks").delete().eq("id", subtaskId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Erro ao remover subtarefa:", error);
    toast.error("Falha ao remover subtarefa");
    return false;
  }
};
