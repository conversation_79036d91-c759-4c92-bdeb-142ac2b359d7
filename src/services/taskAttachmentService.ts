import { supabase } from "@/integrations/supabase/client";
import { TaskAttachment } from "@/types";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

// Bucket para armazenar os anexos de tarefas
const STORAGE_BUCKET = "task-attachments";

// Garantir que o bucket existe
const ensureBucketExists = async (): Promise<boolean> => {
  try {
    // Verifica se o bucket já existe
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketExists = buckets?.some((bucket) => bucket.name === STORAGE_BUCKET);

    if (!bucketExists) {
      // Criar o bucket se não existir
      const { error } = await supabase.storage.createBucket(STORAGE_BUCKET, {
        public: false,
        fileSizeLimit: 50 * 1024 * 1024, // 50MB como limite
      });

      if (error) {
        console.error("Erro ao criar bucket:", error);
        return false;
      }
    }
    return true;
  } catch (error) {
    console.error("Erro ao verificar/criar bucket:", error);
    return false;
  }
};

// Carregar anexos de uma tarefa
export const loadTaskAttachments = async (taskId: string): Promise<TaskAttachment[]> => {
  try {
    const { data, error } = await supabase.from("task_attachments").select("*").eq("task_id", taskId);

    if (error) throw error;

    if (!data || data.length === 0) {
      return [];
    }

    // Transformar os dados do banco para o formato da aplicação
    const attachments: TaskAttachment[] = await Promise.all(
      data.map(async (attachment) => {
        // Gerar URL assinada para acesso ao arquivo
        const { data: urlData, error: urlError } = await supabase.storage.from(STORAGE_BUCKET).createSignedUrl(attachment.file_path, 60 * 60); // URL válida por 1 hora
        
        if (urlError) {
          console.error('Erro ao gerar URL assinada para anexo:', attachment.filename, urlError);
        }
        
        // Anexo carregado com URL gerada

        return {
          id: attachment.id,
          taskId: attachment.task_id,
          filename: attachment.filename,
          filePath: attachment.file_path,
          fileSize: attachment.file_size,
          mimeType: attachment.mime_type,
          createdAt: new Date(attachment.created_at),
          createdBy: attachment.created_by,
          url: urlData?.signedUrl,
        };
      })
    );

    return attachments;
  } catch (error) {
    console.error("Erro ao carregar anexos:", error);
    toast.error("Falha ao carregar anexos");
    return [];
  }
};

// Fazer upload de um arquivo para uma tarefa
export const uploadTaskAttachment = async (taskId: string, file: File, workspaceId: string): Promise<TaskAttachment | null> => {
  try {
    // Garantir que o bucket existe
    const bucketExists = await ensureBucketExists();
    if (!bucketExists) {
      throw new Error("Não foi possível acessar o armazenamento de arquivos");
    }

    // Criar um nome de arquivo único para evitar colisões
    const fileExt = file.name.split(".").pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${workspaceId}/${taskId}/${fileName}`;

    // Upload do arquivo para o Storage
    const { error: uploadError } = await supabase.storage.from(STORAGE_BUCKET).upload(filePath, file, {
      cacheControl: "3600",
      upsert: false,
    });

    if (uploadError) throw uploadError;

    // Registrar o anexo no banco de dados
    const { data: attachment, error: dbError } = await supabase
      .from("task_attachments")
      .insert({
        task_id: taskId,
        filename: file.name,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        created_by: (await supabase.auth.getUser()).data.user?.id,
      })
      .select()
      .single();

    if (dbError) throw dbError;

    // Gerar URL assinada para acesso ao arquivo
    const { data: urlData, error: urlError } = await supabase.storage.from(STORAGE_BUCKET).createSignedUrl(filePath, 60 * 60); // URL válida por 1 hora
    
    if (urlError) {
      console.error('Erro ao gerar URL assinada para upload:', attachment.filename, urlError);
    }
    
    // Upload concluído com URL gerada

    // Retornar o anexo criado
    return {
      id: attachment.id,
      taskId: attachment.task_id,
      filename: attachment.filename,
      filePath: attachment.file_path,
      fileSize: attachment.file_size,
      mimeType: attachment.mime_type,
      createdAt: new Date(attachment.created_at),
      createdBy: attachment.created_by,
      url: urlData?.signedUrl,
    };
  } catch (error) {
    console.error("Erro ao fazer upload de anexo:", error);
    toast.error("Falha ao fazer upload do arquivo");
    return null;
  }
};

// Excluir um anexo
export const deleteTaskAttachment = async (attachment: TaskAttachment): Promise<boolean> => {
  try {
    // Excluir o registro do banco de dados
    const { error: dbError } = await supabase.from("task_attachments").delete().eq("id", attachment.id);

    if (dbError) throw dbError;

    // Excluir o arquivo do Storage
    const { error: storageError } = await supabase.storage.from(STORAGE_BUCKET).remove([attachment.filePath]);

    if (storageError) {
      console.error("Erro ao excluir arquivo do storage:", storageError);
      // Continua mesmo com erro, pois o registro já foi removido do banco
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir anexo:", error);
    toast.error("Falha ao excluir anexo");
    return false;
  }
};

// Gerar URL para visualização do arquivo
export const getAttachmentUrl = async (filePath: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase.storage.from(STORAGE_BUCKET).createSignedUrl(filePath, 60 * 60); // URL válida por 1 hora

    if (error) throw error;
    return data?.signedUrl || null;
  } catch (error) {
    console.error("Erro ao gerar URL do anexo:", error);
    return null;
  }
};
