import { NextApiRequest, NextApiResponse } from 'next';
import Strip<PERSON> from 'stripe';

// Inicializar <PERSON>e com a chave secreta
const stripeSecretKey = process.env.STRIPE_SECRET_KEY || "sk_test_51PkVaQIxqQbmbZWPCBIzL2xCnseBLTpFDqJXi69c42nldk1qlO7iJZ2LNYo4uBSbc7cAjTnT5WVceJ0Q9tyzXP2r00trRZtDUb";
const stripe = new Stripe(stripeSecretKey, { apiVersion: "2023-10-16" as any });

// IDs dos preços no Stripe
const PRICE_IDS = {
  individual: "price_1PHxJ4ALvhA5CrMQMKv4Z1F0",
  shared: "price_1PHxJTALvhA5CrMQfULx4xRQ",
  business: "price_1PHxJkALvhA5CrMQBPe66nKB",
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { planId, email, userId } = req.body;

    if (!planId || !email || !userId) {
      return res.status(400).json({ error: 'planId, email e userId são obrigatórios' });
    }

    // Verificar se o plano existe
    const priceId = PRICE_IDS[planId as keyof typeof PRICE_IDS];
    if (!priceId) {
      return res.status(400).json({ error: 'Plano inválido' });
    }

    // Buscar customer
    const customerList = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (customerList.data.length === 0) {
      return res.status(400).json({ error: 'Customer não encontrado' });
    }

    const customerId = customerList.data[0].id;

    // Buscar métodos de pagamento do customer
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });

    if (paymentMethods.data.length === 0) {
      return res.status(400).json({ error: 'Nenhum método de pagamento encontrado' });
    }

    // Usar o método de pagamento mais recente
    const paymentMethodId = paymentMethods.data[0].id;

    // Verificar se já existe uma assinatura ativa
    const existingSubscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'active',
      limit: 1,
    });

    // Se já tem assinatura ativa, cancelar primeiro
    if (existingSubscriptions.data.length > 0) {
      await stripe.subscriptions.cancel(existingSubscriptions.data[0].id);
    }

    // Criar nova assinatura
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [
        {
          price: priceId,
        },
      ],
      default_payment_method: paymentMethodId,
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        plan_id: planId,
        user_id: userId,
      },
    });

    return res.status(200).json({
      subscriptionId: subscription.id,
      status: subscription.status,
      clientSecret: subscription.latest_invoice?.payment_intent?.client_secret,
    });
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    return res.status(500).json({ error: 'Erro ao criar assinatura' });
  }
}
