import { NextApiRequest, NextApiResponse } from "next";
import Strip<PERSON> from "stripe";

const stripe = new Stripe(
  process.env.STRIPE_SECRET_KEY || "sk_test_51PkVaQIxqQbmbZWPCBIzL2xCnseBLTpFDqJXi69c42nldk1qlO7iJZ2LNYo4uBSbc7cAjTnT5WVceJ0Q9tyzXP2r00trRZtDUb",
  {
    apiVersion: "2023-10-16",
  }
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Método não permitido" });
  }

  try {
    const { amount, planId, userId, email } = req.body;

    if (!amount || !planId || !userId || !email) {
      return res.status(400).json({ error: "Parâmetros obrigatórios ausentes" });
    }

    // Criar um Customer ou usar um existente
    let customerId;
    const customerList = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (customerList.data.length > 0) {
      customerId = customerList.data[0].id;
    } else {
      const customer = await stripe.customers.create({
        email,
        metadata: {
          user_id: userId,
        },
      });
      customerId = customer.id;
    }

    // Criar PaymentIntent com o valor correto
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: "brl",
      customer: customerId,
      setup_future_usage: "off_session",
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        planId,
        userId,
        email,
      },
    });

    return res.status(200).json({
      clientSecret: paymentIntent.client_secret,
    });
  } catch (error) {
    console.error("Erro ao criar intenção de pagamento:", error);
    return res.status(500).json({ error: "Erro ao criar intenção de pagamento" });
  }
}
