import { NextApiRequest, NextApiResponse } from 'next';
import <PERSON><PERSON> from 'stripe';

// Inicializar <PERSON>e com a chave secreta
const stripeSecretKey = process.env.STRIPE_SECRET_KEY || "sk_test_51PkVaQIxqQbmbZWPCBIzL2xCnseBLTpFDqJXi69c42nldk1qlO7iJZ2LNYo4uBSbc7cAjTnT5WVceJ0Q9tyzXP2r00trRZtDUb";
const stripe = new Stripe(stripeSecretKey, { apiVersion: "2023-10-16" as any });

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { planId, email } = req.body;

    if (!planId || !email) {
      return res.status(400).json({ error: 'planId e email são obrigatórios' });
    }

    // Criar ou buscar customer
    let customerId;
    const customerList = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (customerList.data.length > 0) {
      customerId = customerList.data[0].id;
    } else {
      const customer = await stripe.customers.create({
        email,
        metadata: {
          plan_id: planId,
        },
      });
      customerId = customer.id;
    }

    // Criar Setup Intent para capturar método de pagamento
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
      usage: 'off_session',
      metadata: {
        plan_id: planId,
        email,
      },
    });

    return res.status(200).json({
      clientSecret: setupIntent.client_secret,
      customerId,
    });
  } catch (error) {
    console.error('Erro ao criar setup intent:', error);
    return res.status(500).json({ error: 'Erro ao criar setup intent' });
  }
}
