import React, { useState, useEffect } from "react";
import { Sidebar } from "../components/Layout/Sidebar";
import { PageHeader } from "../components/Layout/PageHeader";
import { PageEditor } from "../components/Editor/PageEditor";
import { WorkspaceSettings } from "../components/Workspace/WorkspaceSettings";
import { SubscriptionPlans } from "../components/Subscription/SubscriptionPlans";
import { Settings, CreditCard, LayoutDashboard, Kanban, AlertTriangle } from "lucide-react";
import { Button } from "../components/ui/button";
import { useAuth } from "../contexts/AuthContext";
import { useSubscription } from "../contexts/SubscriptionProvider";
import { useWorkspace } from "../contexts/WorkspaceContext";
import { Card } from "../components/ui/card";
import { useLocation } from "react-router-dom";
import { SubscriptionModal } from "../components/Subscription/SubscriptionModal";
import Dashboard from "./Dashboard";
import KanbanBoard from "./Kanban";
import { useLastPageVisited } from "../hooks/useLastPageVisited";
import { UserStatusAvatar } from "../components/Layout/UserStatusAvatar";
import { toast } from "sonner";

// Define os tipos de conteúdo que podem ser exibidos
type ContentType = "page";

export const WorkspacePage = () => {
  const location = useLocation();
  const { logout, user } = useAuth();
  const { currentWorkspace, workspaces } = useWorkspace();
  const { hasReachedLimit, isAdmin, isSubscribed, trialEndDate } = useSubscription();
  const [showSettings, setShowSettings] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [contentType, setContentType] = useState<ContentType>("page");
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Force dark mode for the application
  useEffect(() => {
    document.documentElement.classList.add("dark");
    return () => {
      document.documentElement.classList.remove("dark");
    };
  }, []);

  // Save last visited content type with our custom hook
  // @ts-expect-error - Corrigir tipo incompatível entre Workspace e Page
  useLastPageVisited(currentWorkspace, () => {}, [], contentType, setContentType);

  // Atualizar a hora da última atualização quando a página mudar
  useEffect(() => {
    setLastUpdated(new Date());
  }, [currentWorkspace, contentType]);

  // Ouvir evento para abrir o modal de assinatura
  useEffect(() => {
    const handleOpenSubscriptionModal = () => {
      setShowSubscriptionModal(true);
    };

    window.addEventListener("open-subscription-modal", handleOpenSubscriptionModal);

    return () => {
      window.removeEventListener("open-subscription-modal", handleOpenSubscriptionModal);
    };
  }, []);

  // Ouvir evento para verificar propriedade do workspace
  useEffect(() => {
    const handleCheckWorkspaceOwnership = (event: CustomEvent) => {
      if (event.detail && typeof event.detail.callback === "function") {
        const isOwner = isWorkspaceOwner();
        event.detail.callback(isOwner);
      }
    };

    window.addEventListener("check-workspace-ownership", handleCheckWorkspaceOwnership as EventListener);

    return () => {
      window.removeEventListener("check-workspace-ownership", handleCheckWorkspaceOwnership as EventListener);
    };
  }, [currentWorkspace, user]);

  // Quando houver uma mudança de contentType, atualize o tipo de conteúdo salvo
  useEffect(() => {
    // O último tipo de conteúdo acessado está sendo salvo corretamente
    // pelo hook useLastPageVisited
    console.log("Tipo de conteúdo alterado para:", contentType);
  }, [contentType]);

  // Verificar se o usuário é membro convidado do workspace atual
  const isInvitedMember = () => {
    if (!currentWorkspace || !user) return false;

    // Se é um workspace próprio, retorna false
    if (currentWorkspace.ownerId === user.id) return false;

    // Se for um workspace onde o usuário é membro convidado, retorna true
    return workspaces.some((ws) => ws.id === currentWorkspace.id && ws.ownerId !== user.id);
  };

  // Verificar se o usuário é o proprietário do workspace atual
  const isWorkspaceOwner = () => {
    if (!currentWorkspace || !user) return false;
    return currentWorkspace.ownerId === user.id;
  };

  // Mostrar banner de limitação apenas se o usuário for o proprietário do workspace
  // e não tiver assinatura ativa
  const shouldShowLimitBanner = hasReachedLimit && !isSubscribed && !isAdmin && isWorkspaceOwner();

  // Mostrar página de upgrade apenas se não tiver nenhum workspace para exibir
  // OU se estiver em um workspace próprio e não tiver assinatura ativa
  if (hasReachedLimit && !isSubscribed && !isAdmin && (!currentWorkspace || (currentWorkspace && isWorkspaceOwner()))) {
    // Verificar se existe algum workspace onde o usuário é convidado
    const hasInvitedWorkspaces = workspaces.some((ws) => ws.ownerId !== user?.id);

    // Se estiver em um workspace onde é convidado, não mostrar o banner de período expirado
    if (currentWorkspace && !isWorkspaceOwner()) {
      return (
        <div className="flex h-screen overflow-hidden bg-notion-page text-notion-text">
          <Sidebar onSelectContentType={setContentType} />

          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex items-center justify-between px-4 py-2 border-b border-notion-border">
              <div className="flex items-center space-x-2">
                {lastUpdated && (
                  <span className="text-xs text-notion-muted">
                    Última atualização: {lastUpdated.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit", second: "2-digit" })}
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <UserStatusAvatar />
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-notion-muted hover:text-notion-text hover:bg-notion-hover flex items-center gap-2"
                  onClick={() => setShowSubscriptionModal(true)}
                >
                  <CreditCard className="h-4 w-4" />
                  Assinatura
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-notion-muted hover:text-notion-text hover:bg-notion-hover"
                  onClick={() => setShowSettings(true)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-notion-muted hover:text-notion-text hover:bg-notion-hover" onClick={logout}>
                  Sair
                </Button>
              </div>
            </div>

            {contentType === "page" && <PageHeader hasLimits={shouldShowLimitBanner} onActionWithCheck={handleActionWithLimitCheck} />}

            <div className="flex-1 overflow-auto">{renderContent()}</div>
          </div>

          <WorkspaceSettings isOpen={showSettings} onClose={() => setShowSettings(false)} />

          <SubscriptionModal isOpen={showSubscriptionModal} onClose={() => setShowSubscriptionModal(false)} />
        </div>
      );
    }

    // Se existir workspaces onde o usuário é convidado, permitir acesso a eles
    if (hasInvitedWorkspaces && !currentWorkspace) {
      return (
        <div className="min-h-screen bg-notion-page p-8 flex">
          {/* Menu lateral com convites */}
          <div className="flex flex-col w-64 min-w-[240px] max-w-[480px]">
            <Sidebar onSelectContentType={setContentType} />
          </div>

          <div className="flex-1">
            <Card className="max-w-2xl mx-auto p-6 mb-8 mt-8">
              <h1 className="text-2xl font-bold mb-4">Seu período de teste acabou</h1>
              <p className="text-notion-muted mb-6">
                Você ainda pode acessar workspaces onde foi convidado. Para criar novos workspaces, escolha um plano abaixo.
              </p>
              <div className="flex justify-center">
                <Button className="bg-primary text-white hover:bg-primary/90" onClick={() => setShowSubscriptionModal(true)}>
                  Ver planos
                </Button>
              </div>
            </Card>
          </div>
        </div>
      );
    }
  }

  // Usar um manipulador para disparar o evento de adicionar tarefa
  const handleCreateTask = () => {
    const event = new CustomEvent("add-task");
    window.dispatchEvent(event);
  };

  // Mostrar aviso de limitação ao tentar adicionar conteúdo em workspace próprio sem assinatura
  const handleActionWithLimitCheck = (action: () => void) => {
    if (shouldShowLimitBanner) {
      toast.error("Você atingiu o limite de uso gratuito. Faça upgrade do seu plano para continuar.", {
        action: {
          label: "Fazer upgrade",
          onClick: () => setShowSubscriptionModal(true),
        },
      });
    } else {
      action();
    }
  };

  // Renderiza o conteúdo correto com base no tipo selecionado
  const renderContent = () => {
    switch (contentType) {
      case "page":
        return currentWorkspace ? <PageEditor /> : <div>No page selected</div>;
      default:
        return <PageEditor />;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-notion-page text-notion-text">
      <Sidebar onSelectContentType={setContentType} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex items-center justify-between px-4 py-2 border-b border-notion-border">
          <div className="flex items-center space-x-2">
            {lastUpdated && (
              <span className="text-xs text-notion-muted">
                Última atualização: {lastUpdated.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit", second: "2-digit" })}
              </span>
            )}
          </div>

          {/* Banner de limitação para workspaces próprios */}
          {shouldShowLimitBanner && (
            <div className="flex-1 mx-4">
              <div className="bg-amber-500/10 border border-amber-500/30 text-amber-700 text-xs px-3 py-1 rounded-md flex items-center">
                <AlertTriangle className="h-3 w-3 mr-2" />
                <span>Você atingiu o limite de uso gratuito neste workspace.</span>
                <Button variant="link" size="sm" className="text-amber-800 p-0 ml-2 h-auto" onClick={() => setShowSubscriptionModal(true)}>
                  Fazer upgrade
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <UserStatusAvatar />
            <Button
              variant="ghost"
              size="sm"
              className="text-notion-muted hover:text-notion-text hover:bg-notion-hover flex items-center gap-2"
              onClick={() => setShowSubscriptionModal(true)}
            >
              <CreditCard className="h-4 w-4" />
              Assinatura
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-notion-muted hover:text-notion-text hover:bg-notion-hover"
              onClick={() => setShowSettings(true)}
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="text-notion-muted hover:text-notion-text hover:bg-notion-hover" onClick={logout}>
              Sair
            </Button>
          </div>
        </div>

        {contentType === "page" && <PageHeader hasLimits={shouldShowLimitBanner} onActionWithCheck={handleActionWithLimitCheck} />}

        <div className="flex-1 overflow-auto">{renderContent()}</div>
      </div>

      <WorkspaceSettings isOpen={showSettings} onClose={() => setShowSettings(false)} />

      <SubscriptionModal isOpen={showSubscriptionModal} onClose={() => setShowSubscriptionModal(false)} />
    </div>
  );
};

export default WorkspacePage;
