
import React, { useEffect } from "react";
import { KanbanBoard } from "@/components/Kanban/KanbanBoard";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { TasksProvider, useTasks } from "@/contexts/TasksContext";
import { v4 as uuidv4 } from "uuid";
import { TaskSubtask } from "@/types";

interface KanbanProps {
  onViewDashboard: () => void;
}

// Sample data loader component to demonstrate functionality
const TasksInitializer: React.FC = () => {
  const { tasks, addTask } = useTasks();
  const { currentWorkspace } = useWorkspace();
  
  useEffect(() => {
    const loadSampleData = async () => {
      // Only load sample data if no tasks exist and we have a workspace
      if (tasks.length === 0 && currentWorkspace) {
        const today = new Date();
        const tomorrow = new Date();
        tomorrow.setDate(today.getDate() + 1);
        
        const nextWeek = new Date();
        nextWeek.setDate(today.getDate() + 7);
        
        // Create some sample tasks
        await addTask({
          title: "Implementar autenticação",
          description: "Adicionar login e registro de usuários usando Supabase Auth",
          assigneeId: null,
          status: "aguardando",
          difficulty: "medio",
          category: "Desenvolvimento",
          organization: "Backend",
          startDate: today,
          endDate: tomorrow,
          estimatedTime: "4h",
          workspaceId: currentWorkspace.id,
          subtasks: [
            {
              id: uuidv4(),
              title: "Configurar rotas de autenticação",
              completed: false,
              createdAt: new Date()
            },
            {
              id: uuidv4(),
              title: "Criar formulário de login",
              completed: true,
              createdAt: new Date()
            }
          ]
        });
        
        await addTask({
          title: "Design da página inicial",
          description: "Criar wireframes e designs para a landing page",
          assigneeId: null,
          status: "em_progresso",
          difficulty: "facil",
          category: "Design",
          organization: "Frontend",
          startDate: today,
          endDate: nextWeek,
          estimatedTime: "8h",
          workspaceId: currentWorkspace.id
        });
        
        await addTask({
          title: "Configurar CI/CD",
          description: "Configurar pipeline de integração contínua e entrega",
          assigneeId: null,
          status: "concluido",
          difficulty: "dificil",
          category: "DevOps",
          organization: "Infraestrutura",
          startDate: new Date(today.getTime() - 86400000 * 3), // 3 days ago
          endDate: today,
          estimatedTime: "12h",
          workspaceId: currentWorkspace.id
        });
      }
    };
    
    loadSampleData();
  }, [tasks.length, currentWorkspace]);
  
  return null;
};

const Kanban = ({ onViewDashboard }: KanbanProps) => {
  const { currentWorkspace } = useWorkspace();
  
  if (!currentWorkspace) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-notion-muted">Selecione um workspace para visualizar o quadro Kanban</p>
      </div>
    );
  }
  
  return (
    <TasksProvider>
      <TasksInitializer />
      <div className="h-full flex flex-col bg-notion-page text-notion-text">
        <div className="flex-1 overflow-hidden">
          <KanbanBoard />
        </div>
      </div>
    </TasksProvider>
  );
};

export default Kanban;
