import React, { useState, useEffect } from "react";
import { WorkspaceMetrics } from "@/components/Dashboard/WorkspaceMetrics";
import { MembersOverview } from "@/components/Dashboard/MembersOverview";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Profile, InvitationData, WorkspaceMember } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";

interface DashboardProps {
  onViewKanban: () => void;
}

const Dashboard = ({ onViewKanban }: DashboardProps) => {
  const { currentWorkspace, workspaces, setCurrentWorkspace, workspaceMembers, updateMemberRole, removeMember } = useWorkspace();
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>("member");
  const [pendingInvitations, setPendingInvitations] = useState<InvitationData[]>([]);
  const [selectedInvitationId, setSelectedInvitationId] = useState<string | null>(null);
  const [selectedInvitationRole, setSelectedInvitationRole] = useState<string>("member");
  const [isManagingInvitation, setIsManagingInvitation] = useState(false);

  useEffect(() => {
    if (!currentWorkspace && workspaces && workspaces.length > 0) {
      setCurrentWorkspace(workspaces[0]);
    }
  }, [currentWorkspace, workspaces, setCurrentWorkspace]);

  useEffect(() => {
    if (currentWorkspace && isPermissionsModalOpen) {
      fetchPendingInvitations();
    }
  }, [currentWorkspace, isPermissionsModalOpen]);

  const fetchPendingInvitations = async () => {
    if (!currentWorkspace) return;

    try {
      const { data, error } = await supabase.from("workspace_invitations").select("*").eq("workspace_id", currentWorkspace.id).eq("accepted", false);

      if (error) {
        console.error("Error fetching pending invitations:", error);
        return;
      }

      if (data) {
        // Create proper InvitationData objects with typed roles
        const invitations = data.map((item) => {
          const role = item.role as WorkspaceMember["role"]; // Type assertion to ensure compatibility
          return {
            id: item.id,
            email: item.email,
            role: role,
            created_at: item.created_at,
            expires_at: item.expires_at || "",
            accepted: item.accepted || false,
            token: item.token || "",
            workspace_id: item.workspace_id,
            invited_by: item.invited_by || "",
          } as InvitationData;
        });
        setPendingInvitations(invitations);
      }
    } catch (err) {
      console.error("Error fetching pending invitations:", err);
    }
  };

  const handleUpdatePermission = async () => {
    if (!isManagingInvitation && selectedMemberId && currentWorkspace) {
      await updateMemberRole(currentWorkspace.id, selectedMemberId, selectedRole as "owner" | "admin" | "member" | "viewer");
      setIsPermissionsModalOpen(false);
    } else if (isManagingInvitation && selectedInvitationId && currentWorkspace) {
      await updateInvitationRole(selectedInvitationId, selectedInvitationRole as "admin" | "member" | "viewer");
      setIsPermissionsModalOpen(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!isManagingInvitation && selectedMemberId && currentWorkspace) {
      await removeMember(currentWorkspace.id, selectedMemberId);
      setIsPermissionsModalOpen(false);
    } else if (isManagingInvitation && selectedInvitationId) {
      await cancelInvitation(selectedInvitationId);
      setIsPermissionsModalOpen(false);
    }
  };

  const updateInvitationRole = async (invitationId: string, role: string) => {
    try {
      const { error } = await supabase.from("workspace_invitations").update({ role }).eq("id", invitationId);

      if (error) throw error;

      toast.success("Permissão do convite atualizada com sucesso!");
      fetchPendingInvitations();
    } catch (err) {
      console.error("Error updating invitation role:", err);
      toast.error("Erro ao atualizar permissão do convite");
    }
  };

  const cancelInvitation = async (invitationId: string) => {
    try {
      const { error } = await supabase.from("workspace_invitations").delete().eq("id", invitationId);

      if (error) throw error;

      toast.success("Convite cancelado com sucesso!");
      fetchPendingInvitations();
    } catch (err) {
      console.error("Error canceling invitation:", err);
      toast.error("Erro ao cancelar convite");
    }
  };

  const openPermissionsModal = (member: Profile) => {
    setSelectedMemberId(member.id);
    setSelectedRole(member.role || "member");
    setIsManagingInvitation(false);
    setIsPermissionsModalOpen(true);
  };

  const openInvitationPermissionsModal = (invitation: InvitationData) => {
    setSelectedInvitationId(invitation.id);
    setSelectedInvitationRole(invitation.role || "member");
    setIsManagingInvitation(true);
    setIsPermissionsModalOpen(true);
  };

  return (
    <div className="h-full flex flex-col bg-notion-page text-notion-text">
      {currentWorkspace ? (
        <ScrollArea className="flex-1 p-6 w-full">
          <div className="w-[95%] mx-auto space-y-6">
            <Card className="bg-notion-sidebar border-notion-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-2xl font-bold text-notion-text">{currentWorkspace.name || "Workspace sem nome"}</CardTitle>
              </CardHeader>
              <CardContent className="text-sm text-notion-muted">
                {currentWorkspace.id && <p>ID: {currentWorkspace.id}</p>}
                <p>Criado em: {new Date(currentWorkspace.createdAt).toLocaleDateString("pt-BR")}</p>
              </CardContent>
            </Card>

            <h2 className="text-xl font-semibold text-notion-text mt-6 mb-4">Métricas do Workspace</h2>
            <WorkspaceMetrics />

            <Tabs defaultValue="members" className="mt-8">
              <TabsList className="bg-notion-sidebar border border-notion-border">
                <TabsTrigger value="members" className="data-[state=active]:bg-notion-hover">
                  Membros
                </TabsTrigger>
                <TabsTrigger value="stats" className="data-[state=active]:bg-notion-hover">
                  Estatísticas
                </TabsTrigger>
                <TabsTrigger value="settings" className="data-[state=active]:bg-notion-hover">
                  Configurações
                </TabsTrigger>
              </TabsList>

              <TabsContent value="members" className="mt-4">
                <MembersOverview onManagePermissions={openPermissionsModal} onManageInvitation={openInvitationPermissionsModal} />
              </TabsContent>

              <TabsContent value="stats" className="mt-4">
                <Card className="bg-notion-sidebar border-notion-border">
                  <CardHeader>
                    <CardTitle className="text-notion-text">Estatísticas do Workspace</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium text-notion-text mb-2">Atividades recentes</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-notion-muted">Páginas criadas (últimos 7 dias)</span>
                            <span className="text-notion-text font-medium">5</span>
                          </div>
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-notion-muted">Edições (últimos 7 dias)</span>
                            <span className="text-notion-text font-medium">23</span>
                          </div>
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-notion-muted">Comentários (últimos 7 dias)</span>
                            <span className="text-notion-text font-medium">7</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-notion-text mb-2">Uso de armazenamento</h3>
                        <div className="bg-notion-page h-2 rounded-full overflow-hidden">
                          <div
                            className="bg-purple-600 h-full"
                            style={{
                              width: "35%",
                            }}
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs mt-1">
                          <span className="text-notion-muted">1.2 GB usados</span>
                          <span className="text-notion-muted">5 GB total</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="settings" className="mt-4">
                <Card className="bg-notion-sidebar border-notion-border">
                  <CardHeader>
                    <CardTitle className="text-notion-text">Configurações do Workspace</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <h3 className="font-medium text-notion-text">Permissões</h3>
                        <p className="text-notion-muted text-sm">Configure o acesso de membros e convites para este workspace.</p>
                        <Button
                          variant="outline"
                          className="border-notion-border text-notion-text mt-2"
                          onClick={() => setIsPermissionsModalOpen(true)}
                        >
                          Gerenciar permissões
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <Card className="max-w-md w-full bg-notion-sidebar border-notion-border">
            <CardHeader>
              <CardTitle className="text-notion-text">Nenhum workspace selecionado</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-notion-muted mb-4">Selecione um workspace existente ou crie um novo para visualizar o dashboard.</p>
              <Button className="w-full bg-purple-600 hover:bg-purple-700">Criar novo workspace</Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Modal de Permissões */}
      <Dialog open={isPermissionsModalOpen} onOpenChange={setIsPermissionsModalOpen}>
        <DialogContent className="bg-notion-sidebar border-notion-border text-notion-text">
          <DialogHeader>
            <DialogTitle>Gerenciar Permissões</DialogTitle>
            <DialogDescription className="text-notion-muted">
              {isManagingInvitation ? "Altere as permissões do convite pendente." : "Altere as permissões dos membros deste workspace."}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {!isManagingInvitation ? (
              <div className="space-y-2">
                <h4 className="font-medium text-notion-text">Selecione um membro</h4>
                {workspaceMembers && workspaceMembers.length > 0 ? (
                  <Select
                    value={selectedMemberId || ""}
                    onValueChange={(value) => {
                      setSelectedMemberId(value);
                      const member = workspaceMembers.find((m) => m.id === value);
                      if (member && member.role) {
                        setSelectedRole(member.role);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full bg-notion-page border-notion-border">
                      <SelectValue placeholder="Selecione um membro" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-page border-notion-border">
                      {workspaceMembers.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.name} ({member.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-notion-muted text-sm">Nenhum membro encontrado.</p>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <h4 className="font-medium text-notion-text">Convite selecionado</h4>
                {pendingInvitations && pendingInvitations.length > 0 ? (
                  <Select
                    value={selectedInvitationId || "unassigned"}
                    onValueChange={(value) => {
                      setSelectedInvitationId(value === "unassigned" ? null : value);
                      if (value !== "unassigned") {
                        const invitation = pendingInvitations.find((inv) => inv.id === value);
                        if (invitation && invitation.role) {
                          setSelectedInvitationRole(invitation.role);
                        }
                      }
                    }}
                  >
                    <SelectTrigger className="w-full bg-notion-page border-notion-border">
                      <SelectValue placeholder="Selecione um convite" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-page border-notion-border">
                      <SelectItem value="unassigned">Selecione um convite</SelectItem>
                      {pendingInvitations.map((invitation) => (
                        <SelectItem key={invitation.id} value={invitation.id}>
                          {invitation.email} (Pendente)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-notion-muted text-sm">Nenhum convite pendente encontrado.</p>
                )}
              </div>
            )}

            {((selectedMemberId && !isManagingInvitation) || (selectedInvitationId && isManagingInvitation)) && (
              <div className="space-y-2">
                <h4 className="font-medium text-notion-text">Permissão</h4>
                <Select
                  value={isManagingInvitation ? selectedInvitationRole : selectedRole}
                  onValueChange={isManagingInvitation ? setSelectedInvitationRole : setSelectedRole}
                >
                  <SelectTrigger className="w-full bg-notion-page border-notion-border">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-notion-page border-notion-border">
                    {!isManagingInvitation && <SelectItem value="owner">Proprietário</SelectItem>}
                    <SelectItem value="admin">Administrador</SelectItem>
                    <SelectItem value="member">Membro</SelectItem>
                    <SelectItem value="viewer">Visualizador</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-between">
            <Button
              variant="destructive"
              onClick={handleRemoveMember}
              disabled={(!selectedMemberId && !isManagingInvitation) || (!selectedInvitationId && isManagingInvitation)}
            >
              {isManagingInvitation ? "Cancelar convite" : "Remover membro"}
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" className="bg-notion-page" onClick={() => setIsPermissionsModalOpen(false)}>
                Cancelar
              </Button>
              <Button
                onClick={handleUpdatePermission}
                disabled={(!selectedMemberId && !isManagingInvitation) || (!selectedInvitationId && isManagingInvitation)}
              >
                Salvar alterações
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Dashboard;
