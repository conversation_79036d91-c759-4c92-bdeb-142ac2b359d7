import React, { useState } from "react";
import { LoginForm } from "../components/Auth/LoginForm";
import { RegisterForm } from "../components/Auth/RegisterForm";
export const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true);
  return <div className="flex min-h-screen bg-notion-page items-center justify-center p-4">
      <div className="w-full max-w-md flex flex-col items-center">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-2 text-notion-text">E-manager</h1>
          <p className="text-notion-muted">Seu espaço de trabalho tudo-em-um</p>
        </div>

        {isLogin ? <LoginForm onSwitchToRegister={() => setIsLogin(false)} /> : <RegisterForm onSwitchToLogin={() => setIsLogin(true)} />}
      </div>
    </div>;
};
export default AuthPage;