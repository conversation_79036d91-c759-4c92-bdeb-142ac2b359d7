
import React from "react";
import { useAuth } from "../contexts/AuthContext";
import AuthPage from "./AuthPage";
import WorkspacePage from "./WorkspacePage";
import { toast } from "sonner";

const Index = () => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-notion-page flex items-center justify-center">
        <div className="animate-pulse text-notion-text">Carregando...</div>
      </div>
    );
  }

  return user ? <WorkspacePage /> : <AuthPage />;
};

export default Index;
