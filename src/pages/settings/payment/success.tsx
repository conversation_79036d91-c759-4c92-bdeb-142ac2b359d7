import React, { useEffect, useState } from "react";
import { CheckCircle2, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useSubscription } from "@/contexts/SubscriptionProvider";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export default function PaymentSuccessPage() {
  const navigate = useNavigate();
  const { checkSubscription } = useSubscription();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function handlePaymentSuccess() {
      try {
        setLoading(true);
        // Obter parâmetros da URL
        const params = new URLSearchParams(window.location.search);
        const paymentIntent = params.get("payment_intent");
        const redirectStatus = params.get("redirect_status");

        if (redirectStatus !== "succeeded" || !paymentIntent) {
          toast.error("Não foi possível confirmar o pagamento.");
          navigate("/settings");
          return;
        }

        // Obter a sessão atual
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session) {
          toast.error("Você precisa estar logado para finalizar o processo.");
          navigate("/login");
          return;
        }

        // Verificar se o usuário já tem um registro na tabela subscribers
        const { data: subscriber } = await supabase.from("subscribers").select("*").eq("user_id", session.user.id).maybeSingle();

        // Se já tiver um registro, atualiza; caso contrário, cria um novo
        if (subscriber) {
          await supabase
            .from("subscribers")
            .update({
              subscribed: true,
              subscription_tier: subscriber.subscription_tier || "individual", // Default para individual se não tiver um plano definido
              stripe_payment_id: paymentIntent,
              updated_at: new Date().toISOString(),
            })
            .eq("user_id", session.user.id);
        } else {
          await supabase.from("subscribers").insert({
            user_id: session.user.id,
            email: session.user.email,
            subscribed: true,
            subscription_tier: "individual", // Default para individual
            stripe_payment_id: paymentIntent,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        }

        // Atualizar o contexto de assinatura
        await checkSubscription();
        toast.success("Assinatura ativada com sucesso!");
      } catch (error) {
        console.error("Erro ao processar confirmação de pagamento:", error);
        toast.error("Ocorreu um erro ao confirmar sua assinatura. Entre em contato com o suporte.");
      } finally {
        setLoading(false);
      }
    }

    handlePaymentSuccess();
  }, [navigate, checkSubscription]);

  return (
    <div className="container mx-auto py-16 max-w-md">
      <Card className="text-center">
        <CardHeader>
          <div className="flex justify-center my-4">
            {loading ? <Loader2 className="h-16 w-16 text-primary animate-spin" /> : <CheckCircle2 className="h-16 w-16 text-green-500" />}
          </div>
          <CardTitle className="text-2xl">{loading ? "Confirmando pagamento..." : "Pagamento Confirmado!"}</CardTitle>
          <CardDescription>
            {loading ? "Estamos processando seu pagamento, aguarde um momento." : "Sua assinatura foi ativada com sucesso"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-6 text-muted-foreground">
            {loading
              ? "Este processo pode levar alguns instantes."
              : "Obrigado por se tornar um assinante. Agora você tem acesso a todos os recursos da sua assinatura."}
          </p>
          <Button className="w-full" onClick={() => navigate("/")} disabled={loading}>
            {loading ? "Aguarde..." : "Voltar para o Dashboard"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
