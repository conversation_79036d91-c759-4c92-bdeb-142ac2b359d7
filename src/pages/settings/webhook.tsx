import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Copy, AlertTriangle, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function StripeWebhookPage() {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success("Copiado para a área de transferência");
    });
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Configuração do Stripe</h1>

      <Alert className="mb-6 bg-green-50 border-green-200">
        <CheckCircle className="h-4 w-4 text-green-500" />
        <AlertTitle>Integração com Stripe implementada!</AlertTitle>
        <AlertDescription>Agora você pode aceitar pagamentos usando o Stripe Elements para uma experiência mais fluida.</AlertDescription>
      </Alert>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Implementação Atual</CardTitle>
          <CardDescription>Como o sistema de pagamentos funciona agora</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>Sua aplicação agora tem suporte para processamento de pagamentos usando:</p>

            <ul className="list-disc pl-5 space-y-2">
              <li>
                <strong>Stripe Elements:</strong> Interface de pagamentos integrada diretamente na aplicação, sem redirecionamentos
              </li>
              <li>
                <strong>Fallback para Stripe Checkout:</strong> Se o Elements falhar, tentamos o Checkout tradicional como alternativa
              </li>
              <li>
                <strong>Gerenciamento de assinaturas local:</strong> Os dados de assinatura são armazenados na tabela "subscribers" do Supabase
              </li>
            </ul>

            <p>Credenciais configuradas:</p>
            <div className="space-y-2">
              <div>
                <p className="text-xs text-gray-500 mb-1">Teste (comentado):</p>
                <div className="bg-secondary/50 p-3 rounded mb-2">
                  <code className="text-xs break-all">
                    pk_test_51PkVaQIxqQbmbZWPatjl4a492UAEDJRcaKZCA6qchePR3YAHn7wD7sQlKwLcK9PYXswWuM0afvl8Kc4JP7V7hTO600xQkDjclU
                  </code>
                </div>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Produção (em uso):</p>
                <div className="bg-secondary/50 p-3 rounded mb-2">
                  <code className="text-xs break-all">
                    pk_live_51PkVaQIxqQbmbZWPJiE0vtb8MTOld4WRvyN8zmuXtNoWRSCYO6sfGcCjijeeBLrgwEkwKk4s2JQBCor1alUo17Ly002XmwsQA7
                  </code>
                </div>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">Cartão de teste: 4242 4242 4242 4242 (use data futura e qualquer CVC)</p>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Próximos passos recomendados</CardTitle>
          <CardDescription>Para uma solução completa de assinaturas</CardDescription>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal pl-5 space-y-3">
            <li>
              <strong>Configurar webhook do Stripe:</strong> Para receber eventos como pagamentos confirmados, assinaturas canceladas, etc.
            </li>
            <li>
              <strong>Implementar lógica de renovação de assinaturas:</strong> Verificar e atualizar o status de assinantes automaticamente
            </li>
            <li>
              <strong>Página de faturamento:</strong> Permitir que usuários vejam seu histórico de pagamentos e atualizem métodos de pagamento
            </li>
            <li>
              <strong>Migrar para as chaves de produção do Stripe:</strong> Quando estiver pronto para produção
            </li>
          </ol>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>URL do Webhook</CardTitle>
          <CardDescription>Use esta URL quando configurar o webhook no Stripe</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 bg-secondary/50 p-3 rounded">
            <code className="flex-1 break-all">https://api.e-manager.apiservices.app.br/functions/v1/stripe-webhook</code>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => copyToClipboard("https://api.e-manager.apiservices.app.br/functions/v1/stripe-webhook")}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Eventos necessários para o webhook</CardTitle>
          <CardDescription>Selecione estes eventos ao configurar o webhook no Stripe</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            <li className="flex items-center gap-2">
              <code className="bg-secondary/50 p-1 rounded">payment_intent.succeeded</code>
              <span>- Quando um pagamento é bem-sucedido</span>
            </li>
            <li className="flex items-center gap-2">
              <code className="bg-secondary/50 p-1 rounded">payment_intent.payment_failed</code>
              <span>- Quando um pagamento falha</span>
            </li>
            <li className="flex items-center gap-2">
              <code className="bg-secondary/50 p-1 rounded">customer.subscription.created</code>
              <span>- Quando uma assinatura é criada</span>
            </li>
            <li className="flex items-center gap-2">
              <code className="bg-secondary/50 p-1 rounded">customer.subscription.updated</code>
              <span>- Quando uma assinatura é atualizada</span>
            </li>
            <li className="flex items-center gap-2">
              <code className="bg-secondary/50 p-1 rounded">customer.subscription.deleted</code>
              <span>- Quando uma assinatura é cancelada</span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
