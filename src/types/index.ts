export interface User {
  id: string;
  email?: string;
}

export interface Profile {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
  avatar?: string | null;
  created_at: Date;
  updated_at: Date;
  displayName?: string;
  role?: string;
  isPending?: boolean;
  isInactive?: boolean;
  accepted?: boolean;
  isInvite?: boolean;
}

export interface Workspace {
  id: string;
  name: string;
  icon?: string;
  ownerId: string;
  members: WorkspaceMember[];
  createdAt: Date;
  updatedAt: Date;
  isFreeTier?: boolean;
  role?: WorkspaceMember["role"];
}

export interface WorkspaceMember {
  userId: string;
  workspaceId?: string;
  role: "owner" | "admin" | "member" | "viewer" | "guest";
  user?: Profile;
}

export interface InvitationData {
  id: string;
  email: string;
  role: WorkspaceMember["role"];
  created_at: string;
  expires_at: string;
  accepted: boolean;
  token: string;
  workspace_id: string;
  invited_by: string;
}

export interface PageContentMetadata {
  icon?: string;
}

export interface PageContent {
  type: string;
  content: Record<string, unknown>[];
  metadata?: PageContentMetadata;
}

export interface Page {
  id: string;
  title: string;
  content: string;
  icon?: string; // This is computed from content.metadata.icon
  cover?: string;
  workspaceId: string;
  parentId: string | null;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  isArchived: boolean;
}

export type BlockType =
  | "text"
  | "heading_1"
  | "heading_2"
  | "heading_3"
  | "bulleted_list"
  | "numbered_list"
  | "todo"
  | "toggle"
  | "code"
  | "columns"
  | "image"
  | "callout"
  | "table"
  | "dashboard"
  | "kanban"
  | "sticky_note"
  | "divider"
  | "empty_line";

export interface SupabaseProfile {
  id: string;
  name: string;
  email: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface SupabaseWorkspace {
  id: string;
  name: string;
  icon?: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  role?: string;
}

export interface SupabasePage {
  id: string;
  title: string;
  content: string | Record<string, unknown>;
  icon?: string;
  cover?: string;
  workspace_id: string;
  parent_id: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
  is_archived: boolean;
}

export interface TaskDialogProps {
  open: boolean;
  onClose: () => void;
  task: Task | null;
  isCreateMode: boolean;
  initialStartDate?: Date;
  initialEndDate?: Date;
}

export interface TaskSubtask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date;
}

export interface TaskAttachment {
  id: string;
  taskId: string;
  filename: string;
  filePath: string;
  fileSize?: number;
  mimeType?: string;
  createdAt: Date;
  createdBy?: string;
  url?: string; // URL para acesso direto ao arquivo
}

export type TaskStatus = "aguardando" | "em_progresso" | "concluido" | "backlog";
export type TaskDifficulty = "facil" | "medio" | "dificil";
export type TaskType = "bug" | "nova_funcionalidade" | null;

export interface Task {
  id: string;
  title: string;
  description: string;
  assigneeId: string | null;
  assigneeName?: string;
  status: TaskStatus;
  difficulty: TaskDifficulty;
  category?: string;
  organization?: string;
  startDate: Date | string;
  endDate: Date | string;
  estimatedTime: string;
  createdAt: Date;
  updatedAt: Date;
  parentId?: string | null;
  workspaceId: string;
  subtasks?: TaskSubtask[];
  attachments?: TaskAttachment[]; // Lista de anexos da tarefa
  createdBy?: string;
  taskType?: TaskType;
  priority?: "alta" | "media" | "baixa";
  createdById?: string;
  createdByName?: string;
}

// Helper functions to convert between Supabase and app types
export function convertToProfile(data: SupabaseProfile): Profile {
  return {
    id: data.id,
    name: data.name,
    email: data.email,
    avatar_url: data.avatar_url,
    created_at: new Date(data.created_at),
    updated_at: new Date(data.updated_at),
    isPending: false, // Por padrão, usuários convertidos não são pendentes
    isInactive: false, // Por padrão, usuários convertidos não são inativos
  };
}

export function convertToWorkspace(data: SupabaseWorkspace): Workspace {
  return {
    id: data.id,
    name: data.name,
    icon: data.icon,
    ownerId: data.owner_id,
    members: [],
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    isFreeTier: false,
    role: data.role as WorkspaceMember["role"],
  };
}

export function convertToPage(data: SupabasePage): Page {
  // Melhorada a extração de ícone do conteúdo
  let icon: string | undefined = undefined;

  if (data.content) {
    try {
      // Primeiro tentamos analisar como um objeto JSON completo
      let contentObj;

      if (typeof data.content === "string") {
        contentObj = JSON.parse(data.content);
      } else {
        contentObj = data.content;
      }

      // Verificar se o conteúdo tem a estrutura de metadata
      if (contentObj && contentObj.metadata && contentObj.metadata.icon) {
        icon = contentObj.metadata.icon;
      }
      // Caso seja um array (formato antigo), verifica se tem o primeiro item com metadata
      else if (Array.isArray(contentObj) && contentObj.length > 0) {
        // Verificar se algum item tem metadata com ícone
        for (const item of contentObj) {
          if (item && item.metadata && item.metadata.icon) {
            icon = item.metadata.icon;
            break;
          }
        }

        // Se não encontrou ícone, use um padrão com base no tipo de conteúdo
        if (!icon && contentObj.length > 0) {
          // Mapear tipos para ícones padrão
          const typeToIcon: Record<string, string> = {
            kanban: "📋",
            dashboard: "📊",
            text: "📄",
            toggle: "📑",
            code: "💻",
          };

          if (contentObj[0].type && typeToIcon[contentObj[0].type]) {
            icon = typeToIcon[contentObj[0].type];
          } else {
            // Ícone padrão caso não tenha tipo definido
            icon = "📄";
          }
        }
      }

      // Se ainda não tiver ícone, use o padrão
      if (!icon) {
        icon = "📄";
      }
    } catch (e) {
      console.error("Error parsing page content for icon:", e);
      icon = "📄"; // Ícone padrão em caso de erro
    }
  } else {
    icon = "📄"; // Ícone padrão para páginas sem conteúdo
  }

  return {
    id: data.id,
    title: data.title,
    content: typeof data.content === "string" ? data.content : JSON.stringify(data.content),
    icon: icon,
    cover: data.cover,
    workspaceId: data.workspace_id,
    parentId: data.parent_id,
    createdBy: data.created_by,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    isArchived: data.is_archived,
  };
}

// Estender as definições de tipo para incluir a tabela auth.users
export interface SupabaseAuth {
  id: string;
  email?: string;
  created_at?: string;
  updated_at?: string;
}

// Tipos para colaboração em tempo real
export interface UserPresence {
  id: string;
  name: string;
  email?: string;
  avatar_url?: string | null;
  cursor: {
    x: number;
    y: number;
  };
  color: string;
  lastActivity: Date;
  pageId: string;
}

export interface StickyNote {
  id: string;
  content: string;
  position: {
    x: number;
    y: number;
  };
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  color: string;
  pageId: string;
  workspaceId: string;
}
