// Custom types for tasks since we can't modify the generated Supabase types
export interface TaskRow {
  id: string;
  title: string;
  description: string | null;
  assignee_id: string | null;
  status: string;
  difficulty: string;
  category: string | null;
  organization: string | null;
  start_date: string | null;
  end_date: string | null;
  estimated_time: string | null;
  created_at: string;
  updated_at: string;
  parent_id: string | null;
  workspace_id: string;
  created_by: string | null;
  task_type: string | null;
  priority: string | null;
  created_by_id: string | null;
  created_by_name: string | null;
}

export interface TaskSubtaskRow {
  id: string;
  task_id: string;
  title: string;
  completed: boolean;
  created_at: string;
}

export interface TaskAttachmentRow {
  id: string;
  task_id: string;
  filename: string;
  file_path: string;
  file_size: number | null;
  mime_type: string | null;
  created_at: string;
  created_by: string | null;
}
