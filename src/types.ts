// Original BlockType definition
export type BlockType =
  | "text"
  | "heading_1"
  | "heading_2"
  | "heading_3"
  | "bulleted_list"
  | "numbered_list"
  | "todo"
  | "toggle"
  | "code"
  | "columns"
  | "image"
  | "callout"
  | "table"
  | "dashboard"
  | "kanban"
  | "divider"
  | "empty_line"
  | "html_editor";

// Types for tasks
export type TaskStatus = "aguardando" | "em_progresso" | "concluido" | "backlog";
export type TaskDifficulty = "facil" | "medio" | "dificil";

export interface TaskSubtask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  assigneeId: string | null;
  assigneeName?: string;
  status: TaskStatus;
  difficulty: TaskDifficulty;
  category?: string;
  organization?: string;
  startDate: Date | string;
  endDate: Date | string;
  estimatedTime: string;
  createdAt: Date;
  updatedAt: Date;
  parentId?: string | null;
  workspaceId: string;
  subtasks?: TaskSubtask[];
  createdBy?: string;
  priority?: "alta" | "media" | "baixa";
  createdById?: string;
  createdByName?: string;
}

// User and Profile types
export interface User {
  id: string;
  email?: string;
}

export interface Profile {
  id: string;
  name: string;
  email: string;
  avatar_url?: string;
  avatar?: string; // Added avatar property
  created_at: Date;
  updated_at: Date;
  displayName?: string;
  role?: string; // Added role property
}

// Workspace related types
export interface Workspace {
  id: string;
  name: string;
  icon?: string;
  ownerId: string;
  members: WorkspaceMember[];
  createdAt: Date;
  updatedAt: Date;
  isFreeTier?: boolean;
  role?: WorkspaceMember["role"];
}

export interface WorkspaceMember {
  id?: string;
  name?: string;
  email?: string;
  userId: string;
  workspaceId?: string;
  role: "owner" | "admin" | "member" | "viewer" | "guest";
  user?: Profile;
  inviteAccepted?: boolean;
  avatar?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface InvitationData {
  id: string;
  email: string;
  role: WorkspaceMember["role"] | string; // Modified to accept string as well
  created_at: string;
  expires_at: string;
  accepted: boolean;
  token: string;
  workspace_id: string;
  invited_by: string;
}

// Page related types
export interface PageContentMetadata {
  icon?: string;
}

export interface PageContent {
  type: string;
  content: any[];
  metadata?: PageContentMetadata;
}

export interface Page {
  id: string;
  title: string;
  emoji?: string;
  content?: any;
  workspaceId: string;
  parentId?: string | null;
  cover?: string;
  icon?: string;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
  isArchived?: boolean;
}

// Supabase specific types
export interface SupabaseProfile {
  id: string;
  name: string;
  email: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface SupabaseWorkspace {
  id: string;
  name: string;
  icon?: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  role?: string;
}

export interface SupabasePage {
  id: string;
  title: string;
  content: any;
  icon?: string;
  cover?: string;
  workspace_id: string;
  parent_id: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
  is_archived: boolean;
}

// Helper functions to convert between Supabase and app types
export function convertToProfile(data: SupabaseProfile): Profile {
  return {
    id: data.id,
    name: data.name,
    email: data.email,
    avatar_url: data.avatar_url,
    avatar: data.avatar_url, // Set avatar_url as avatar
    created_at: new Date(data.created_at),
    updated_at: new Date(data.updated_at),
    displayName: data.name, // Set name as the displayName
  };
}

export function convertToWorkspace(data: SupabaseWorkspace): Workspace {
  return {
    id: data.id,
    name: data.name,
    icon: data.icon,
    ownerId: data.owner_id,
    members: [],
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    isFreeTier: false,
    role: data.role as WorkspaceMember["role"],
  };
}

export function convertToPage(data: SupabasePage): Page {
  // Extract icon from content metadata if it exists
  let icon = data.icon || undefined;
  if (data.content) {
    try {
      const contentObj = typeof data.content === "string" ? JSON.parse(data.content) : data.content;
      if (contentObj?.metadata?.icon) {
        icon = contentObj.metadata.icon;
      } else if (Array.isArray(contentObj) && contentObj.length > 0) {
        // Check if any item has metadata with icon
        for (const item of contentObj) {
          if (item && item.metadata && item.metadata.icon) {
            icon = item.metadata.icon;
            break;
          }
        }

        // If no icon found, use a default based on content type
        if (!icon && contentObj.length > 0) {
          const typeToIcon: Record<string, string> = {
            kanban: "📋",
            dashboard: "📊",
            text: "📄",
            toggle: "📑",
            code: "💻",
          };

          if (contentObj[0].type && typeToIcon[contentObj[0].type]) {
            icon = typeToIcon[contentObj[0].type];
          } else {
            // Default icon if no type defined
            icon = "📄";
          }
        }
      }

      // Still no icon, use default
      if (!icon) {
        icon = "📄";
      }
    } catch (e) {
      console.error("Error parsing page content for icon:", e);
      icon = "📄"; // Default icon in case of error
    }
  } else {
    icon = "📄"; // Default icon for pages without content
  }

  return {
    id: data.id,
    title: data.title,
    content: data.content,
    icon: icon,
    cover: data.cover,
    workspaceId: data.workspace_id,
    parentId: data.parent_id,
    createdBy: data.created_by,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    isArchived: data.is_archived,
  };
}
