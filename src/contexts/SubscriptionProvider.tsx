import React, { createContext, useContext, useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./AuthContext";
import { SubscriptionAlert } from "@/components/Subscription/SubscriptionAlert";

interface SubscriptionContextType {
  isSubscribed: boolean;
  isAdmin: boolean;
  hasReachedLimit: boolean;
  trialEndDate: Date | null;
  subscriptionTier: string | null;
  subscriptionEnd: Date | null;
  checkSubscription: () => Promise<void>;
  viewSubscriptionPlans: () => void;
  isLoading: boolean;
  isError: boolean;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [hasReachedLimit, setHasReachedLimit] = useState(false);
  const [trialEndDate, setTrialEndDate] = useState<Date | null>(null);
  const [subscriptionTier, setSubscriptionTier] = useState<string | null>(null);
  const [subscriptionEnd, setSubscriptionEnd] = useState<Date | null>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [checkAttempts, setCheckAttempts] = useState(0);

  // Verificar se deve mostrar o alerta de período de teste
  const updateAlertVisibility = (shouldShowTrialAlert: boolean) => {
    // Se o período de teste não acabou, não mostrar alerta
    if (!shouldShowTrialAlert) {
      setShowAlert(false);
      return;
    }

    // Criar um evento customizado para verificar se o usuário está em um workspace próprio
    // Este evento será capturado pelo componente WorkspacePage que tem acesso ao WorkspaceContext
    const checkEvent = new CustomEvent("check-workspace-ownership", {
      detail: {
        callback: (isOwner: boolean) => {
          // Só mostrar o alerta se o usuário for o proprietário do workspace atual
          setShowAlert(isOwner);
        },
      },
    });

    window.dispatchEvent(checkEvent);
  };

  // Fallback para verificar a assinatura diretamente do banco
  const checkSubscriptionDirectly = async () => {
    try {
      if (!user) return false;

      // Verificar se o usuário é admin
      if (user.email === "<EMAIL>") {
        setIsAdmin(true);
        setIsSubscribed(true);
        setHasReachedLimit(false);
        setSubscriptionTier("business");
        return true;
      }

      // Verificar status no banco de dados
      const { data: subscriber, error } = await supabase.from("subscribers").select("*").eq("user_id", user.id).maybeSingle();

      if (error) {
        console.error("Erro ao verificar assinatura no banco:", error);
        return false;
      }

      if (subscriber) {
        setIsSubscribed(subscriber.subscribed || false);
        setIsAdmin(subscriber.is_admin || false);
        setSubscriptionTier(subscriber.subscription_tier || null);
        setSubscriptionEnd(subscriber.subscription_end ? new Date(subscriber.subscription_end) : null);
        setTrialEndDate(subscriber.trial_end_date ? new Date(subscriber.trial_end_date) : null);

        // Verificar se o trial acabou
        const trialEnded = subscriber.trial_end_date && new Date(subscriber.trial_end_date) < new Date();
        setHasReachedLimit(trialEnded && !subscriber.subscribed);

        // Verificar se deve mostrar o alerta (baseado no trial expirado)
        if (trialEnded && !subscriber.subscribed && !subscriber.is_admin) {
          updateAlertVisibility(true);
        } else {
          updateAlertVisibility(false);
        }

        return true;
      } else {
        // Se não encontrou um registro, criar um com período de teste
        const trialEnd = new Date();
        trialEnd.setDate(trialEnd.getDate() + 7); // 7 dias de teste

        await supabase.from("subscribers").insert({
          user_id: user.id,
          email: user.email,
          subscribed: false,
          is_admin: false,
          trial_end_date: trialEnd.toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        setIsSubscribed(false);
        setIsAdmin(false);
        setTrialEndDate(trialEnd);
        setHasReachedLimit(false);
        updateAlertVisibility(false);
        return true;
      }
    } catch (err) {
      console.error("Erro ao verificar assinatura diretamente:", err);
      return false;
    }
  };

  const checkSubscription = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setIsError(false);

      // Primeiro tentar usar a função Edge
      try {
        const { data, error } = await supabase.functions.invoke("check-subscription");

        if (error) {
          throw error;
        }

        // Atualizar o estado com os dados recebidos
        setIsSubscribed(data.isSubscribed);
        setIsAdmin(data.isAdmin);
        setHasReachedLimit(data.hasReachedLimit);
        setTrialEndDate(data.trialEndDate ? new Date(data.trialEndDate) : null);
        setSubscriptionTier(data.subscriptionTier || null);
        setSubscriptionEnd(data.subscriptionEnd ? new Date(data.subscriptionEnd) : null);

        // Mostrar alerta apenas se o período de teste terminou e o usuário não está inscrito
        if (data.hasReachedLimit && !data.isSubscribed && !data.isAdmin) {
          // Mostrar somente se o período de teste realmente terminou (não durante o teste)
          if (data.trialEndDate && new Date(data.trialEndDate) < new Date()) {
            updateAlertVisibility(true);
          } else {
            updateAlertVisibility(false);
          }
        } else {
          updateAlertVisibility(false);
        }

        // Resetar contador de tentativas em caso de sucesso
        setCheckAttempts(0);
        return;
      } catch (error) {
        console.error("Erro na função Edge check-subscription:", error);
        // Continuar para o fallback
      }

      // Fallback: verificar diretamente no banco
      const success = await checkSubscriptionDirectly();

      if (success) {
        // Resetar contador de tentativas em caso de sucesso
        setCheckAttempts(0);
      } else {
        // Se o fallback também falhar, incrementar contador de tentativas
        setCheckAttempts((prev) => prev + 1);
        setIsError(true);

        // Após várias tentativas falhas, assumir que o usuário pode usar (para não bloquear experiência)
        if (checkAttempts >= 2) {
          setIsSubscribed(true);
          setIsAdmin(false);
          setHasReachedLimit(false);
          setSubscriptionTier("individual");
          updateAlertVisibility(false);
        }
      }
    } catch (error) {
      console.error("Error checking subscription:", error);
      setIsError(true);
      setCheckAttempts((prev) => prev + 1);

      // Após várias tentativas falhas, liberar acesso
      if (checkAttempts >= 2) {
        setIsSubscribed(true);
        setIsAdmin(false);
        setHasReachedLimit(false);
        setSubscriptionTier("individual");
        updateAlertVisibility(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const viewSubscriptionPlans = () => {
    // Disparar o evento para abrir o modal
    window.dispatchEvent(new CustomEvent("open-subscription-modal"));
    setShowAlert(false);
  };

  useEffect(() => {
    if (user) {
      checkSubscription();

      // Verificar assinatura a cada 10 minutos
      const interval = setInterval(() => {
        checkSubscription();
      }, 10 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [user]);

  // Se ocorrer um erro na verificação, tentar novamente após um tempo
  useEffect(() => {
    if (isError && checkAttempts < 3) {
      const retryTimeout = setTimeout(() => {
        checkSubscription();
      }, 5000 * checkAttempts); // Aumentar o tempo entre as tentativas

      return () => clearTimeout(retryTimeout);
    }
  }, [isError, checkAttempts]);

  const value = {
    isSubscribed,
    isAdmin,
    hasReachedLimit,
    trialEndDate,
    subscriptionTier,
    subscriptionEnd,
    checkSubscription,
    viewSubscriptionPlans,
    isLoading,
    isError,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
      <SubscriptionAlert isOpen={showAlert} onClose={() => setShowAlert(false)} onViewPlans={viewSubscriptionPlans} />
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error("useSubscription must be used within a SubscriptionProvider");
  }
  return context;
};
