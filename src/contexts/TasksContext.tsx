import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";
import { useWorkspace } from "./WorkspaceContext";
import { Task, TaskStatus, TaskDifficulty, TaskSubtask } from "@/types";
import { loadTasksFromSupabase } from "@/services/taskService";
import { useTaskOperations } from "@/hooks/useTaskOperations";
import { useTaskFilters } from "@/hooks/useTaskFilters";
import { toast } from "sonner";

// Type definitions for our context
interface TasksContextType {
  tasks: Task[];
  addTask: (task: Omit<Task, "id" | "createdAt" | "updatedAt">) => Promise<Task>;
  updateTask: (id: string, task: Partial<Task>) => Promise<Task | null>;
  deleteTask: (id: string) => Promise<boolean>;
  duplicateTask: (id: string, newDates?: { startDate?: Date; endDate?: Date }) => Promise<Task | null>;
  getTasksByDateRange: (startDate: Date, endDate: Date) => Task[];
  getTasksByStatus: (status: TaskStatus) => Task[];
  getTasksByAssignee: (assigneeId: string) => Task[];
  addSubtask: (taskId: string, subtaskTitle: string) => Promise<TaskSubtask | null>;
  updateSubtask: (taskId: string, subtaskId: string, updates: Partial<TaskSubtask>) => Promise<boolean>;
  deleteSubtask: (taskId: string, subtaskId: string) => Promise<boolean>;
  refreshTasks: () => Promise<void>;
  loading: boolean;
  error: string | null;
}

const TasksContext = createContext<TasksContextType | undefined>(undefined);

export const TasksProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadAttempts, setLoadAttempts] = useState(0);

  const { user } = useAuth();
  const { currentWorkspace } = useWorkspace();

  // Use our custom hooks
  const taskOperations = useTaskOperations(tasks, setTasks);
  const taskFilters = useTaskFilters(tasks);

  // Load tasks when workspace changes
  useEffect(() => {
    if (currentWorkspace) {
      loadTasks();
    } else {
      setTasks([]);
      setLoading(false);
      setError(null);
    }
  }, [currentWorkspace]);

  // Retry loading tasks if there was an error
  useEffect(() => {
    if (error && loadAttempts < 3 && currentWorkspace) {
      const retryTimeout = setTimeout(() => {
        console.log(`Retrying to load tasks (attempt ${loadAttempts + 1}/3)...`);
        loadTasks();
      }, 3000 * (loadAttempts + 1)); // Backoff strategy

      return () => clearTimeout(retryTimeout);
    }
  }, [error, loadAttempts, currentWorkspace]);

  const loadTasks = async () => {
    if (!currentWorkspace) {
      setTasks([]);
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const formattedTasks = await loadTasksFromSupabase(currentWorkspace.id);
      setTasks(formattedTasks);

      // Reset attempts counter on success
      setLoadAttempts(0);
    } catch (error) {
      console.error("Error loading tasks:", error);
      setError("Não foi possível carregar as tarefas. Tente novamente.");

      // Increment attempts counter
      setLoadAttempts((prev) => prev + 1);

      // Don't show toast if multiple loading failures to avoid spam
      if (loadAttempts < 1) {
        toast.error("Não foi possível carregar as tarefas");
      }
    } finally {
      setLoading(false);
    }
  };

  // Função pública para recarregar tarefas
  const refreshTasks = async () => {
    await loadTasks();
  };

  const value = {
    tasks,
    ...taskOperations,
    ...taskFilters,
    refreshTasks,
    loading,
    error,
  };

  return <TasksContext.Provider value={value}>{children}</TasksContext.Provider>;
};

export const useTasks = (): TasksContextType => {
  const context = useContext(TasksContext);
  if (context === undefined) {
    throw new Error("useTasks must be used within a TasksProvider");
  }
  return context;
};
