import React, { createContext, useContext, useState, useEffect, useRef } from "react";
import { useAuth } from "./AuthContext";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { UserPresence, StickyNote } from "@/types";
import { v4 as uuidv4 } from "uuid";
import { useWorkspace } from "./WorkspaceContext";

interface RealtimeContextType {
  activeUsers: UserPresence[];
  stickyNotes: StickyNote[];
  updateCursorPosition: (x: number, y: number) => void;
  createStickyNote: (content: string, x: number, y: number, color: string) => Promise<StickyNote | null>;
  updateStickyNote: (id: string, data: Partial<StickyNote>) => Promise<void>;
  deleteStickyNote: (id: string) => Promise<void>;
  updateStickyNotePosition: (id: string, x: number, y: number) => Promise<void>;
  cursorVisible: boolean;
  setCursorVisible: (visible: boolean) => void;
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

// Cores para diferenciar os usuários
const USER_COLORS = [
  "#FF5733", // Vermelho
  "#33FF57", // Verde
  "#3357FF", // Azul
  "#FF33A8", // Rosa
  "#33FFF6", // Ciano
  "#FFD133", // Amarelo
  "#9E33FF", // Roxo
  "#FF8333", // Laranja
];

interface PresencePayload {
  name: string;
  email: string;
  avatar_url: string | null;
  cursor: { x: number; y: number };
  color: string;
}

export const RealtimeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, profile } = useAuth();
  const { currentWorkspace, currentPage } = useWorkspace();
  const [activeUsers, setActiveUsers] = useState<UserPresence[]>([]);
  const [stickyNotes, setStickyNotes] = useState<StickyNote[]>([]);
  const [cursorVisible, setCursorVisible] = useState(true);
  const channelRef = useRef<ReturnType<typeof supabase.channel> | null>(null);
  const userColorRef = useRef<string>("");

  // Inicializar a cor do usuário
  useEffect(() => {
    if (user && !userColorRef.current) {
      // Gerar cor baseada no ID do usuário para que seja consistente
      const colorIndex = user.id.charCodeAt(0) % USER_COLORS.length;
      userColorRef.current = USER_COLORS[colorIndex];
    }
  }, [user]);

  // Inicializar o canal de presença quando o usuário mudar de página
  useEffect(() => {
    if (!user || !profile || !currentPage) return;

    const setupRealtimeChannel = async () => {
      // Limpa canal anterior se existir
      if (channelRef.current) {
        await supabase.removeChannel(channelRef.current);
      }

      // Inicializa o canal para a página atual
      const channel = supabase.channel(`page:${currentPage.id}`, {
        config: {
          presence: {
            key: user.id,
          },
        },
      });

      // Handler para quando outros usuários entrarem no canal
      channel.on("presence", { event: "sync" }, () => {
        const state = channel.presenceState();
        const usersArray: UserPresence[] = [];

        Object.keys(state).forEach((presenceId) => {
          const presenceData = state[presenceId][0] as unknown as PresencePayload;

          if (presenceData) {
            usersArray.push({
              id: presenceId,
              name: presenceData.name || "Usuário anônimo",
              email: presenceData.email,
              avatar_url: presenceData.avatar_url,
              cursor: presenceData.cursor || { x: 0, y: 0 },
              color: presenceData.color || "#333333",
              lastActivity: new Date(),
              pageId: currentPage.id,
            });
          }
        });

        setActiveUsers(usersArray);
      });

      // Handler para quando usuários saírem do canal
      channel.on("presence", { event: "leave" }, ({ key }) => {
        setActiveUsers((prev) => prev.filter((user) => user.id !== key));
      });

      // Subscribe para mudanças nas sticky notes
      channel.on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "sticky_notes",
          filter: `page_id=eq.${currentPage.id}`,
        },
        (payload) => {
          console.log("Sticky note changes:", payload);

          const { eventType, new: newRecord, old: oldRecord } = payload;

          if (eventType === "INSERT") {
            const note: StickyNote = {
              id: newRecord.id,
              content: newRecord.content,
              position: newRecord.position,
              createdBy: newRecord.created_by,
              createdAt: new Date(newRecord.created_at),
              updatedAt: new Date(newRecord.updated_at),
              color: newRecord.color,
              pageId: newRecord.page_id,
              workspaceId: newRecord.workspace_id,
            };

            setStickyNotes((prev) => [...prev, note]);
          } else if (eventType === "UPDATE") {
            setStickyNotes((prev) =>
              prev.map((note) => {
                if (note.id === newRecord.id) {
                  return {
                    ...note,
                    content: newRecord.content,
                    position: newRecord.position,
                    updatedAt: new Date(newRecord.updated_at),
                    color: newRecord.color,
                  };
                }
                return note;
              })
            );
          } else if (eventType === "DELETE") {
            setStickyNotes((prev) => prev.filter((note) => note.id !== oldRecord.id));
          }
        }
      );

      // Entrar no canal e definir status inicial
      channel.subscribe(async (status) => {
        if (status === "SUBSCRIBED") {
          await channel.track({
            name: profile.name,
            email: profile.email,
            avatar_url: profile.avatar_url,
            cursor: { x: 0, y: 0 },
            color: userColorRef.current,
          });
        }
      });

      channelRef.current = channel;

      // Carregar as sticky notes existentes para esta página
      fetchStickyNotes();
    };

    setupRealtimeChannel();

    return () => {
      // Limpar canal ao sair
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [user, profile, currentPage]);

  // Carregar sticky notes existentes
  const fetchStickyNotes = async () => {
    if (!currentPage || !currentWorkspace) return;

    try {
      const { data, error } = await supabase.from("sticky_notes").select("*").eq("page_id", currentPage.id).eq("workspace_id", currentWorkspace.id);

      if (error) throw error;

      const typedNotes: StickyNote[] = data.map((note) => ({
        id: note.id,
        content: note.content,
        position: note.position,
        createdBy: note.created_by,
        createdAt: new Date(note.created_at),
        updatedAt: new Date(note.updated_at),
        color: note.color,
        pageId: note.page_id,
        workspaceId: note.workspace_id,
      }));

      setStickyNotes(typedNotes);
    } catch (error) {
      console.error("Erro ao carregar sticky notes:", error);
      toast.error("Falha ao carregar notas adesivas");
    }
  };

  // Atualizar posição do cursor
  const updateCursorPosition = (x: number, y: number) => {
    if (!user || !channelRef.current || !cursorVisible) return;

    // Atualizar posição do cursor no canal
    channelRef.current.track({
      name: profile?.name,
      email: profile?.email,
      avatar_url: profile?.avatar_url,
      cursor: { x, y },
      color: userColorRef.current,
    });
  };

  // Criar uma nova sticky note
  const createStickyNote = async (content: string, x: number, y: number, color: string): Promise<StickyNote | null> => {
    if (!user || !currentPage || !currentWorkspace) return null;

    try {
      const now = new Date();
      const noteId = uuidv4();

      const newNote = {
        id: noteId,
        content,
        position: { x, y },
        created_by: user.id,
        created_at: now.toISOString(),
        updated_at: now.toISOString(),
        color,
        page_id: currentPage.id,
        workspace_id: currentWorkspace.id,
      };

      const { error } = await supabase.from("sticky_notes").insert([newNote]);

      if (error) throw error;

      const typedNote: StickyNote = {
        id: noteId,
        content,
        position: { x, y },
        createdBy: user.id,
        createdAt: now,
        updatedAt: now,
        color,
        pageId: currentPage.id,
        workspaceId: currentWorkspace.id,
      };

      // Note: não precisamos adicionar no estado porque o listener realtime já vai fazer isso
      return typedNote;
    } catch (error) {
      console.error("Erro ao criar sticky note:", error);
      toast.error("Falha ao criar nota adesiva");
      return null;
    }
  };

  // Atualizar uma sticky note
  const updateStickyNote = async (id: string, data: Partial<StickyNote>): Promise<void> => {
    if (!user) return;

    try {
      const now = new Date();
      const updateData: Record<string, unknown> = {
        ...data,
        updated_at: now.toISOString(),
      };

      // Converter campos para o formato do banco
      if (data.position) {
        updateData.position = data.position;
      }

      delete updateData.createdBy;
      delete updateData.createdAt;
      delete updateData.updatedAt;
      delete updateData.pageId;
      delete updateData.workspaceId;

      if (updateData.pageId) updateData.page_id = updateData.pageId;
      if (updateData.workspaceId) updateData.workspace_id = updateData.workspaceId;

      const { error } = await supabase.from("sticky_notes").update(updateData).eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Erro ao atualizar sticky note:", error);
      toast.error("Falha ao atualizar nota adesiva");
    }
  };

  // Atualizar a posição de uma sticky note
  const updateStickyNotePosition = async (id: string, x: number, y: number): Promise<void> => {
    await updateStickyNote(id, { position: { x, y } });
  };

  // Excluir uma sticky note
  const deleteStickyNote = async (id: string): Promise<void> => {
    try {
      const { error } = await supabase.from("sticky_notes").delete().eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Erro ao excluir sticky note:", error);
      toast.error("Falha ao excluir nota adesiva");
    }
  };

  return (
    <RealtimeContext.Provider
      value={{
        activeUsers,
        stickyNotes,
        updateCursorPosition,
        createStickyNote,
        updateStickyNote,
        deleteStickyNote,
        updateStickyNotePosition,
        cursorVisible,
        setCursorVisible,
      }}
    >
      {children}
    </RealtimeContext.Provider>
  );
};

export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (context === undefined) {
    throw new Error("useRealtime deve ser usado dentro de um RealtimeProvider");
  }
  return context;
};
