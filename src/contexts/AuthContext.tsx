import React, { createContext, useContext, useEffect, useState } from "react";
import { User as SupabaseUser } from "@supabase/supabase-js";
import { supabase } from "../integrations/supabase/client";
import { Profile, SupabaseProfile, convertToProfile } from "../types";
import { toast } from "sonner";

// Export the User type for use in other components
export type User = SupabaseUser;

interface AuthContextType {
  user: SupabaseUser | null;
  profile: Profile | null;
  login: (
    email: string,
    password: string
  ) => Promise<{
    success: boolean;
    error?: string;
  }>;
  register: (
    name: string,
    email: string,
    password: string
  ) => Promise<{
    success: boolean;
    error?: string;
  }>;
  logout: () => Promise<void>;
  isLoading: boolean;
  currentUser: Profile | null;
  users: Profile[];
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<Profile[]>([]);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase.from("profiles").select("*").eq("id", userId).maybeSingle();

      if (error) {
        console.error("Error fetching profile:", error);
        toast.error("Error fetching user profile");
        return;
      }

      if (data) {
        setProfile(convertToProfile(data as SupabaseProfile));
      } else {
        console.error("No profile found for user");
        toast.error("User profile not found");
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      toast.error("Failed to load user profile");
    }
  };

  const fetchAllUsers = async () => {
    try {
      // Buscar perfis de usuários registrados
      const { data: profilesData, error: profilesError } = await supabase.from("profiles").select("*");

      if (profilesError) {
        console.error("Error fetching profiles:", profilesError);
        return;
      }

      // Usar a API REST do Supabase para obter todos os usuários auth
      // Isso requer permissões de serviço e uma chave de serviço
      const { data: authUsersData, error: authUsersError } = await supabase.auth.admin.listUsers();

      if (authUsersError) {
        console.error("Error fetching auth users:", authUsersError);
        // Continuar com os perfis que já temos
      }

      // Mapear os perfis que já temos
      const usersMap = new Map<string, Profile>();

      if (profilesData) {
        profilesData.forEach((profile) => {
          usersMap.set(profile.id, convertToProfile(profile as SupabaseProfile));
        });
      }

      // Adicionar os usuários auth que não têm perfil
      if (authUsersData?.users) {
        authUsersData.users.forEach((authUser) => {
          if (!usersMap.has(authUser.id)) {
            // Criar um perfil básico para usuários sem perfil
            usersMap.set(authUser.id, {
              id: authUser.id,
              name: authUser.email || `Usuário ${authUser.id.substring(0, 8)}`,
              email: authUser.email || "",
              avatar_url: null,
              created_at: new Date(authUser.created_at || new Date()),
              updated_at: new Date(authUser.updated_at || new Date()),
              isInactive: true,
              // Propriedades adicionais obrigatórias
              displayName: authUser.email || `Usuário ${authUser.id.substring(0, 8)}`,
              avatar: null,
            });
          }
        });
      }

      setUsers(Array.from(usersMap.values()));
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    const checkSession = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) throw error;

        if (session?.user) {
          setUser(session.user);
          await fetchProfile(session.user.id);
        }
      } catch (error) {
        console.error("Error checking session:", error);
        toast.error("Error checking session");
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();
    fetchAllUsers();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user || null);

      if (session?.user) {
        setTimeout(() => {
          fetchProfile(session.user.id);
        }, 0);
      } else {
        setProfile(null);
      }

      setIsLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message || "Failed to login" };
    }
  };

  const register = async (name: string, email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message || "Registration failed" };
    }
  };

  const logout = async () => {
    try {
      // Fazer o signOut completo com escopo global
      await supabase.auth.signOut({ scope: "global" });

      // Atualizar o estado da aplicação
      setUser(null);
      setProfile(null);

      // Redirecionar após logout (opcional)
      window.location.href = "/";
    } catch (error) {
      console.error("Erro durante logout:", error);
      toast.error("Falha ao realizar logout");
    }
  };

  const value = {
    user,
    profile,
    login,
    register,
    logout,
    isLoading,
    currentUser: profile,
    users,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
