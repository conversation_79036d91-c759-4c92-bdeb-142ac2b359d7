import React, { createContext, useContext, useState, useEffect, useRef } from "react";
import { useAuth } from "./AuthContext";
import { toast } from "sonner";
import { Page, Workspace, WorkspaceMember, Profile } from "../types";
import { useWorkspaceOperations } from "../hooks/useWorkspaceOperations";
import { usePageOperations } from "../hooks/usePageOperations";
import { useMemberOperations } from "../hooks/useMemberOperations";
import { useWorkspaceInitialization } from "../hooks/useWorkspaceInitialization";
import { convertToPage } from "../types";
import { supabase } from "../integrations/supabase/client";
import { useUserPreferences } from "../hooks/useUserPreferences";
import { useLastPageVisited } from "../hooks/useLastPageVisited";

interface WorkspaceContextType {
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  pages: Page[];
  currentPage: Page | null;
  isLoading: boolean;
  createWorkspace: (name: string) => Promise<Workspace | null>;
  updateWorkspace: (id: string, data: Partial<Workspace>) => Promise<void>;
  deleteWorkspace: (id: string) => Promise<void>;
  setCurrentWorkspace: (workspace: Workspace | null) => void;
  switchWorkspace: (id: string) => void;
  createPage: (title: string, parentId?: string | null) => Promise<Page | null>;
  updatePage: (id: string, data: Partial<Page>) => Promise<void>;
  deletePage: (id: string) => Promise<void>;
  reorderPages: (pageId: string, newParentId: string | null) => Promise<void>;
  setCurrentPage: (page: Page | null) => void;
  inviteMember: (workspaceId: string, email: string, role: WorkspaceMember["role"]) => Promise<boolean>;
  updateMemberRole: (workspaceId: string, userId: string, role: WorkspaceMember["role"]) => Promise<void>;
  removeMember: (workspaceId: string, userId: string) => Promise<void>;
  refreshWorkspaces: () => Promise<void>;
  lastSaved: Date | null;
  workspaceMembers: Profile[] | null;
  getAllWorkspaceUsers: (workspaceId: string) => Promise<Profile[]>;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

export const WorkspaceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, profile } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const { lastWorkspaceId, updateLastWorkspace } = useUserPreferences(user?.id);
  const [workspaceMembers, setWorkspaceMembers] = useState<Profile[] | null>(null);
  const pagesLoadedRef = useRef<Set<string>>(new Set());

  const { workspaces, setWorkspaces, currentWorkspace, setCurrentWorkspace, createWorkspace, updateWorkspace, deleteWorkspace, loadWorkspaces } =
    useWorkspaceOperations(user, profile);

  const { pages, setPages, currentPage, setCurrentPage, createPage, updatePage, deletePage, reorderPages, lastSaved } = usePageOperations(
    user,
    currentWorkspace
  );

  const { inviteMember, updateMemberRole, removeMember } = useMemberOperations(user, setWorkspaces);

  // Função para obter todos os usuários relacionados ao workspace (ativos, inativos e convites pendentes)
  const getAllWorkspaceUsers = async (workspaceId: string): Promise<Profile[]> => {
    if (!workspaceId) {
      console.warn("Workspace ID não fornecido ao buscar usuários");
      return [];
    }

    try {
      console.log(`Buscando todos os usuários do workspace: ${workspaceId}`);

      // Mapa para armazenar usuários por ID
      const usersByIdMap = new Map<string, Profile>();

      // 1. Primeiro, obter todos os perfis existentes para referência
      const { data: allProfilesData, error: profilesError } = await supabase.from("profiles").select("*");

      if (profilesError) {
        console.error("Erro ao buscar todos os perfis:", profilesError);
      } else {
        console.log("Todos os perfis disponíveis:", allProfilesData);
      }

      // Criar um mapa de perfis para busca rápida por ID
      const profilesMap = new Map();
      if (allProfilesData) {
        allProfilesData.forEach((profile) => {
          if (profile && profile.id) {
            profilesMap.set(profile.id, profile);
          }
        });
      }

      // 2. Obter todos os membros do workspace
      const { data: membershipsData, error: membershipsError } = await supabase
        .from("workspace_members")
        .select(
          `
          workspace_id,
          user_id,
          role,
          profiles:user_id(*)
        `
        )
        .eq("workspace_id", workspaceId);

      if (membershipsError) {
        console.error("Erro ao buscar membros do workspace:", membershipsError);
        return [];
      }

      if (membershipsData && Array.isArray(membershipsData)) {
        console.log("Membros do workspace brutos:", membershipsData);

        // Processar membros
        for (const member of membershipsData) {
          if (!member || !member.user_id) continue;

          const userId = member.user_id;
          const profile = member.profiles || profilesMap.get(userId);

          if (profile) {
            const name = profile.name || "";
            const email = profile.email || "";

            usersByIdMap.set(userId, {
              id: userId,
              name: name,
              email: email,
              avatar_url: profile.avatar_url || null,
              avatar: profile.avatar_url || null,
              created_at: new Date(profile.created_at || new Date()),
              updated_at: new Date(profile.updated_at || new Date()),
              displayName: name,
              role: member.role,
            });
          } else {
            // Caso o perfil não seja encontrado na busca com join nem no mapa, fazer uma busca específica
            const { data: profileData } = await supabase.from("profiles").select("*").eq("id", userId).single();

            if (profileData) {
              usersByIdMap.set(userId, {
                id: userId,
                name: profileData.name || "Usuário",
                email: profileData.email || "",
                avatar_url: profileData.avatar_url || null,
                avatar: profileData.avatar_url || null,
                created_at: new Date(profileData.created_at || new Date()),
                updated_at: new Date(profileData.updated_at || new Date()),
                displayName: profileData.name || "Usuário",
                role: member.role,
              });
            } else {
              // Perfil não encontrado, usar valores padrão
              usersByIdMap.set(userId, {
                id: userId,
                name: "Usuário",
                email: "",
                avatar_url: null,
                avatar: null,
                created_at: new Date(),
                updated_at: new Date(),
                displayName: "Usuário",
                role: member.role,
              });
            }
          }
        }
      }

      // 3. Processar convites pendentes
      const { data: pendingInvitations, error: invitationsError } = await supabase
        .from("workspace_invitations")
        .select("*")
        .eq("workspace_id", workspaceId)
        .eq("accepted", false);

      if (invitationsError) {
        console.error("Erro ao buscar convites pendentes:", invitationsError);
      } else if (pendingInvitations && Array.isArray(pendingInvitations)) {
        pendingInvitations.forEach((invitation) => {
          if (!invitation || !invitation.email) return;

          const displayName = invitation.email.split("@")[0];
          const inviteId = `invite_${invitation.id}`;

          usersByIdMap.set(inviteId, {
            id: inviteId,
            name: displayName.charAt(0).toUpperCase() + displayName.slice(1),
            email: invitation.email,
            displayName: displayName.charAt(0).toUpperCase() + displayName.slice(1),
            avatar_url: null,
            avatar: null,
            created_at: new Date(invitation.created_at || new Date()),
            updated_at: new Date(invitation.created_at || new Date()),
            role: invitation.role,
          } as Profile);
        });
      }

      // Converter mapa para array e ordenar
      const result = Array.from(usersByIdMap.values()).sort((a, b) => {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();
        return nameA.localeCompare(nameB);
      });

      console.log(
        "Usuários finais processados:",
        result.map((u) => ({
          id: u.id,
          name: u.name,
          email: u.email,
          role: u.role,
        }))
      );

      return result;
    } catch (error) {
      console.error("Erro ao buscar todos os usuários do workspace:", error);
      return [];
    }
  };

  // Função para buscar e atualizar os membros do workspace atual
  const fetchWorkspaceMembers = async (workspaceId: string) => {
    if (!workspaceId) return;

    try {
      console.log("Buscando membros do workspace:", workspaceId);
      const users = await getAllWorkspaceUsers(workspaceId);
      setWorkspaceMembers(users);
      console.log(`${users.length} membros encontrados para o workspace`);
    } catch (error) {
      console.error("Erro ao buscar membros do workspace:", error);
      toast.error("Falha ao carregar membros do workspace");
    }
  };

  // Define the refreshWorkspaces function to reload workspaces
  const refreshWorkspaces = async () => {
    if (!user) return;
    try {
      setIsLoading(true);
      const refreshedWorkspaces = await loadWorkspaces();

      // If we have a current workspace, find and set the updated version
      if (currentWorkspace && refreshedWorkspaces.length > 0) {
        const updatedWorkspace = refreshedWorkspaces.find((w) => w.id === currentWorkspace.id);
        if (updatedWorkspace) {
          setCurrentWorkspace(updatedWorkspace);
          await fetchPagesForWorkspace(updatedWorkspace.id);
          await fetchWorkspaceMembers(updatedWorkspace.id);
        }
      }
    } catch (error) {
      console.error("Error refreshing workspaces:", error);
      toast.error("Falha ao atualizar os workspaces");
    } finally {
      setIsLoading(false);
    }
  };

  // Define the function to fetch pages for a specific workspace
  const fetchPagesForWorkspace = async (workspaceId: string) => {
    if (!workspaceId) return;

    try {
      console.log("Fetching pages for workspace:", workspaceId);

      const { data: pagesData, error: pagesError } = await supabase
        .from("pages")
        .select("*")
        .eq("workspace_id", workspaceId)
        .eq("is_archived", false)
        .order("created_at", { ascending: false });

      if (pagesError) {
        console.error("Error fetching pages:", pagesError);
        toast.error("Falha ao carregar páginas");
        return;
      }

      if (pagesData && pagesData.length > 0) {
        // Usar a função convertToPage para processar os dados
        const fetchedPages = pagesData.map((page) => convertToPage(page));

        console.log("Loaded pages with icons:", fetchedPages);
        setPages(fetchedPages);

        // If we don't have a current page yet, set the first one
        if (!currentPage && fetchedPages.length > 0) {
          setCurrentPage(fetchedPages[0]);
        }

        // Mark that we've loaded pages for this workspace
        pagesLoadedRef.current.add(workspaceId);
      } else {
        setPages([]);
        setCurrentPage(null);
      }
    } catch (error) {
      console.error("Error fetching pages:", error);
      toast.error("Falha ao carregar páginas");
    }
  };

  const switchWorkspace = async (id: string) => {
    console.log("Switching to workspace:", id);
    const workspace = workspaces.find((w) => w.id === id);
    if (!workspace) return;

    setCurrentWorkspace(workspace);
    await updateLastWorkspace(id);

    // Only fetch pages if we haven't already loaded them for this workspace
    if (!pagesLoadedRef.current.has(id)) {
      await fetchPagesForWorkspace(id);
    }

    await fetchWorkspaceMembers(id);
  };

  // Handle workspace initialization and selection
  useEffect(() => {
    if (!user || !workspaces.length) {
      return;
    }

    // If we're already working with a workspace, do nothing
    if (currentWorkspace) return;

    // If we have a last workspace ID from preferences, use it
    if (lastWorkspaceId) {
      const savedWorkspace = workspaces.find((w) => w.id === lastWorkspaceId);
      if (savedWorkspace) {
        console.log("Using saved workspace from preferences:", savedWorkspace.name);
        setCurrentWorkspace(savedWorkspace);
        fetchPagesForWorkspace(lastWorkspaceId);
        fetchWorkspaceMembers(lastWorkspaceId);
        return;
      }
    }

    // If no workspace is selected yet but we have workspaces, use the first one
    if (workspaces.length > 0) {
      console.log("Using first workspace:", workspaces[0].name);
      setCurrentWorkspace(workspaces[0]);
      fetchPagesForWorkspace(workspaces[0].id);
      fetchWorkspaceMembers(workspaces[0].id);
      updateLastWorkspace(workspaces[0].id);
    }
  }, [workspaces, lastWorkspaceId, user]);

  // Use the hook to restore the last page visited
  useLastPageVisited(currentPage, setCurrentPage, pages);

  useWorkspaceInitialization(user, setWorkspaces, setCurrentWorkspace, currentWorkspace, fetchPagesForWorkspace, setIsLoading);

  // When current workspace changes, ensure pages are loaded
  useEffect(() => {
    if (currentWorkspace && !pagesLoadedRef.current.has(currentWorkspace.id)) {
      fetchPagesForWorkspace(currentWorkspace.id);
    }
  }, [currentWorkspace]);

  return (
    <WorkspaceContext.Provider
      value={{
        workspaces,
        currentWorkspace,
        pages,
        currentPage,
        isLoading,
        createWorkspace,
        updateWorkspace,
        deleteWorkspace,
        setCurrentWorkspace,
        switchWorkspace,
        createPage,
        updatePage,
        deletePage,
        reorderPages,
        setCurrentPage,
        inviteMember,
        updateMemberRole,
        removeMember,
        refreshWorkspaces,
        lastSaved,
        workspaceMembers,
        getAllWorkspaceUsers,
      }}
    >
      {children}
    </WorkspaceContext.Provider>
  );
};

export const useWorkspace = () => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error("useWorkspace must be used within a WorkspaceProvider");
  }
  return context;
};
