
import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";

interface RegisterFormProps {
  onSwitchToLogin: () => void;
}

export const RegisterForm = ({
  onSwitchToLogin
}: RegisterFormProps) => {
  const {
    register,
    isLoading
  } = useAuth();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    if (!name || !email || !password || !confirmPassword) {
      setError("Por favor, preencha todos os campos.");
      return;
    }
    
    if (password !== confirmPassword) {
      setError("As senhas não coincidem.");
      return;
    }
    
    if (password.length < 6) {
      setError("A senha deve ter pelo menos 6 caracteres.");
      return;
    }

    // Validate email format with a simple regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError("Formato de email inválido.");
      return;
    }
    
    try {
      // Make sure we're passing the parameters in the correct order: name, email, password
      const result = await register(name, email, password);
      if (!result.success) {
        setError(result.error || "Ocorreu um erro ao criar a conta.");
      }
    } catch (err) {
      setError((err as Error).message || "Ocorreu um erro ao criar a conta.");
    }
  };

  return <div className="w-full max-w-sm p-6 bg-notion-sidebar rounded-lg border border-notion-border">
      <h2 className="text-2xl font-semibold mb-6 text-notion-text text-center">Criar sua conta grátis</h2>
      
      {error && <div className="bg-red-500/10 border border-red-500/50 text-red-500 p-3 rounded mb-4 text-sm">
          {error}
        </div>}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name" className="text-notion-text">Nome</Label>
          <Input id="name" type="text" placeholder="Seu nome" value={name} onChange={e => setName(e.target.value)} className="bg-notion-page border-notion-border text-notion-text" required />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="email" className="text-notion-text">Email</Label>
          <Input id="email" type="email" placeholder="<EMAIL>" value={email} onChange={e => setEmail(e.target.value)} className="bg-notion-page border-notion-border text-notion-text" required />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="password" className="text-notion-text">Senha</Label>
          <Input id="password" type="password" placeholder="********" value={password} onChange={e => setPassword(e.target.value)} className="bg-notion-page border-notion-border text-notion-text" required />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-notion-text">Confirme a senha</Label>
          <Input id="confirmPassword" type="password" placeholder="********" value={confirmPassword} onChange={e => setConfirmPassword(e.target.value)} className="bg-notion-page border-notion-border text-notion-text" required />
        </div>
        
        <Button type="submit" className="w-full bg-white text-black hover:bg-white/90" disabled={isLoading}>
          {isLoading ? "Criando conta..." : "Registrar"}
        </Button>
        
        <div className="text-center text-sm text-notion-text">
          <span>Já tem uma conta? </span>
          <button type="button" onClick={onSwitchToLogin} className="text-blue-400 hover:underline focus:outline-none">
            Entrar
          </button>
        </div>
      </form>
    </div>;
};
