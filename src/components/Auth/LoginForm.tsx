import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
interface LoginFormProps {
  onSwitchToRegister: () => void;
}
export const LoginForm = ({
  onSwitchToRegister
}: LoginFormProps) => {
  const {
    login,
    isLoading
  } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    if (!email || !password) {
      setError("Por favor, preencha todos os campos.");
      return;
    }
    try {
      await login(email, password);
    } catch (err) {
      setError((err as <PERSON>rror).message || "Ocorreu um erro ao fazer login.");
    }
  };
  return <div className="w-full max-w-sm p-6 bg-notion-sidebar rounded-lg border border-notion-border">
      <h2 className="text-2xl font-semibold mb-6 text-notion-text text-center">Entre na sua conta</h2>
      
      {error && <div className="bg-red-500/10 border border-red-500/50 text-red-500 p-3 rounded mb-4 text-sm">
          {error}
        </div>}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-notion-text">Email</Label>
          <Input id="email" type="email" placeholder="<EMAIL>" value={email} onChange={e => setEmail(e.target.value)} className="bg-notion-page border-notion-border text-notion-text" required />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="password" className="text-notion-text">Senha</Label>
          <Input id="password" type="password" placeholder="********" value={password} onChange={e => setPassword(e.target.value)} className="bg-notion-page border-notion-border text-notion-text" required />
        </div>
        
        <Button type="submit" className="w-full bg-white text-black hover:bg-white/90" disabled={isLoading}>
          {isLoading ? "Entrando..." : "Entrar"}
        </Button>
        
        <div className="text-center text-sm text-notion-text">
          <span>Não tem uma conta? </span>
          <button type="button" onClick={onSwitchToRegister} className="text-blue-400 hover:underline focus:outline-none">
            Registre-se
          </button>
        </div>
        
        
      </form>
    </div>;
};