import React from "react";
import { UserPresence } from "@/types";

interface UserCursorProps {
  user: UserPresence;
}

export const UserCursor: React.FC<UserCursorProps> = ({ user }) => {
  // Não mostrar cursor para o próprio usuário
  if (!user || !user.cursor) return null;

  // Função para determinar se deve usar texto claro ou escuro com base na cor de fundo
  const getContrastColor = (color: string) => {
    // Converter a cor hex para RGB
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);

    // Calcular luminância (fórmula padrão para calcular contraste)
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Retornar cor do texto com base na luminância
    return luminance > 0.5 ? "#000000" : "#FFFFFF";
  };

  // Estilo para o cursor baseado na cor atribuída ao usuário
  const cursorStyle: React.CSSProperties = {
    position: "absolute",
    left: user.cursor.x,
    top: user.cursor.y,
    pointerEvents: "none", // Para não interferir com cliques na página
    zIndex: 1000,
    transform: "translate(0, 0)",
    transition: "transform 0.1s ease, top 0.1s ease, left 0.1s ease", // Suavizar movimento
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  };

  // Estilo para a placa de identificação
  const nameTagStyle: React.CSSProperties = {
    backgroundColor: user.color,
    color: getContrastColor(user.color),
    padding: "2px 6px",
    borderRadius: "4px",
    fontSize: "12px",
    fontWeight: "bold",
    whiteSpace: "nowrap",
    marginTop: "4px", // Espaço entre o cursor e o nome
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
  };

  return (
    <div style={cursorStyle} className="user-cursor">
      {/* Cursor simples do mouse */}
      <svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 0L0 16L3.5 12.5L7 19L9 18L5.5 11.5L10 11.5L0 0Z" fill={user.color} stroke="#FFFFFF" strokeWidth="1" />
      </svg>

      {/* Tag de nome */}
      <div style={nameTagStyle}>{user.name?.split(" ")[0] || "Usuário"}</div>
    </div>
  );
};
