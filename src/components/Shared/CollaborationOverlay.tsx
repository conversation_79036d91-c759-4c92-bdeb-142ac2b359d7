import React, { useEffect, useState } from "react";
import { useRealtime } from "@/contexts/RealtimeContext";
import { useAuth } from "@/contexts/AuthContext";
import { UserCursor } from "./UserCursor";
import { StickyNoteComponent } from "./StickyNoteComponent";
import { Button } from "../ui/button";
import { Plus, Eye, EyeOff, Users, StickyNote } from "lucide-react";

interface CollaborationOverlayProps {
  containerRef: React.RefObject<HTMLDivElement>;
}

export const CollaborationOverlay: React.FC<CollaborationOverlayProps> = ({ containerRef }) => {
  const { activeUsers, stickyNotes, updateCursorPosition, createStickyNote, cursorVisible, setCursorVisible } = useRealtime();
  const { user } = useAuth();
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#FFD133"); // <PERSON><PERSON> por padrão
  const [notesVisible, setNotesVisible] = useState(true);

  // Cores disponíveis para as notas
  const noteColors = ["#FFD133", "#33FF57", "#3357FF", "#FF33A8", "#33FFF6", "#FF5733", "#9E33FF"];

  // Monitorar movimento do mouse dentro do container
  useEffect(() => {
    if (!containerRef.current) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;

      // Obter posição relativa ao container
      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Atualizar posição no contexto
      updateCursorPosition(x, y);
    };

    const container = containerRef.current;
    container.addEventListener("mousemove", handleMouseMove);

    return () => {
      container.removeEventListener("mousemove", handleMouseMove);
    };
  }, [containerRef, updateCursorPosition]);

  // Criar uma nova nota adesiva
  const handleCreateNote = async () => {
    if (!containerRef.current) return;

    // Calcular posição central do container
    const rect = containerRef.current.getBoundingClientRect();
    const x = rect.width / 2 - 100; // Metade da largura da nota (200px)
    const y = rect.height / 2 - 75; // Metade da altura aproximada da nota

    await createStickyNote("Digite sua nota aqui...", x, y, selectedColor);
    setShowColorPicker(false);
  };

  // Filtrar o usuário atual dos cursores ativos
  const otherUsers = activeUsers.filter((u) => u.id !== user?.id);

  // Alternar visibilidade dos cursores
  const toggleCursorVisibility = () => {
    setCursorVisible(!cursorVisible);
  };

  // Alternar visibilidade das notas
  const toggleNotesVisibility = () => {
    setNotesVisible(!notesVisible);
  };

  return (
    <>
      {/* Overlay para os cursores */}
      <div className="absolute inset-0 pointer-events-none">
        {cursorVisible && otherUsers.map((user) => <UserCursor key={user.id} user={user} />)}
      </div>

      {/* Sticky notes */}
      <div className="sticky-notes-container" style={{ display: notesVisible ? "block" : "none" }}>
        {stickyNotes.map((note) => (
          <StickyNoteComponent key={note.id} note={note} />
        ))}
      </div>

      {/* Controles */}
      <div className="fixed bottom-6 right-6 flex flex-col items-end gap-2">
        {/* Seletor de cores */}
        {showColorPicker && (
          <div className="bg-white border border-gray-200 rounded-md p-2 shadow-lg">
            <div className="flex gap-2 mb-2">
              {noteColors.map((color) => (
                <button
                  key={color}
                  className={`w-6 h-6 rounded-full ${selectedColor === color ? "ring-2 ring-black" : ""}`}
                  style={{ backgroundColor: color }}
                  onClick={() => setSelectedColor(color)}
                />
              ))}
            </div>
            <Button className="w-full text-xs" size="sm" variant="default" onClick={handleCreateNote}>
              Adicionar nota
            </Button>
          </div>
        )}

        {/* Status de usuários ativos */}
        {otherUsers.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-md px-3 py-1 shadow-lg text-xs flex items-center gap-1 text-gray-600">
            <Users size={14} className="text-gray-500" />
            <span>{otherUsers.length} online</span>
          </div>
        )}

        {/* Botões de ação */}
        <div className="flex gap-2">
          {/* Botão para mostrar/ocultar cursores */}
          <Button
            variant="default"
            size="icon"
            className="bg-white border-gray-200"
            onClick={toggleCursorVisibility}
            title={cursorVisible ? "Ocultar cursores" : "Mostrar cursores"}
          >
            {cursorVisible ? <Eye size={16} /> : <EyeOff size={16} />}
          </Button>

          {/* Botão para mostrar/ocultar notas */}
          <Button
            variant="outline"
            size="icon"
            className="bg-white border-gray-200"
            onClick={toggleNotesVisibility}
            title={notesVisible ? "Ocultar notas" : "Mostrar notas"}
          >
            <StickyNote size={16} className={notesVisible ? "text-amber-500" : "text-gray-400"} />
          </Button>

          {/* Botão para adicionar nota */}
          <Button variant="default" size="icon" onClick={() => setShowColorPicker(!showColorPicker)} title="Adicionar nota adesiva">
            <Plus size={16} />
          </Button>
        </div>
      </div>
    </>
  );
};
