import React, { useState, useRef, useEffect } from "react";
import { StickyNote } from "@/types";
import { useRealtime } from "@/contexts/RealtimeContext";
import { useAuth } from "@/contexts/AuthContext";
import { Trash2, Edit } from "lucide-react";
import { toast } from "sonner";

interface StickyNoteProps {
  note: StickyNote;
  isEditing?: boolean;
}

export const StickyNoteComponent: React.FC<StickyNoteProps> = ({ note, isEditing }) => {
  const { user } = useAuth();
  const { updateStickyNote, updateStickyNotePosition, deleteStickyNote } = useRealtime();

  const [content, setContent] = useState(note.content);
  const [isEditingContent, setIsEditingContent] = useState(isEditing || false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const noteRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLTextAreaElement>(null);

  // Função para determinar se deve usar texto claro ou escuro com base na cor de fundo
  const getContrastColor = (color: string) => {
    try {
      // Converter a cor hex para RGB
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);

      // Calcular luminância (fórmula padrão para calcular contraste)
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // Retornar cor do texto com base na luminância
      return luminance > 0.5 ? "#000000" : "#FFFFFF";
    } catch (e) {
      return "#000000"; // Fallback para texto escuro se houver erro
    }
  };

  // Atualiza o conteúdo local quando o note muda
  useEffect(() => {
    setContent(note.content);
  }, [note.content]);

  // Foca no textarea quando entra no modo de edição
  useEffect(() => {
    if (isEditingContent && contentRef.current) {
      contentRef.current.focus();
    }
  }, [isEditingContent]);

  // Começar a arrastar a nota
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === contentRef.current || isEditingContent) return;

    // Verificar se o clique foi em um dos botões
    const target = e.target as HTMLElement;
    if (target.closest("button")) return;

    setIsDragging(true);

    if (noteRef.current) {
      const rect = noteRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  // Processar o arrastar da nota
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !noteRef.current) return;

    // Calcular nova posição
    const newX = e.clientX - dragOffset.x;
    const newY = e.clientY - dragOffset.y;

    // Aplicar nova posição via CSS
    noteRef.current.style.left = `${newX}px`;
    noteRef.current.style.top = `${newY}px`;
  };

  // Finalizar o arrastar e atualizar posição no banco
  const handleMouseUp = async () => {
    if (!isDragging || !noteRef.current) return;

    setIsDragging(false);

    // Obter posição final
    const rect = noteRef.current.getBoundingClientRect();

    // Atualizar no banco de dados
    await updateStickyNotePosition(note.id, rect.left, rect.top);
  };

  // Adicionar/remover event listeners
  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
    } else {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    }

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging]);

  // Salvar alterações do conteúdo
  const handleContentSave = async () => {
    setIsEditingContent(false);

    if (content !== note.content) {
      await updateStickyNote(note.id, { content });
    }
  };

  // Excluir a nota
  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    try {
      await deleteStickyNote(note.id);
      toast.success("Nota removida com sucesso");
    } catch (error) {
      console.error("Erro ao excluir nota:", error);
      toast.error("Erro ao remover nota");
    }
  };

  // Permitir apenas o próprio criador excluir/editar
  const isOwner = user?.id === note.createdBy;

  // Determinar cores de texto para melhor contraste
  const headerTextColor = getContrastColor(note.color);

  // Tudo em português e seguindo o design do sistema
  return (
    <div
      ref={noteRef}
      className={`sticky-note absolute rounded-md shadow-md overflow-hidden
        ${isDragging ? "cursor-grabbing z-50" : "cursor-grab z-40"}
        ${!isOwner ? "border-2" : ""}
        `}
      style={{
        backgroundColor: note.color,
        width: "200px",
        left: note.position.x,
        top: note.position.y,
        userSelect: isDragging ? "none" : "auto",
        borderColor: isOwner ? "transparent" : note.color,
        borderStyle: isOwner ? "none" : "dashed",
        opacity: isDragging ? 0.8 : 1,
      }}
      onMouseDown={handleMouseDown}
    >
      <div className="p-2 sticky-note-header flex justify-between items-center" style={{ backgroundColor: `${note.color}dd` }}>
        <div className="text-xs truncate max-w-[125px]" style={{ color: headerTextColor, fontWeight: "bold" }}>
          {note.createdBy === user?.id ? "Você" : "Colaborador"}
        </div>
        {isOwner && (
          <div className="flex gap-1">
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                setIsEditingContent(true);
              }}
              className="opacity-70 hover:opacity-100"
              style={{ color: headerTextColor }}
            >
              <Edit size={14} />
            </button>
            <button type="button" onClick={handleDelete} className="opacity-70 hover:opacity-100" style={{ color: headerTextColor }}>
              <Trash2 size={14} />
            </button>
          </div>
        )}
      </div>

      <div className="p-2 min-h-[100px] bg-white bg-opacity-90">
        {isEditingContent ? (
          <textarea
            ref={contentRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onBlur={handleContentSave}
            onKeyDown={(e) => {
              if (e.key === "Enter" && e.ctrlKey) {
                handleContentSave();
              }
            }}
            className="w-full h-full min-h-[90px] p-1 outline-none bg-transparent resize-none text-gray-800"
            placeholder="Digite sua nota aqui..."
          />
        ) : (
          <div className="whitespace-pre-wrap break-words text-sm text-gray-800" onClick={() => isOwner && setIsEditingContent(true)}>
            {content || "Nota sem conteúdo"}
          </div>
        )}
      </div>
    </div>
  );
};
