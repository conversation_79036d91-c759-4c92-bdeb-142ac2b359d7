import React, { useState, useEffect } from "react";
import { useTasks } from "@/contexts/TasksContext";
import { Task } from "@/types";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, addMonths, subMonths } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ChevronLeft, ChevronRight, Edit, Trash, Copy, Plus } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { TaskDialog } from "../Kanban/TaskDialog";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Profile } from "@/types";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import { User } from "lucide-react";
import { renderProfileName } from "@/utils/profileUtils";

interface CalendarProps {
  onEditTask: (task: Task) => void;
  onDeleteTask: (taskId: string) => void;
  onDuplicateTask: (taskId: string) => void;
  filterUserId?: string | "all";
  selectedUsers?: string[];
  initialMonth?: Date;
  allUsers?: Profile[];
}

export const Calendar: React.FC<CalendarProps> = ({
  onEditTask,
  onDeleteTask,
  onDuplicateTask,
  filterUserId = "all",
  selectedUsers = [],
  initialMonth = new Date(),
  allUsers = [],
}) => {
  const { tasks, duplicateTask } = useTasks();
  const { users, currentUser } = useAuth();

  const availableUsers = allUsers.length > 0 ? allUsers : users;

  const [currentDate, setCurrentDate] = useState(initialMonth);
  const [daysInMonth, setDaysInMonth] = useState<Date[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<string[]>([]);
  const [isUserFilterOpen, setIsUserFilterOpen] = useState(false);
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [selectedDay, setSelectedDay] = useState<Date | null>(null);

  useEffect(() => {
    const start = startOfMonth(currentDate);
    const end = endOfMonth(currentDate);
    const days = eachDayOfInterval({ start, end });
    setDaysInMonth(days);
  }, [currentDate]);

  // Atualizar currentDate quando initialMonth for alterado externamente
  useEffect(() => {
    setCurrentDate(initialMonth);
  }, [initialMonth]);

  // Usar selectedUsers fornecidos externamente ou filterUserId para compatibilidade
  useEffect(() => {
    if (selectedUsers.length > 0) {
      setFilteredUsers(selectedUsers);
    } else if (filterUserId && filterUserId !== "all") {
      setFilteredUsers([filterUserId]);
    } else {
      setFilteredUsers([]);
    }
  }, [filterUserId, selectedUsers]);

  const handlePrevMonth = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1));
  };

  const handleAddTask = (day: Date) => {
    setSelectedDay(day);
    setIsTaskDialogOpen(true);
  };

  const handleCloseTaskDialog = () => {
    setIsTaskDialogOpen(false);
    setSelectedDay(null);
  };

  const toggleUserFilter = (userId: string) => {
    setFilteredUsers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  const getTasksForDay = (day: Date) => {
    return tasks.filter((task) => {
      const startDate = new Date(task.startDate);
      const endDate = new Date(task.endDate);

      // Verifica se a tarefa está dentro do dia atual
      const isInRange = (startDate <= day && endDate >= day) || isSameDay(startDate, day) || isSameDay(endDate, day);

      // Verifica se a tarefa passa pelo filtro de usuários
      const passesUserFilter = filteredUsers.length === 0 || (task.assigneeId && filteredUsers.includes(task.assigneeId));

      return isInRange && passesUserFilter;
    });
  };

  const handleDuplicateToNextDay = (taskId: string, currentDay: Date) => {
    const nextDay = new Date(currentDay);
    nextDay.setDate(nextDay.getDate() + 1);

    duplicateTask(taskId, {
      startDate: nextDay,
      endDate: nextDay,
    });
  };

  const weekDays = ["domingo", "segunda-feira", "terça-feira", "quarta-feira", "quinta-feira", "sexta-feira", "sábado"];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "aguardando":
        return "bg-amber-500 hover:bg-amber-600";
      case "em_progresso":
        return "bg-blue-500 hover:bg-blue-600";
      case "concluido":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "facil":
        return "bg-green-500 hover:bg-green-600";
      case "medio":
        return "bg-amber-500 hover:bg-amber-600";
      case "dificil":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  // Buscar o usuário real pelo ID
  const getUserById = (userId: string): Profile | undefined => {
    if (!userId) return undefined;
    return availableUsers.find((user) => user.id === userId);
  };

  // Renderizar o nome do usuário com informações de status
  const renderUserName = (user: Profile | undefined) => {
    return renderProfileName(user);
  };

  return (
    <div className="mt-2">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <button onClick={handlePrevMonth} className="p-2 rounded-full hover:bg-notion-hover">
            <ChevronLeft className="h-5 w-5 text-notion-text" />
          </button>
          <h2 className="text-xl font-medium text-notion-text">{format(currentDate, "MMMM yyyy", { locale: ptBR })}</h2>
          <button onClick={handleNextMonth} className="p-2 rounded-full hover:bg-notion-hover">
            <ChevronRight className="h-5 w-5 text-notion-text" />
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <Popover open={isUserFilterOpen} onOpenChange={setIsUserFilterOpen}>
            <PopoverContent className="w-[200px] p-0 bg-notion-sidebar border-notion-border">
              <Command className="bg-notion-sidebar">
                <CommandInput placeholder="Buscar usuário..." className="h-9 border-none focus:ring-0 bg-notion-sidebar text-notion-text" />
                <ScrollArea className="h-[200px]">
                  <CommandEmpty>Nenhum usuário encontrado.</CommandEmpty>
                  <CommandGroup>
                    {availableUsers
                      .filter((user) => (user.name || user.email) && !user.id.toString().startsWith("invite_"))
                      .map((user) => {
                        const isSelected = filteredUsers.includes(user.id);

                        return (
                          <CommandItem
                            key={user.id}
                            onSelect={() => toggleUserFilter(user.id)}
                            className={`text-notion-text ${isSelected ? "bg-purple-500/30" : ""}`}
                          >
                            <div
                              className={`mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-notion-border ${
                                isSelected ? "bg-purple-500 text-white" : "opacity-50"
                              }`}
                            >
                              {isSelected ? (
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-4 w-4">
                                  <path d="M5 13l4 4L19 7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                              ) : null}
                            </div>
                            <span>{user.name || user.email || `Usuário ${user.id.substring(0, 8)}`}</span>
                          </CommandItem>
                        );
                      })}
                  </CommandGroup>
                </ScrollArea>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {weekDays.map((day, index) => (
          <div key={index} className="font-medium text-notion-muted text-center p-2">
            {day}
          </div>
        ))}

        {Array.from({ length: daysInMonth[0]?.getDay() || 0 }).map((_, index) => (
          <div key={`empty-${index}`} className="border border-notion-border bg-notion-sidebar/50 rounded-md"></div>
        ))}

        {daysInMonth.map((day) => {
          const tasksForDay = getTasksForDay(day);
          const isToday = isSameDay(day, new Date());

          return (
            <div
              key={day.toISOString()}
              className={`border border-notion-border relative ${
                isToday ? "bg-purple-600/10 border-purple-500" : "bg-notion-sidebar"
              } rounded-md min-h-[150px] overflow-hidden`}
            >
              <div className="flex justify-between items-center p-2 border-b border-notion-border sticky top-0 bg-notion-sidebar z-10">
                <span className={`text-sm font-medium ${isToday ? "text-purple-600" : "text-notion-text"}`}>{format(day, "d")}</span>
                <button onClick={() => handleAddTask(day)} className="p-1 text-notion-muted hover:text-notion-text rounded" title="Adicionar tarefa">
                  <Plus size={14} />
                </button>
              </div>

              <ScrollArea className="h-full max-h-[160px] p-1">
                {tasksForDay.map((task) => {
                  const startDate = new Date(task.startDate);
                  const endDate = new Date(task.endDate);
                  const isMultiDay = !isSameDay(startDate, endDate);
                  const isStartDay = isSameDay(startDate, day);
                  const isEndDay = isSameDay(endDate, day);

                  return (
                    <div
                      key={`${task.id}-${day.toISOString()}`}
                      className={`mb-1 p-1 rounded ${
                        isMultiDay ? `${getStatusColor(task.status)} text-white` : "bg-notion-page border border-notion-border"
                      }`}
                    >
                      <div className="flex justify-between items-center text-white">
                        <div className="text-xs font-medium truncate max-w-[70%]">{task.title}</div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => onEditTask(task)}
                            className="p-0.5 text-notion-muted hover:text-notion-text rounded"
                            title="Editar tarefa"
                          >
                            <Edit size={10} />
                          </button>
                          <button
                            onClick={() => handleDuplicateToNextDay(task.id, day)}
                            className="p-0.5 text-notion-muted hover:text-notion-text rounded"
                            title="Duplicar para o próximo dia"
                          >
                            <Copy size={10} />
                          </button>
                          <button
                            onClick={() => onDeleteTask(task.id)}
                            className="p-0.5 text-notion-muted hover:text-red-500 rounded"
                            title="Excluir tarefa"
                          >
                            <Trash size={10} />
                          </button>
                        </div>
                      </div>

                      {!isMultiDay && (
                        <div className="flex items-center justify-between mt-1">
                          <Badge className={`${getStatusColor(task.status)} text-white text-[10px] px-1 py-0`}>
                            {task.status === "aguardando" ? "Aguardando" : task.status === "em_progresso" ? "Em Progresso" : "Concluído"}
                          </Badge>
                          <span className="text-[10px] text-notion-muted">{task.estimatedTime}</span>
                        </div>
                      )}

                      {task.assigneeId && (
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3 text-notion-muted" />
                          <span className="text-xs text-notion-muted truncate">{renderUserName(getUserById(task.assigneeId))}</span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </ScrollArea>
            </div>
          );
        })}
      </div>

      <TaskDialog open={isTaskDialogOpen} onClose={handleCloseTaskDialog} task={null} isCreateMode={true} />
    </div>
  );
};
