import React, { useState, useEffect } from "react";
import { useWorkspace } from "../../contexts/WorkspaceContext";
import { useAuth } from "../../contexts/AuthContext";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "../ui/dialog";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Separator } from "../ui/separator";
import { ScrollArea } from "../ui/scroll-area";
import { InviteMemberModal } from "./InviteMemberModal";
import { User, Plus, Trash2, Settings, Users, Mail } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { WorkspaceMember, InvitationData } from "../../types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { sendInvitationEmail } from "../../utils/emailUtils";
import { renderProfileName } from "@/utils/profileUtils";

interface WorkspaceSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const WorkspaceSettings = ({ isOpen, onClose }: WorkspaceSettingsProps) => {
  const { user, profile } = useAuth();
  const { currentWorkspace, updateWorkspace, deleteWorkspace, updateMemberRole, removeMember } = useWorkspace();

  const [name, setName] = useState(currentWorkspace?.name || "");
  const [isLoading, setIsLoading] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<InvitationData[]>([]);
  const [refreshData, setRefreshData] = useState(0);

  React.useEffect(() => {
    setName(currentWorkspace?.name || "");
  }, [currentWorkspace]);

  useEffect(() => {
    if (currentWorkspace && isOpen) {
      fetchPendingInvitations();
    }
  }, [currentWorkspace, isOpen, refreshData]);

  const fetchPendingInvitations = async () => {
    if (!currentWorkspace) return;

    try {
      console.log("Fetching invitations for workspace settings:", currentWorkspace.id);

      const { data, error } = await supabase.from("workspace_invitations").select("*").eq("workspace_id", currentWorkspace.id).eq("accepted", false);

      if (error) {
        console.error("Error fetching invitations:", error);
        toast.error("Erro ao carregar convites");
        return;
      }

      console.log("Fetched invitations for settings:", data);

      const typedData = data
        ? data.map((item) => ({
            ...item,
            role: item.role as WorkspaceMember["role"],
          }))
        : [];

      setPendingInvitations(typedData);
    } catch (err) {
      console.error("Error fetching invitations:", err);
      toast.error("Erro ao carregar convites");
    }
  };

  const handleSave = async () => {
    if (!currentWorkspace || !name.trim()) return;

    setIsLoading(true);
    try {
      await updateWorkspace(currentWorkspace.id, { name: name.trim() });
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentWorkspace) return;

    if (window.confirm(`Tem certeza que deseja excluir o workspace "${currentWorkspace.name}"? Esta ação não pode ser desfeita.`)) {
      try {
        await deleteWorkspace(currentWorkspace.id);
        onClose();
      } catch (error) {
        console.error(error);
      }
    }
  };

  const handleRoleChange = async (userId: string, role: WorkspaceMember["role"]) => {
    if (!currentWorkspace) return;

    try {
      await updateMemberRole(currentWorkspace.id, userId, role);
    } catch (error) {
      console.error(error);
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!currentWorkspace || !isAdmin) return;
    if (confirm(`Tem certeza que deseja remover este membro do workspace ${currentWorkspace.name}?`)) {
      try {
        await removeMember(currentWorkspace.id, userId);
        // Data should refresh automatically if context updates state correctly
        // Force refresh if needed: setRefreshData(p => p + 1);
      } catch (err) {
        console.error("Error removing member:", err);
        // Toast error is likely handled inside removeMember context function
      }
    }
  };

  const cancelInvitation = async (invitationId: string) => {
    try {
      console.log("Cancelling invitation from settings:", invitationId);

      const { error } = await supabase.from("workspace_invitations").delete().eq("id", invitationId);

      if (error) throw error;

      toast.success("Convite cancelado com sucesso");
      setRefreshData((prev) => prev + 1);
    } catch (err) {
      console.error("Error canceling invitation:", err);
      toast.error("Erro ao cancelar convite");
    }
  };

  const resendInvitation = async (invitation: InvitationData) => {
    if (!profile || !currentWorkspace) return;

    try {
      // Gerar um novo token
      const newToken = crypto.randomUUID();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      console.log("Resending invitation with new token:", newToken);

      // Atualizar o convite com o novo token
      const { error } = await supabase
        .from("workspace_invitations")
        .update({
          token: newToken,
          expires_at: expiresAt.toISOString(),
        })
        .eq("id", invitation.id);

      if (error) throw error;

      // Send invitation email
      const emailResult = await sendInvitationEmail(invitation.email, currentWorkspace.name, profile.name, newToken);

      console.log("Email resend result:", emailResult);

      if (!emailResult.success) {
        console.error("Failed to send invitation email:", emailResult.error);
        toast.warning("Erro ao enviar email, mas o token foi atualizado");
        return;
      }

      toast.success(`Convite reenviado para ${invitation.email}`);
    } catch (err) {
      console.error("Error resending invitation:", err);
      toast.error("Erro ao reenviar convite");
    }
  };

  const isOwner = currentWorkspace?.ownerId === user?.id;
  const isAdmin = isOwner || currentWorkspace?.members.some((member) => member.userId === user?.id && member.role === "admin");

  if (!currentWorkspace) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={() => onClose()}>
        <DialogContent
          className="bg-notion-sidebar border-notion-border text-notion-text max-w-3xl"
          aria-describedby="workspace-settings-description"
        >
          <DialogHeader>
            <DialogTitle className="text-notion-text">Configurações do Workspace</DialogTitle>
            <DialogDescription id="workspace-settings-description">Gerencie as configurações e membros do seu workspace</DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="bg-notion-hover mb-4">
              <TabsTrigger value="general" className="data-[state=active]:bg-notion-page">
                <Settings className="h-4 w-4 mr-2" />
                Geral
              </TabsTrigger>
              <TabsTrigger value="members" className="data-[state=active]:bg-notion-page">
                <Users className="h-4 w-4 mr-2" />
                Membros
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-notion-text">
                  Nome do workspace
                </Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="bg-notion-page border-notion-border text-notion-text"
                  disabled={!isAdmin}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={onClose} className="border-notion-border text-notion-text hover:bg-notion-hover">
                  Cancelar
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isLoading || !isAdmin || name === currentWorkspace.name}
                  className="bg-white text-black hover:bg-white/90"
                >
                  {isLoading ? "Salvando..." : "Salvar alterações"}
                </Button>
              </div>

              {isOwner && (
                <>
                  <Separator className="my-6 bg-notion-border" />

                  <div className="border border-red-500/30 rounded-md p-4 bg-red-500/5">
                    <h3 className="text-red-500 font-semibold mb-2">Zona de perigo</h3>
                    <p className="text-notion-text text-sm mb-4">
                      Excluir um workspace é uma ação permanente e não pode ser desfeita. Isso excluirá todas as páginas e dados deste workspace.
                    </p>
                    <Button variant="destructive" onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir workspace
                    </Button>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="members" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-notion-text font-medium">Membros do workspace</h3>
                {(isOwner || isAdmin) && (
                  <Button className="bg-white text-black hover:bg-white/90" onClick={() => setShowInviteModal(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Convidar
                  </Button>
                )}
              </div>

              <ScrollArea className="h-[300px] border border-notion-border rounded-md">
                <div className="p-2 space-y-1">
                  {currentWorkspace.members.map((member) => {
                    const isCurrentUser = member.userId === user?.id;
                    const isMemberOwner = member.userId === currentWorkspace.ownerId;

                    return (
                      <div key={member.userId} className="flex items-center justify-between p-2 hover:bg-notion-hover rounded">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 bg-notion-hover rounded-full flex items-center justify-center overflow-hidden">
                            {member.user?.avatar_url ? (
                              <img src={member.user.avatar_url} alt={member.user.name} className="h-full w-full object-cover" />
                            ) : (
                              <User className="h-4 w-4 text-notion-muted" />
                            )}
                          </div>
                          <div>
                            <p className="text-notion-text font-medium">
                              {member.user ? renderProfileName(member.user) : "Usuário"}
                              {isCurrentUser && <span className="text-notion-muted text-xs ml-2">(Você)</span>}
                              {isMemberOwner && <span className="bg-yellow-500/20 text-yellow-700 text-xs px-1 rounded ml-2">Proprietário</span>}
                            </p>
                            <p className="text-notion-muted text-xs">{member.user?.email || ""}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {!isMemberOwner && (
                            <>
                              {isAdmin && !isCurrentUser && (
                                <Select
                                  value={member.role}
                                  onValueChange={(value) => handleRoleChange(member.userId, value as WorkspaceMember["role"])}
                                >
                                  <SelectTrigger className="h-8 w-28 bg-notion-page border-notion-border text-notion-text text-xs">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent className="bg-notion-sidebar border-notion-border min-w-[8rem]">
                                    <SelectItem value="admin">Administrador</SelectItem>
                                    <SelectItem value="member">Membro</SelectItem>
                                    <SelectItem value="viewer">Visualizador</SelectItem>
                                  </SelectContent>
                                </Select>
                              )}
                              {!isAdmin && (
                                <span className="text-notion-muted text-xs px-2 py-1 bg-notion-hover rounded">
                                  {member.role === "admin" ? "Administrador" : member.role === "member" ? "Membro" : "Visualizador"}
                                </span>
                              )}

                              {isAdmin && !isCurrentUser && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-notion-muted hover:text-red-500 hover:bg-red-500/10"
                                  onClick={() => handleRemoveMember(member.userId)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })}

                  {pendingInvitations.length > 0 && (
                    <>
                      <Separator className="my-2 bg-notion-border" />
                      <div className="py-1">
                        <h4 className="text-xs font-medium text-notion-muted px-2 py-1">CONVITES PENDENTES</h4>
                      </div>

                      {pendingInvitations.map((invitation) => (
                        <div
                          key={invitation.id}
                          className="flex items-center justify-between p-2 hover:bg-notion-hover rounded border border-dashed border-notion-border"
                        >
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-8 bg-notion-hover rounded-full flex items-center justify-center overflow-hidden">
                              <Mail className="h-4 w-4 text-notion-muted" />
                            </div>
                            <div>
                              <p className="text-notion-text font-medium flex items-center">
                                {invitation.email}
                                <span className="bg-blue-500/20 text-blue-700 text-xs px-1 rounded ml-2">Pendente</span>
                              </p>
                              <p className="text-notion-muted text-xs">
                                {invitation.role === "admin" ? "Administrador" : invitation.role === "member" ? "Membro" : "Visualizador"}
                              </p>
                            </div>
                          </div>

                          {isAdmin && (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 text-notion-muted hover:text-notion-text text-xs"
                                onClick={() => resendInvitation(invitation)}
                              >
                                Reenviar
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-notion-muted hover:text-red-500 hover:bg-red-500/10"
                                onClick={() => cancelInvitation(invitation.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                    </>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      <InviteMemberModal isOpen={showInviteModal} onClose={() => setShowInviteModal(false)} />
    </>
  );
};
