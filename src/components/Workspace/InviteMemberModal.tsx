import React, { useState, useEffect } from "react";
import { useWorkspace } from "../../contexts/WorkspaceContext";
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter, DialogDescription } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { WorkspaceMember, InvitationData } from "../../types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface InviteMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const InviteMemberModal = ({ isOpen, onClose }: InviteMemberModalProps) => {
  const { currentWorkspace, inviteMember } = useWorkspace();
  const [email, setEmail] = useState("");
  const [role, setRole] = useState<WorkspaceMember["role"]>("member");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [pendingInvitations, setPendingInvitations] = useState<InvitationData[]>([]);
  const [refreshInvites, setRefreshInvites] = useState(0);

  useEffect(() => {
    if (currentWorkspace && isOpen) {
      fetchPendingInvitations();
    }
  }, [currentWorkspace, isOpen, refreshInvites]);

  const fetchPendingInvitations = async () => {
    if (!currentWorkspace) return;

    try {
      console.log("Fetching pending invitations for workspace:", currentWorkspace.id);

      const { data, error } = await supabase.from("workspace_invitations").select("*").eq("workspace_id", currentWorkspace.id).eq("accepted", false);

      if (error) {
        console.error("Error fetching invitations:", error);
        toast.error("Erro ao exibir usuários convidados");
        return;
      }

      console.log("Fetched invitations:", data);

      if (data) {
        // Convert string role values to expected WorkspaceMember["role"] type
        const typedData = data.map((item) => ({
          ...item,
          role: item.role as WorkspaceMember["role"],
        }));

        setPendingInvitations(typedData);
      }
    } catch (err) {
      console.error("Error fetching invitations:", err);
      toast.error("Erro ao exibir usuários convidados");
    }
  };

  const handleInvite = async () => {
    if (!email) {
      setError("Por favor, insira um email");
      return;
    }

    if (!currentWorkspace) {
      setError("Nenhum workspace selecionado");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      console.log("Inviting member:", email, "with role:", role);
      const result = await inviteMember(currentWorkspace.id, email, role);

      if (result) {
        setEmail("");
        setRole("member");
        setRefreshInvites((prev) => prev + 1);
      }
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para abrir checkout para licenças adicionais
  const handleUpgradePlan = () => {
    // Abrir o modal de assinatura
    window.dispatchEvent(new CustomEvent("open-subscription-modal"));
    onClose(); // Fechar o modal atual
  };

  const cancelInvitation = async (invitationId: string) => {
    try {
      console.log("Cancelling invitation:", invitationId);

      const { error } = await supabase.from("workspace_invitations").delete().eq("id", invitationId);

      if (error) {
        console.error("Error canceling invitation:", error);
        throw error;
      }

      toast.success("Convite cancelado com sucesso");
      setRefreshInvites((prev) => prev + 1);
    } catch (err) {
      console.error("Error canceling invitation:", err);
      toast.error("Erro ao cancelar convite");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="bg-notion-sidebar border-notion-border text-notion-text">
        <DialogHeader>
          <DialogTitle className="text-notion-text">Convidar membro</DialogTitle>
          <DialogDescription>Convide novos membros para o seu workspace</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && <div className="bg-red-500/10 border border-red-500/50 text-red-500 p-3 rounded text-sm">{error}</div>}

          <div className="space-y-2">
            <Label htmlFor="email" className="text-notion-text">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-notion-page border-notion-border text-notion-text"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role" className="text-notion-text">
              Papel
            </Label>
            <Select value={role} onValueChange={(value) => setRole(value as WorkspaceMember["role"])}>
              <SelectTrigger className="bg-notion-page border-notion-border text-notion-text">
                <SelectValue placeholder="Selecione um papel" />
              </SelectTrigger>
              <SelectContent className="bg-notion-sidebar border-notion-border">
                <SelectItem value="admin">Administrador</SelectItem>
                <SelectItem value="member">Membro</SelectItem>
                <SelectItem value="viewer">Visualizador</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-notion-muted mt-1">
              {role === "admin" && "Pode gerenciar membros e todas as páginas."}
              {role === "member" && "Pode criar e editar páginas."}
              {role === "viewer" && "Pode apenas visualizar páginas."}
            </p>
          </div>

          {pendingInvitations.length > 0 && (
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Convites pendentes</h3>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {pendingInvitations.map((invite) => (
                  <div key={invite.id} className="flex items-center justify-between p-2 bg-notion-hover rounded text-sm">
                    <div>
                      <p className="font-medium">{invite.email}</p>
                      <p className="text-xs text-notion-muted">
                        Papel: {invite.role === "admin" ? "Administrador" : invite.role === "member" ? "Membro" : "Visualizador"}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" className="text-notion-muted hover:text-red-500" onClick={() => cancelInvitation(invite.id)}>
                      Cancelar
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose} className="border-notion-border text-notion-text hover:bg-notion-hover">
            Cancelar
          </Button>
          <Button onClick={handleInvite} disabled={isLoading} className="bg-white text-black hover:bg-white/90">
            {isLoading ? "Convidando..." : "Convidar"}
          </Button>
          <Button onClick={handleUpgradePlan} variant="outline" className="border-primary text-primary hover:bg-primary/10">
            Ver planos e licenças
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
