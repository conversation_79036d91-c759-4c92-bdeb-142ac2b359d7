import React, { useState, lazy, Suspense, useEffect } from "react";
import { Input } from "../ui/input";
import { Plus, Search, X, Trash2, Save, XCircle } from "lucide-react";
import { useWorkspace } from "../../contexts/WorkspaceContext";
import { useSubscription } from "@/contexts/SubscriptionProvider";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import dayjs from "dayjs";
import { useDebounce } from "../../hooks/useDebounce";
import { BlockType } from "@/types";

// Use React's lazy loading instead of Next.js dynamic import
const EmojiPicker = lazy(() => import("emoji-picker-react"));

interface PageHeaderProps {
  hasLimits?: boolean;
  onActionWithCheck?: (action: () => void) => void;
}

export const PageHeader = ({ hasLimits = false, onActionWithCheck }: PageHeaderProps) => {
  const { currentPage, updatePage, pages, setCurrentPage, deletePage, lastSaved } = useWorkspace();
  const { hasReachedLimit, isAdmin, isSubscribed } = useSubscription();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<typeof pages>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [showBlockTypeMenu, setShowBlockTypeMenu] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const [localTitle, setLocalTitle] = useState<string>("");

  // Use either the prop-based hasLimits or the subscription-based isReadOnly check
  const isReadOnly = false; // Permitir edição independente do status da assinatura

  useEffect(() => {
    if (currentPage) {
      setLocalTitle(currentPage.title || "");
      setHasChanges(false); // Reset changes when page changes
    }
  }, [currentPage?.id, currentPage?.title]);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isReadOnly) {
      setLocalTitle(e.target.value);
      setHasChanges(e.target.value !== currentPage?.title);
    }
  };

  const handleEmojiSelect = (emoji: { emoji: string }) => {
    if (currentPage && !isReadOnly) {
      updatePage(currentPage.id, { icon: emoji.emoji });
      setShowEmojiPicker(false);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);

    if (!term) {
      setSearchResults([]);
      return;
    }

    const results = pages.filter((page) => page.title?.toLowerCase().includes(term.toLowerCase()));

    setSearchResults(results);
  };

  const handleResultClick = (pageId: string) => {
    const page = pages.find((p) => p.id === pageId);
    if (page) {
      setCurrentPage(page);
      setIsSearching(false);
      setSearchTerm("");
      setSearchResults([]);
    }
  };

  const handleRemoveIcon = () => {
    if (currentPage && !isReadOnly) {
      updatePage(currentPage.id, { icon: null });
    }
  };

  // Adicionar novo bloco do tipo selecionado após o último bloco
  const handleAddBlock = (blockType: BlockType = "text") => {
    // Se temos limites e a função de verificação, use-a
    if (hasLimits && onActionWithCheck) {
      onActionWithCheck(() => {
        // Este código só será executado se o usuário for capaz
        window.dispatchEvent(new CustomEvent("add-block", { detail: { type: blockType } }));
        setShowBlockTypeMenu(false);
        setHasChanges(true);
      });
    } else if (!isReadOnly) {
      // Comportamento normal se não tiver limitação
      window.dispatchEvent(new CustomEvent("add-block", { detail: { type: blockType } }));
      setShowBlockTypeMenu(false);
      setHasChanges(true); // Mark as changed when adding blocks
    }
  };

  // Botão salvar manual - Agora salva Título E dispara evento para Blocos
  const handleManualSave = async () => {
    if (!currentPage || isReadOnly || isSaving || !hasChanges) return;

    // Se temos limites e a função de verificação, use-a
    if (hasLimits && onActionWithCheck) {
      onActionWithCheck(async () => {
        await performSave();
      });
    } else {
      await performSave();
    }
  };

  const performSave = async () => {
    setIsSaving(true);
    console.log("Manual save: Saving title...");

    try {
      // Salvar o título diretamente (sem toast, a menos que falhe)
      await updatePage(currentPage.id, { title: localTitle });
      console.log("Title saved successfully.");

      // Disparar evento para PageEditor salvar os blocos
      console.log("Manual save: Dispatching save-page event for blocks...");
      window.dispatchEvent(new CustomEvent("save-page"));

      // Reset changes state
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving title manually:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // Listen for content changes from the editor
  useEffect(() => {
    const handleContentChange = () => {
      setHasChanges(true);
    };

    window.addEventListener("content-changed", handleContentChange);

    return () => {
      window.removeEventListener("content-changed", handleContentChange);
    };
  }, []);

  if (!currentPage) {
    return null;
  }

  const blockTypes = [
    { type: "text", label: "Texto" },
    { type: "heading_1", label: "Título 1" },
    { type: "heading_2", label: "Título 2" },
    { type: "heading_3", label: "Título 3" },
    { type: "bulleted_list", label: "Lista com marcadores" },
    { type: "numbered_list", label: "Lista numerada" },
    { type: "todo", label: "Lista de tarefas" },
    { type: "toggle", label: "Toggle" },
    { type: "code", label: "Bloco de código" },
    { type: "image", label: "Imagem" },
    { type: "callout", label: "Destaque" },
    { type: "table", label: "Tabela" },
    { type: "columns", label: "Colunas" },
  ];

  return (
    <div className="px-4 py-4 border-b border-notion-border flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="relative">
          <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
            <PopoverTrigger asChild>
              <button
                className={`w-8 h-8 flex items-center justify-center text-2xl ${
                  isReadOnly ? "cursor-default" : "hover:bg-notion-hover cursor-pointer"
                } rounded`}
                disabled={isReadOnly}
              >
                {currentPage.icon || "📄"}
              </button>
            </PopoverTrigger>
            {!isReadOnly && (
              <PopoverContent className="w-full p-0 border-notion-border" align="start">
                <Suspense fallback={<div className="p-4 text-center">Carregando...</div>}>
                  <EmojiPicker onEmojiClick={handleEmojiSelect} width={320} height={400} />
                </Suspense>
              </PopoverContent>
            )}
          </Popover>
          {currentPage.icon && !isReadOnly && (
            <button
              onClick={handleRemoveIcon}
              className="absolute -top-1 -right-1 bg-notion-sidebar rounded-full p-0.5 text-notion-muted hover:text-red-500"
              title="Remover ícone"
            >
              <XCircle className="w-4 h-4" />
            </button>
          )}
        </div>
        <Input
          className="border-none bg-transparent text-xl font-bold w-auto flex-grow focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-notion-muted"
          placeholder="Sem título"
          value={localTitle}
          onChange={handleTitleChange}
          readOnly={isReadOnly}
        />
      </div>
      <div className="flex items-center gap-3">
        {lastSaved && (
          <span className="text-xs text-notion-muted">
            Última alteração salva às {lastSaved.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit", second: "2-digit" })}
          </span>
        )}
        <Popover open={showBlockTypeMenu} onOpenChange={setShowBlockTypeMenu}>
          <PopoverTrigger asChild>
            <button
              className="p-2 text-notion-muted hover:text-notion-text hover:bg-notion-hover rounded-md"
              title="Adicionar bloco"
              disabled={isReadOnly}
            >
              <Plus className="w-4 h-4" />
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-60 p-0">
            <div className="py-2 max-h-64 overflow-auto">
              <div className="px-3 py-1 text-xs text-notion-muted border-b border-notion-border">TIPOS DE BLOCO</div>
              {blockTypes.map((block) => (
                <div
                  key={block.type}
                  className="flex items-center px-3 py-1.5 hover:bg-notion-hover cursor-pointer text-notion-text"
                  onClick={() => handleAddBlock(block.type as BlockType)}
                  role="button"
                >
                  <div>{block.label}</div>
                </div>
              ))}
            </div>
          </PopoverContent>
        </Popover>
        <button
          className={`flex items-center gap-2 bg-notion-sidebar border border-notion-border text-notion-text px-4 py-2 rounded transition ${
            isSaving ? "opacity-60 cursor-not-allowed" : ""
          } ${!hasChanges ? "opacity-50 cursor-not-allowed" : "hover:bg-notion-hover"}`}
          onClick={handleManualSave}
          disabled={isSaving || !hasChanges}
        >
          <Save className="w-4 h-4" />
          {isSaving ? "Salvando..." : "Salvar"}
        </button>
      </div>
    </div>
  );
};
