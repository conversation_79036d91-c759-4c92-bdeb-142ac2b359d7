import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import releaseNotesData from "@/data/release-notes.json";
import { useReleaseNotifications } from "@/hooks/useReleaseNotifications";

interface ReleaseNotesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface Release {
  version: string;
  date: string;
  corrections: string[];
  features: string[];
}

export const ReleaseNotesModal: React.FC<ReleaseNotesModalProps> = ({ open, onOpenChange }) => {
  const [releases, setReleases] = useState<Release[]>([]);
  const { currentVersion, markVersionAsRead, hasUnreadNotifications, isLoaded } = useReleaseNotifications();

  useEffect(() => {
    // Só atualizar após carregar do localStorage
    if (!isLoaded) return;

    const allReleases = releaseNotesData.releases || [];

    // Se há notificações não lidas, mostrar apenas a versão mais recente
    // Caso contrário, mostrar todas as versões
    if (hasUnreadNotifications) {
      setReleases(allReleases.slice(0, 1)); // Apenas a primeira (mais recente)
    } else {
      setReleases(allReleases); // Todas as versões
    }
  }, [hasUnreadNotifications, isLoaded]);

  // Função para fechar o modal e marcar a versão atual como lida
  const handleClose = () => {
    markVersionAsRead(currentVersion);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isLoaded && hasUnreadNotifications ? `Nova versão ${currentVersion} disponível!` : "Notas de versão"}</DialogTitle>
          <DialogDescription>
            {isLoaded && hasUnreadNotifications
              ? "Confira as novidades e melhorias da versão mais recente"
              : "Acompanhe as novidades e correções do sistema"}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 max-h-[60vh] overflow-y-auto p-1">
          {releases.map((release) => (
            <div key={release.version} className="space-y-3 border-b border-gray-200 pb-4 last:border-0">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Versão {release.version}</h3>
                <span className="text-sm text-gray-500">{new Date(release.date).toLocaleDateString("pt-BR")}</span>
              </div>

              {release.corrections.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-gray-700">Correções:</h4>
                  <ul className="list-disc ml-5 text-sm space-y-1">
                    {release.corrections.map((correction, index) => (
                      <li key={index}>{correction}</li>
                    ))}
                  </ul>
                </div>
              )}

              {release.features.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-gray-700">Novas funcionalidades:</h4>
                  <ul className="list-disc ml-5 text-sm space-y-1">
                    {release.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button onClick={handleClose}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
