import React, { useState, useEffect, useRef, useCallback } from "react";
import { useWorkspace } from "../../contexts/WorkspaceContext";
import { useAuth } from "../../contexts/AuthContext";
import { useSubscription } from "../../contexts/SubscriptionProvider";
import { useUserPreferences } from "../../hooks/useUserPreferences";
import {
  Search,
  Plus,
  ChevronDown,
  ChevronRight,
  File,
  Settings,
  Mail,
  Bell,
  Trash,
  Move,
  MoreVertical,
  LayoutDashboard,
  Kanban,
  Info,
} from "lucide-react";
import { Button } from "../ui/button";
import { Page, WorkspaceMember } from "../../types";
import { ScrollArea } from "../ui/scroll-area";
import { Separator } from "../ui/separator";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../ui/dropdown-menu";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, <PERSON>alogFooter } from "../ui/dialog";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { Badge } from "../ui/badge";
import releaseNotesData from "@/data/release-notes.json";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Define tipos de conteúdo que podem ser exibidos
type ContentType = "page";

interface SidebarProps {
  onSelectContentType?: (type: ContentType) => void;
}

interface PendingInvitation {
  id: string;
  workspace_id: string;
  email: string;
  role: WorkspaceMember["role"];
  token: string;
  workspaces?: {
    name: string;
  };
}

interface MovePageDialogProps {
  isOpen: boolean;
  onClose: () => void;
  page: Page;
  pages: Page[];
  onMove: (pageId: string, newParentId: string | null) => void;
}

const MovePageDialog = ({ isOpen, onClose, page, pages, onMove }: MovePageDialogProps) => {
  const [selectedParentId, setSelectedParentId] = useState<string | null>(null);

  // Filter out the current page and its descendants to avoid cycles
  const getDescendantIds = (pageId: string, allPages: Page[]): string[] => {
    const directChildren = allPages.filter((p) => p.parentId === pageId).map((p) => p.id);
    const allDescendants = [...directChildren];

    directChildren.forEach((childId) => {
      allDescendants.push(...getDescendantIds(childId, allPages));
    });

    return allDescendants;
  };

  const descendants = getDescendantIds(page.id, pages);
  const eligibleParents = pages.filter((p) => p.id !== page.id && !descendants.includes(p.id));

  const renderPageOption = (parentPage: Page | null, depth = 0) => {
    const pageId = parentPage?.id;
    const pageChildren = pageId ? pages.filter((p) => p.parentId === pageId) : pages.filter((p) => !p.parentId);

    return (
      <div key={pageId || "root"} className="space-y-1">
        {parentPage && (
          <div
            className={`flex items-center py-1 px-2 rounded cursor-pointer ${
              selectedParentId === pageId ? "bg-notion-hover" : "hover:bg-notion-hover/50"
            }`}
            onClick={() => setSelectedParentId(pageId)}
            style={{ paddingLeft: `${depth * 12 + 8}px` }}
          >
            <div className="w-4 h-4 mr-2 flex items-center justify-center text-notion-muted">
              {parentPage.icon || <File className="h-3.5 w-3.5" />}
            </div>
            <span className="text-sm truncate">{parentPage.title || "Sem título"}</span>
          </div>
        )}

        {pageChildren
          .filter((childPage) => childPage.id !== page.id && !descendants.includes(childPage.id))
          .map((childPage) => renderPageOption(childPage, depth + 1))}
      </div>
    );
  };

  const handleMove = () => {
    onMove(page.id, selectedParentId);
    onClose();
  };

  console.log("page.icon", page.icon);
  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="bg-notion-sidebar border-notion-border text-notion-text">
        <DialogHeader>
          <DialogTitle className="text-notion-text">Mover página</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 my-4">
          <div className="bg-notion-hover p-2 rounded flex items-center">
            <div className="w-4 h-4 mr-2 flex items-center justify-center text-notion-muted">{page.icon || <File className="h-3.5 w-3.5" />}</div>
            <span className="text-sm font-medium">{page.title || "Sem título"}</span>
          </div>

          <div>
            <p className="text-sm text-notion-muted mb-2">Selecione o novo local:</p>

            <div
              className={`flex items-center py-1 px-2 mb-2 rounded cursor-pointer ${
                !selectedParentId ? "bg-notion-hover" : "hover:bg-notion-hover/50"
              }`}
              onClick={() => setSelectedParentId(null)}
            >
              <span className="text-sm font-medium">Raiz do workspace</span>
            </div>

            <ScrollArea className="h-60">
              <div className="space-y-1">
                {pages.filter((p) => !p.parentId && p.id !== page.id && !descendants.includes(p.id)).map((rootPage) => renderPageOption(rootPage))}
              </div>
            </ScrollArea>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} className="border-notion-border text-notion-text hover:bg-notion-hover">
            Cancelar
          </Button>
          <Button onClick={handleMove} className="bg-white text-black hover:bg-white/90">
            Mover
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const Sidebar = ({ onSelectContentType }: SidebarProps) => {
  const { user, profile } = useAuth();
  const { workspaces, currentWorkspace, pages, currentPage, switchWorkspace, createWorkspace, createPage, deletePage, setCurrentPage, updatePage } =
    useWorkspace();
  const { isAdmin, isSubscribed, trialEndDate } = useSubscription();
  const { sidebarWidth, updateSidebarWidth } = useUserPreferences(user?.id);

  const [expanded, setExpanded] = useState<Record<string, boolean>>({});
  const [showNewWorkspaceInput, setShowNewWorkspaceInput] = useState(false);
  const [newWorkspaceName, setNewWorkspaceName] = useState("");
  const [pendingInvitations, setPendingInvitations] = useState<PendingInvitation[]>([]);
  const [movePageDialogOpen, setMovePageDialogOpen] = useState(false);
  const [pageToMove, setPageToMove] = useState<Page | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Obter a versão atual do aplicativo do arquivo release-notes.json
  const appVersion = releaseNotesData.releases && releaseNotesData.releases.length > 0 ? releaseNotesData.releases[0].version : "1.0.0";

  useEffect(() => {
    if (user) {
      fetchPendingInvitations();
    }
  }, [user]);

  const fetchPendingInvitations = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("workspace_invitations")
        .select(
          `
          id,
          workspace_id,
          email,
          role,
          token,
          workspaces:workspace_id (name)
        `
        )
        .eq("email", user.email)
        .eq("accepted", false);

      if (error) {
        console.error("Error fetching pending invitations:", error);
        return;
      }

      const typedData = data
        ? data.map((item) => ({
            ...item,
            role: item.role as WorkspaceMember["role"],
          }))
        : [];

      setPendingInvitations(typedData);
    } catch (err) {
      console.error("Error fetching pending invitations:", err);
    }
  };

  const acceptInvitation = async (invitationId: string, token: string) => {
    try {
      const { data, error } = await supabase.rpc("accept_workspace_invitation", { token_param: token });

      if (error) throw error;

      const response = data as any;
      if (response.success) {
        toast.success("Você foi adicionado ao workspace!");
        // Refresh workspaces to include the newly accepted one
        window.location.reload();
      } else {
        toast.error(response.message || "Convite inválido ou expirado");
      }
    } catch (error: any) {
      toast.error(error.message || "Erro ao aceitar convite");
    }
  };

  const toggleExpand = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setExpanded((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleCreatePage = () => {
    if (!currentWorkspace) return;

    // Verificar assinatura e limitações antes de criar página
    checkSubscriptionBeforeAction(() => {
      createPage("Nova página");
    });
  };

  const handleCreateWorkspace = () => {
    if (newWorkspaceName.trim()) {
      // Verificar assinatura e limitações antes de criar workspace
      checkSubscriptionBeforeAction(() => {
        createWorkspace(newWorkspaceName);
        setNewWorkspaceName("");
        setShowNewWorkspaceInput(false);
      });
    }
  };

  // Função para verificar assinatura antes de executar uma ação
  const checkSubscriptionBeforeAction = (action: () => void) => {
    // Se o usuário for admin, executar a ação sem verificar
    if (isAdmin) {
      action();
      return;
    }

    // Verificar se tem assinatura ativa ou está no período de teste
    if (isSubscribed) {
      // Usuário assinante pode fazer tudo
      action();
    } else if (trialEndDate && new Date(trialEndDate) > new Date()) {
      // Ainda está no período de teste
      action();
    } else {
      // Fora do período de teste e sem assinatura
      toast.error("Atualize o plano de pagamento para desbloquear os recursos ilimitados");
    }
  };

  const handleWorkspaceInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleCreateWorkspace();
    } else if (e.key === "Escape") {
      setShowNewWorkspaceInput(false);
      setNewWorkspaceName("");
    }
  };

  const handlePageClick = (page: Page) => {
    setCurrentPage(page);
  };

  const handleDeletePage = (page: Page, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    const confirmDelete = window.confirm(`Tem certeza que deseja excluir a página "${page.title || "Sem título"}"?`);
    if (confirmDelete) {
      deletePage(page.id);
    }
  };

  const handleMovePage = (page: Page, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    setPageToMove(page);
    setMovePageDialogOpen(true);
  };

  const handleMovePageConfirm = (pageId: string, newParentId: string | null) => {
    if (!currentWorkspace) return;

    updatePage(pageId, { parentId: newParentId });
  };

  const handleCreateSubpage = (parentPage: Page, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentWorkspace) return;

    createPage("Nova subpágina", parentPage.id);

    setExpanded((prev) => ({
      ...prev,
      [parentPage.id]: true,
    }));
  };

  const renderPages = (parentId: string | null) => {
    if (!currentWorkspace) return null;

    const filteredPages = pages.filter((page) => page.parentId === parentId && page.workspaceId === currentWorkspace.id && !page.isArchived);

    if (filteredPages.length === 0) return null;

    return filteredPages.map((page) => {
      const hasChildren = pages.some((p) => p.parentId === page.id && !p.isArchived);
      const isExpanded = expanded[page.id] || false;
      const isActive = currentPage?.id === page.id;

      return (
        <div key={page.id} className="group">
          <div
            className={`flex items-center py-1 px-2 rounded hover:bg-notion-hover cursor-pointer ${isActive ? "bg-notion-hover" : ""}`}
            onClick={() => handlePageClick(page)}
          >
            <div className="mr-1 w-5 flex items-center justify-center">
              {hasChildren ? (
                <button onClick={(e) => toggleExpand(page.id, e)} className="focus:outline-none text-notion-muted hover:text-notion-text">
                  {isExpanded ? <ChevronDown className="h-3.5 w-3.5" /> : <ChevronRight className="h-3.5 w-3.5" />}
                </button>
              ) : (
                <div className="flex items-center justify-center w-4 h-4">
                  {page.icon ? <span className="text-base">{page.icon}</span> : <File className="h-3.5 w-3.5 text-notion-muted" />}
                </div>
              )}
            </div>
            <div className="flex items-center flex-1 ml-1">
              <span className="truncate text-sm">{page.title || "Sem título"}</span>
            </div>

            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-notion-hover"
                onClick={(e) => handleCreateSubpage(page, e)}
              >
                <Plus className="h-3.5 w-3.5 text-notion-muted" />
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-notion-hover"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-3.5 w-3.5 text-notion-muted" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-notion-sidebar border-notion-border">
                  <DropdownMenuItem className="text-notion-text hover:bg-notion-hover cursor-pointer" onClick={(e) => handleMovePage(page, e)}>
                    <Move className="h-3.5 w-3.5 mr-2 text-notion-muted" />
                    <span>Mover página</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-red-500 hover:bg-red-500/10 hover:text-red-500 cursor-pointer"
                    onClick={(e) => handleDeletePage(page, e)}
                  >
                    <Trash className="h-3.5 w-3.5 mr-2" />
                    <span>Excluir página</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {isExpanded && hasChildren && <div className="pl-4">{renderPages(page.id)}</div>}
        </div>
      );
    });
  };

  const handleSelectContentType = (type: ContentType) => {
    if (onSelectContentType) {
      onSelectContentType(type);
    }
  };

  // Handle resize functionality
  const startResize = useCallback((e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  }, []);

  const resize = useCallback(
    (e: MouseEvent) => {
      if (!isResizing || !sidebarRef.current) return;
      
      const newWidth = e.clientX;
      if (newWidth >= 240 && newWidth <= 480) {
        updateSidebarWidth(newWidth);
      }
    },
    [isResizing, updateSidebarWidth]
  );

  const stopResize = useCallback(() => {
    setIsResizing(false);
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', resize);
      document.addEventListener('mouseup', stopResize);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    } else {
      document.removeEventListener('mousemove', resize);
      document.removeEventListener('mouseup', stopResize);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }

    return () => {
      document.removeEventListener('mousemove', resize);
      document.removeEventListener('mouseup', stopResize);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing, resize, stopResize]);

  return (
    <div 
      ref={sidebarRef}
      className="relative h-screen bg-notion-sidebar flex flex-col border-r border-notion-border overflow-hidden"
      style={{ width: `${sidebarWidth}px`, minWidth: '240px', maxWidth: '480px' }}
    >
      <div className="p-2">
        <Popover>
          <PopoverTrigger asChild>
            {currentWorkspace && (
              <div className="flex items-center space-x-2 text-notion-text w-full text-sm cursor-pointer hover:bg-notion-hover p-1.5 rounded-md">
                <div className="w-4 h-4 flex items-center justify-center bg-red-600 text-white rounded">
                  {currentWorkspace.icon || currentWorkspace.name.charAt(0)}
                </div>
                <span className="font-medium truncate flex-1">{currentWorkspace.name}</span>
                <div className="flex items-center space-x-1">
                  {pendingInvitations.length > 0 && (
                    <Badge variant="outline" className="bg-blue-500 text-white text-xs h-5 px-1">
                      {pendingInvitations.length}
                    </Badge>
                  )}
                  <ChevronDown className="h-3.5 w-3.5 text-notion-muted flex-shrink-0" />
                </div>
              </div>
            )}
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0 bg-notion-page border-notion-border">
            <div className="py-1">
              <div className="px-2 py-1.5 text-xs font-medium text-notion-muted">WORKSPACES</div>
              {workspaces.map((workspace) => (
                <div
                  key={workspace.id}
                  className={`flex items-center px-2 py-1.5 text-sm cursor-pointer ${
                    workspace.id === currentWorkspace?.id ? "bg-notion-hover text-notion-text" : "text-notion-text hover:bg-notion-hover"
                  }`}
                  onClick={() => switchWorkspace(workspace.id)}
                >
                  <div className="w-4 h-4 mr-2 flex items-center justify-center bg-red-600 text-white rounded text-xs">
                    {workspace.icon || workspace.name.charAt(0)}
                  </div>
                  <span className="truncate">{workspace.name}</span>
                </div>
              ))}

              {pendingInvitations.length > 0 && (
                <>
                  <Separator className="my-2 bg-notion-border" />
                  <div className="px-2 py-1.5 text-xs font-medium text-notion-muted flex items-center">
                    <Mail className="h-3 w-3 mr-1" /> CONVITES PENDENTES
                  </div>

                  {pendingInvitations.map((invitation) => (
                    <div key={invitation.id} className="px-2 py-1.5">
                      <div className="flex flex-col mb-1 bg-notion-hover p-2 rounded-md text-xs">
                        <div className="flex items-center justify-between">
                          <span className="font-medium truncate">{invitation.workspaces?.name || "Workspace"}</span>
                          <Badge className="ml-1 text-[10px] h-4 px-1 bg-blue-500 text-white">
                            {invitation.role === "admin" ? "Admin" : invitation.role === "member" ? "Membro" : "Visualizador"}
                          </Badge>
                        </div>
                        <div className="flex space-x-2 mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-[10px] h-6 py-0 px-2 flex-1 bg-green-50 text-green-700 hover:bg-green-100 hover:text-green-800 border-green-200"
                            onClick={() => acceptInvitation(invitation.id, invitation.token)}
                          >
                            Aceitar
                          </Button>
                          <Button variant="ghost" size="sm" className="text-[10px] h-6 py-0 px-2 flex-1 text-notion-muted hover:text-notion-text">
                            Recusar
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              )}

              {showNewWorkspaceInput ? (
                <div className="px-2 py-1.5">
                  <input
                    type="text"
                    className="w-full px-2 py-1 text-sm border border-notion-border rounded bg-notion-page text-notion-text"
                    placeholder="Nome do workspace"
                    value={newWorkspaceName}
                    onChange={(e) => setNewWorkspaceName(e.target.value)}
                    onKeyDown={handleWorkspaceInputKeyDown}
                    autoFocus
                  />
                  <div className="flex gap-2 mt-1">
                    <Button size="sm" className="text-xs h-6 py-0" onClick={handleCreateWorkspace}>
                      Criar
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-xs h-6 py-0"
                      onClick={() => {
                        setShowNewWorkspaceInput(false);
                        setNewWorkspaceName("");
                      }}
                    >
                      Cancelar
                    </Button>
                  </div>
                </div>
              ) : (
                <div
                  className="flex items-center px-2 py-1.5 text-sm text-notion-muted hover:text-notion-text hover:bg-notion-hover cursor-pointer"
                  onClick={() => setShowNewWorkspaceInput(true)}
                >
                  <Plus className="h-3.5 w-3.5 mr-2" />
                  <span>Adicionar workspace</span>
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="px-2 space-y-1">
        <Button
          variant="ghost"
          size="sm"
          className="w-full text-notion-muted justify-start text-sm font-normal hover:bg-notion-hover hover:text-notion-text"
          onClick={handleCreatePage}
        >
          <Plus className="h-3.5 w-3.5 mr-2" />
          Nova página
        </Button>
      </div>

      <Separator className="my-2 bg-notion-border" />

      <ScrollArea className="flex-1 px-2">
        <div className="space-y-1">
          <div className="space-y-[2px]">{renderPages(null)}</div>
        </div>
      </ScrollArea>

      <div className="p-2 mt-auto border-t border-notion-border">
        {profile && (
          <Button variant="ghost" size="sm" className="w-full justify-start text-sm font-medium text-notion-text hover:bg-notion-hover">
            <div className="w-6 h-6 rounded-full bg-notion-hover overflow-hidden flex items-center justify-center text-xs mr-2">
              {profile.avatar_url ? (
                <img src={profile.avatar_url} alt={profile.name} className="w-full h-full object-cover" />
              ) : (
                profile.name.charAt(0)
              )}
            </div>
            {profile.name}
            {pendingInvitations.length > 0 && (
              <Badge variant="outline" className="ml-auto bg-blue-500 text-white text-xs">
                {pendingInvitations.length}
              </Badge>
            )}
          </Button>
        )}

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center justify-center text-xs text-notion-muted mt-2 py-1">
                <Info className="h-3 w-3 mr-1" />
                <span>v{appVersion}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>E-Manager versão {appVersion}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {pageToMove && (
        <MovePageDialog
          isOpen={movePageDialogOpen}
          onClose={() => setMovePageDialogOpen(false)}
          page={pageToMove}
          pages={pages}
          onMove={handleMovePageConfirm}
        />
      )}
      
      {/* Resize handle */}
      <div 
        className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-notion-border/50 transition-colors"
        onMouseDown={startResize}
      />
    </div>
  );
};
