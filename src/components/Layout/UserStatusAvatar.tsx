import React, { useState } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { Bell } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ReleaseNotesModal } from "./ReleaseNotesModal";
import { useReleaseNotifications } from "@/hooks/useReleaseNotifications";

export const UserStatusAvatar = () => {
  const { user, logout, profile } = useAuth();
  const navigate = useNavigate();
  const [showReleaseNotes, setShowReleaseNotes] = useState(false);
  const { hasUnreadNotifications, isLoaded } = useReleaseNotifications();

  return (
    <>
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Avatar>
              {profile?.avatar_url ? (
                <AvatarImage src={profile?.avatar_url} alt={profile?.name} />
              ) : (
                <AvatarFallback>{profile?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
              )}
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 mr-2">
            <DropdownMenuLabel>{profile?.name}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {/* <DropdownMenuItem onClick={() => navigate('/profile')}>Perfil</DropdownMenuItem> */}
            <DropdownMenuItem onClick={logout}>Sair</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant="ghost" size="icon" className="rounded-full relative" onClick={() => setShowReleaseNotes(true)}>
          <Bell className="h-5 w-5" />
          {isLoaded && hasUnreadNotifications && <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500" />}
        </Button>
      </div>

      <ReleaseNotesModal open={showReleaseNotes} onOpenChange={setShowReleaseNotes} />
    </>
  );
};
