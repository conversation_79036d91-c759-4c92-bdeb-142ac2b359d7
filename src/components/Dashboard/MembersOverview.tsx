import React, { useEffect, useState } from "react";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Profile, InvitationData, WorkspaceMember } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { Trash2, UserX } from "lucide-react";
import { toast } from "sonner";
import { useMemberOperations } from "@/hooks/useMemberOperations";
import { useAuth } from "@/contexts/AuthContext";
import { isProfileAccepted, isProfileInactive, isProfilePending, isInviteProfile } from "@/utils/profileUtils";

interface MembersOverviewProps {
  onManagePermissions?: (member: Profile) => void;
  onManageInvitation?: (invitation: InvitationData) => void;
}

export const MembersOverview: React.FC<MembersOverviewProps> = ({ onManagePermissions, onManageInvitation }) => {
  const { currentWorkspace, refreshWorkspaces, getAllWorkspaceUsers } = useWorkspace();
  const { removeMember } = useMemberOperations(useAuth().user, () => {});
  const [isLoading, setIsLoading] = useState(false);
  const [allWorkspaceUsers, setAllWorkspaceUsers] = useState<Profile[]>([]);

  useEffect(() => {
    if (currentWorkspace) {
      fetchAllUsers();
    }
  }, [currentWorkspace]);

  const fetchAllUsers = async () => {
    if (!currentWorkspace) return;
    try {
      setIsLoading(true);
      const users = await getAllWorkspaceUsers(currentWorkspace.id);
      setAllWorkspaceUsers(users);
    } catch (error) {
      console.error("Erro ao buscar todos os usuários:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!currentWorkspace) return;

    setIsLoading(true);
    try {
      const { error } = await supabase.from("workspace_invitations").delete().eq("id", invitationId);

      if (error) {
        console.error("Error canceling invitation:", error);
        toast.error("Falha ao cancelar o convite");
        return;
      }

      toast.success("Convite cancelado com sucesso");
      fetchAllUsers();
    } catch (err) {
      console.error("Error canceling invitation:", err);
      toast.error("Falha ao cancelar o convite");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (!currentWorkspace) return;

    setIsLoading(true);
    try {
      // Verificar se é um ID de convite ou de usuário normal
      if (userId.startsWith("invite_")) {
        // Se for convite, extrair o ID real do convite e cancelar
        const invitationId = userId.replace("invite_", "");
        await handleCancelInvitation(invitationId);
      } else {
        // Se for usuário regular, remove normalmente
        await removeMember(currentWorkspace.id, userId);
        await refreshWorkspaces();
      }

      // Atualizar a lista de usuários após remoção bem-sucedida
      await fetchAllUsers();
      toast.success("Membro removido com sucesso");
    } catch (err) {
      console.error("Error removing member:", err);
      toast.error("Falha ao remover o membro");
    } finally {
      setIsLoading(false);
    }
  };

  // Em um sistema real, essas informações seriam calculadas pelo backend
  const getMemberActivity = (memberId: string) => {
    const activities = ["Ativo agora", "Há 30 minutos", "Há 2 horas", "Há 1 dia", "Há 3 dias"];
    return activities[Math.floor(Math.random() * activities.length)];
  };

  const getMemberPages = (memberId: string) => {
    return Math.floor(Math.random() * 20);
  };

  if (!currentWorkspace) {
    return (
      <Card className="bg-notion-sidebar border-notion-border">
        <CardHeader>
          <CardTitle className="text-notion-text">Membros do Workspace</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-notion-muted text-sm">Sem dados disponíveis</p>
        </CardContent>
      </Card>
    );
  }

  const allMembers = [...(allWorkspaceUsers || [])];
  const hasMembersOrInvitations = allMembers.length > 0;

  return (
    <Card className="bg-notion-sidebar border-notion-border">
      <CardHeader>
        <CardTitle className="text-notion-text">Membros do Workspace</CardTitle>
      </CardHeader>
      <CardContent>
        {!hasMembersOrInvitations ? (
          <p className="text-notion-muted text-sm">Sem membros ou convites pendentes</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow className="border-notion-border">
                <TableHead className="text-notion-muted">Usuário</TableHead>
                <TableHead className="text-notion-muted">Cargo</TableHead>
                <TableHead className="text-notion-muted">Status</TableHead>
                <TableHead className="text-notion-muted text-right">Páginas criadas</TableHead>
                <TableHead className="text-notion-muted text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Membros do Workspace (ativos e inativos) */}
              {allMembers &&
                allMembers
                  .filter((member) => !isInviteProfile(member))
                  .map((member: Profile) => {
                    const isInactive = isProfileInactive(member);

                    return (
                      <TableRow
                        key={member.id}
                        className={`border-notion-border hover:bg-notion-hover cursor-pointer ${isInactive ? "opacity-70" : ""}`}
                        onClick={(e) => {
                          // Impedir que o clique no botão de remoção propague
                          const target = e.target as HTMLElement;
                          if (!target.closest("button")) {
                            onManagePermissions && onManagePermissions(member);
                          }
                        }}
                      >
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={
                                  member.avatar_url ||
                                  member.avatar ||
                                  `https://api.dicebear.com/7.x/initials/svg?seed=${member.name || member.email}`
                                }
                                alt={member.name || member.email}
                              />
                              <AvatarFallback>{(member.name || member.email || "").substring(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <span className="text-notion-text">{member.name || member.email || `Usuário ${member.id.substring(0, 8)}`}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-notion-hover border-notion-border text-notion-muted">
                            {member.role === "owner" ? "Proprietário" : member.role === "admin" ? "Administrador" : "Membro"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-notion-muted">
                          {isInactive ? (
                            <Badge className="bg-red-500/20 text-red-500 border-red-500/30">Inativo</Badge>
                          ) : (
                            getMemberActivity(member.id)
                          )}
                        </TableCell>
                        <TableCell className="text-right text-notion-text">{isInactive ? "-" : getMemberPages(member.id)}</TableCell>
                        <TableCell className="text-right">
                          {member.role !== "owner" && (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveMember(member.id);
                              }}
                              disabled={isLoading || member.role === "owner"}
                              className="text-red-500 hover:text-red-600 hover:bg-red-100/20"
                            >
                              <UserX size={16} />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}

              {/* Convites pendentes e aceitos */}
              {allMembers &&
                allMembers
                  .filter((member) => isInviteProfile(member))
                  .map((invitation: Profile) => {
                    const isPending = isProfilePending(invitation);
                    const isAccepted = isProfileAccepted(invitation);

                    return (
                      <TableRow
                        key={invitation.id}
                        className="border-notion-border hover:bg-notion-hover cursor-pointer"
                        onClick={(e) => {
                          // Impedir que o clique no botão de cancelamento propague
                          const target = e.target as HTMLElement;
                          if (!target.closest("button") && onManageInvitation) {
                            // Criar um objeto InvitationData para compatibilidade
                            const inviteData: InvitationData = {
                              id: invitation.id.replace("invite_", ""),
                              email: invitation.email,
                              role: invitation.role as WorkspaceMember["role"],
                              created_at: invitation.created_at?.toISOString() || "",
                              expires_at: "",
                              accepted: isAccepted,
                              token: "",
                              workspace_id: currentWorkspace?.id || "",
                              invited_by: "",
                            };
                            onManageInvitation(inviteData);
                          }
                        }}
                      >
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback>{invitation.email.substring(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <span className="text-notion-text">{invitation.email}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-notion-hover border-notion-border text-notion-muted">
                            {invitation.role === "owner" ? "Proprietário" : invitation.role === "admin" ? "Administrador" : "Membro"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {isAccepted ? (
                            <Badge className="bg-green-500/20 text-green-500 border-green-500/30">Convite aceito</Badge>
                          ) : (
                            <Badge className="bg-yellow-500/20 text-yellow-500 border-yellow-500/30">Convite pendente</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right text-notion-text">-</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveMember(invitation.id);
                            }}
                            disabled={isLoading}
                            className="text-red-500 hover:text-red-600 hover:bg-red-100/20"
                          >
                            <Trash2 size={16} />
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};
