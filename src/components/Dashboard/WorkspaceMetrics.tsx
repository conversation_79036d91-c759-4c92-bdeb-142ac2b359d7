
import React from "react";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { useAuth } from "@/contexts/AuthContext";
import { useTasks } from "@/contexts/TasksContext";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Users, FileText, Clock, Calendar } from "lucide-react";

export const WorkspaceMetrics = () => {
  const { currentWorkspace, pages, workspaceMembers } = useWorkspace();
  const { currentUser } = useAuth();
  const { tasks } = useTasks();

  const totalPages = pages?.length || 0;
  const totalMembers = workspaceMembers?.length || 0;
  const totalTasks = tasks.length;
  
  const completedTasks = tasks.filter(task => task.status === "concluido").length;
  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  
  // Calcular o último acesso (em um sistema real, isso viria do backend)
  const lastAccess = new Date().toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  if (!currentWorkspace) {
    return <div className="text-notion-muted">Selecione um workspace para ver as métricas.</div>;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card className="bg-notion-sidebar border-notion-border">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-notion-text text-md font-medium">Membros</CardTitle>
          <Users className="h-4 w-4 text-notion-muted" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-notion-text">{totalMembers}</div>
          <p className="text-xs text-notion-muted">Usuários com acesso ao workspace</p>
        </CardContent>
      </Card>

      <Card className="bg-notion-sidebar border-notion-border">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-notion-text text-md font-medium">Páginas</CardTitle>
          <FileText className="h-4 w-4 text-notion-muted" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-notion-text">{totalPages}</div>
          <p className="text-xs text-notion-muted">Total de páginas no workspace</p>
        </CardContent>
      </Card>

      <Card className="bg-notion-sidebar border-notion-border">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-notion-text text-md font-medium">Último acesso</CardTitle>
          <Clock className="h-4 w-4 text-notion-muted" />
        </CardHeader>
        <CardContent>
          <div className="text-md font-medium text-notion-text">{lastAccess}</div>
          <p className="text-xs text-notion-muted">Por {currentUser?.displayName || currentUser?.name || "você"}</p>
        </CardContent>
      </Card>

      <Card className="bg-notion-sidebar border-notion-border">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-notion-text text-md font-medium">Tarefas</CardTitle>
          <Calendar className="h-4 w-4 text-notion-muted" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-notion-text">{totalTasks}</div>
          <p className="text-xs text-notion-muted">{completedTasks} concluídas ({completionRate}%)</p>
        </CardContent>
      </Card>
    </div>
  );
};
