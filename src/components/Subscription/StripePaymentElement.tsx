import { useEffect, useState } from "react";
import { PaymentElement, useStripe, useElements, Elements } from "@stripe/react-stripe-js";
import { loadStripe, StripeError } from "@stripe/stripe-js";

// Inicializa o Stripe com a chave pública
// Chave de teste (para ambiente de desenvolvimento)
// const stripePromise = loadStripe("pk_test_51PkVaQIxqQbmbZWPatjl4a492UAEDJRcaKZCA6qchePR3YAHn7wD7sQlKwLcK9PYXswWuM0afvl8Kc4JP7V7hTO600xQkDjclU");

// Chave de produção (ambiente de produção)
const stripePromise = loadStripe("pk_live_51PkVaQIxqQbmbZWPJiE0vtb8MTOld4WRvyN8zmuXtNoWRSCYO6sfGcCjijeeBLrgwEkwKk4s2JQBCor1alUo17Ly002XmwsQA7");

interface StripePaymentElementProps {
  clientSecret: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

function CheckoutForm({ onSuccess, onError }: Omit<StripePaymentElementProps, "clientSecret">) {
  const stripe = useStripe();
  const elements = useElements();
  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!stripe) {
      return;
    }

    const clientSecret = new URLSearchParams(window.location.search).get("payment_intent_client_secret");

    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      switch (paymentIntent?.status) {
        case "succeeded":
          setMessage("Pagamento realizado com sucesso!");
          onSuccess?.();
          break;
        case "processing":
          setMessage("Seu pagamento está sendo processado.");
          break;
        case "requires_payment_method":
          setMessage("Seu pagamento não foi bem sucedido, tente novamente.");
          break;
        default:
          setMessage("Algo deu errado.");
          break;
      }
    });
  }, [stripe, onSuccess]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);

    try {
      // Confirmar Payment Intent para processar pagamento
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/settings/payment/success`,
        },
        redirect: "if_required",
      });

      if (error) {
        setMessage(error.message ?? "Algo deu errado.");
        const genericError = new Error(error.message ?? "Erro no pagamento");
        onError?.(genericError);
      } else if (paymentIntent && paymentIntent.status === "succeeded") {
        setMessage("Pagamento realizado com sucesso!");
        onSuccess?.();
      }
    } catch (error) {
      setMessage("Ocorreu um erro ao processar o pagamento.");
      onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <PaymentElement />

      {message && (
        <div className="mt-4 text-sm text-center">
          {message.includes("sucesso") ? <p className="text-green-500">{message}</p> : <p className="text-red-500">{message}</p>}
        </div>
      )}

      <button
        disabled={isLoading || !stripe || !elements}
        className={`w-full mt-6 bg-primary text-primary-foreground py-3 rounded-lg font-semibold transition
          ${isLoading || !stripe || !elements ? "opacity-50 cursor-not-allowed" : "hover:bg-primary/90"}`}
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            Processando...
          </div>
        ) : (
          "Pagar agora"
        )}
      </button>
    </form>
  );
}

export function StripePaymentElement({ clientSecret, onSuccess, onError }: StripePaymentElementProps) {
  const appearance = {
    theme: "flat" as const,
    variables: {
      colorPrimary: "hsl(var(--primary))",
      colorBackground: "transparent",
      colorText: "hsl(var(--foreground))",
      colorDanger: "hsl(var(--destructive))",
      fontFamily: "var(--font-sans)",
      spacingUnit: "4px",
      borderRadius: "8px",
    },
  };

  const options = {
    clientSecret,
    appearance,
  };

  return (
    <div className="w-full">
      {clientSecret && (
        <Elements stripe={stripePromise} options={options as any}>
          <CheckoutForm onSuccess={onSuccess} onError={onError} />
        </Elements>
      )}
    </div>
  );
}
