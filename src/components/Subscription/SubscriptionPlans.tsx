import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Check, Loader2, CreditCard } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useSubscription } from "@/contexts/SubscriptionProvider";
import { Badge } from "@/components/ui/badge";
import { loadStripe } from "@stripe/stripe-js";
// Removido StripePaymentElement pois agora usamos simulação simples

// Chave pública do Stripe
// Chave de teste (para ambiente de desenvolvimento)
// const stripePromise = loadStripe("pk_test_51PkVaQIxqQbmbZWPatjl4a492UAEDJRcaKZCA6qchePR3YAHn7wD7sQlKwLcK9PYXswWuM0afvl8Kc4JP7V7hTO600xQkDjclU");

// Chave de produção (ambiente de produção)
const stripePromise = loadStripe("pk_live_51PkVaQIxqQbmbZWPJiE0vtb8MTOld4WRvyN8zmuXtNoWRSCYO6sfGcCjijeeBLrgwEkwKk4s2JQBCor1alUo17Ly002XmwsQA7");

// IDs dos preços no Stripe
// IDs de teste (para ambiente de desenvolvimento)
// const PRICE_IDS = {
//   individual: "price_1PHxJ4ALvhA5CrMQMKv4Z1F0",
//   shared: "price_1PHxJTALvhA5CrMQfULx4xRQ",
//   business: "price_1PHxJkALvhA5CrMQBPe66nKB",
// };

// IDs de produção (ambiente de produção)
const PRICE_IDS = {
  individual: "price_1RREVbIxqQbmbZWPxURraZMM",
  shared: "price_1RREVhIxqQbmbZWPznPRIxZN",
  business: "price_1RREVoIxqQbmbZWPX8yDVRdX",
};

const plans = [
  {
    id: "individual",
    name: "Individual",
    price: "R$ 19,00",
    description: "Perfeito para uso pessoal",
    features: ["Workspaces ilimitados", "Upload de arquivos ilimitados", "Páginas ilimitadas", "Acesso a todos os recursos"],
    priceId: PRICE_IDS.individual,
  },
  {
    id: "shared",
    name: "Compartilhado",
    price: "R$ 49,00",
    description: "Ideal para pequenas equipes",
    features: ["Tudo do plano Individual", "Até 4 usuários", "R$ 20,00 por usuário adicional"],
    priceId: PRICE_IDS.shared,
  },
  {
    id: "business",
    name: "Empresarial",
    price: "R$ 99,00",
    description: "Para grandes equipes",
    features: ["Tudo do plano Compartilhado", "Até 10 usuários", "R$ 17,00 por usuário adicional"],
    priceId: PRICE_IDS.business,
  },
];

export const SubscriptionPlans = () => {
  const { isSubscribed, isAdmin, checkSubscription } = useSubscription();
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPlan, setCurrentPlan] = useState<string | null>(null);
  const [cancelingPlan, setCancelingPlan] = useState<boolean>(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<"select-plan" | "checkout">("select-plan");
  const [checkoutData, setCheckoutData] = useState<{ planId: string; planName: string; planPrice: string } | null>(null);
  const [cardData, setCardData] = useState({
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardName: "",
  });

  useEffect(() => {
    fetchCurrentPlan();
  }, []);

  const fetchCurrentPlan = async () => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) return;

      // Usar maybeSingle para evitar erro quando não houver registro
      const { data, error } = await supabase.from("subscribers").select("subscription_tier, subscribed").eq("user_id", session.user.id).maybeSingle();

      if (error) {
        console.error("Erro ao buscar plano:", error);
        return;
      }

      // Verificar se data existe e se a assinatura está ativa
      if (data && data.subscribed) {
        setCurrentPlan(data.subscription_tier);
      } else {
        setCurrentPlan(null);
      }
    } catch (error) {
      console.error("Erro ao buscar plano:", error);
    }
  };

  // Criar subscriber se não existir
  const ensureSubscriberExists = async (userId: string, email: string) => {
    try {
      // Verificar se já existe
      const { data, error } = await supabase.from("subscribers").select("id").eq("user_id", userId).maybeSingle();

      if (error) {
        console.error("Erro ao verificar subscriber:", error);
        return false;
      }

      // Se não existir, criar
      if (!data) {
        const { error: insertError } = await supabase.from("subscribers").insert({
          user_id: userId,
          email: email,
          subscribed: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (insertError) {
          console.error("Erro ao criar subscriber:", insertError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error("Erro ao garantir existência do subscriber:", error);
      return false;
    }
  };

  // Funções para formatar dados do cartão
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(" ");
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    if (v.length >= 2) {
      return v.substring(0, 2) + "/" + v.substring(2, 4);
    }
    return v;
  };

  const validateCardData = () => {
    if (!cardData.cardNumber || cardData.cardNumber.replace(/\s/g, "").length < 16) {
      return "Número do cartão deve ter 16 dígitos";
    }
    if (!cardData.expiryDate || cardData.expiryDate.length < 5) {
      return "Data de validade inválida";
    }
    if (!cardData.cvv || cardData.cvv.length < 3) {
      return "CVV deve ter pelo menos 3 dígitos";
    }
    if (!cardData.cardName || cardData.cardName.trim().length < 2) {
      return "Nome no cartão é obrigatório";
    }
    return null;
  };

  const isFormValid = () => {
    return validateCardData() === null;
  };

  // Função simplificada para simular assinatura (para demonstração)
  const simulateSubscription = async (planId: string, email: string) => {
    try {
      // Validar dados do cartão
      const validationError = validateCardData();
      if (validationError) {
        throw new Error(validationError);
      }

      // Simular um delay de processamento
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Para demonstração, vamos apenas marcar como assinado
      // Em produção, isso seria feito através do webhook do Stripe
      return true;
    } catch (error) {
      console.error("Erro ao simular assinatura:", error);
      throw error;
    }
  };

  const handlePlanSelection = async (planId: string, priceId: string) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedPlan(planId);

      // Se já tem uma assinatura ativa e está tentando mudar de plano, primeiro cancela
      if (isSubscribed && currentPlan && currentPlan !== planId) {
        await cancelSubscription();
      }

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para assinar um plano");
        return;
      }

      // Encontrar os dados do plano selecionado
      const plan = plans.find((p) => p.id === planId);
      if (!plan) {
        throw new Error("Plano não encontrado");
      }

      // Preparar dados para o checkout
      setCheckoutData({
        planId: planId,
        planName: plan.name,
        planPrice: plan.price,
      });

      // Ir para o step de checkout
      setCurrentStep("checkout");
    } catch (error) {
      console.error("Erro ao iniciar processo de pagamento:", error);
      setError(error.message || "Ocorreu um erro ao processar sua solicitação");
      toast.error("Erro ao processar assinatura. Tente novamente mais tarde.");
    } finally {
      setLoading(false);
    }
  };

  const handleCheckoutConfirm = async () => {
    if (!checkoutData) return;

    try {
      setLoading(true);
      setError(null);

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para assinar um plano");
        return;
      }

      // Atualizar o registro com o plano selecionado
      await ensureSubscriberExists(session.user.id, session.user.email);
      await supabase
        .from("subscribers")
        .update({
          subscription_tier: checkoutData.planId,
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", session.user.id);

      // Simular o processo de assinatura
      const success = await simulateSubscription(checkoutData.planId, session.user.email || "");

      if (success) {
        // Atualizar o registro de assinante
        await supabase
          .from("subscribers")
          .update({
            subscribed: true,
            subscription_tier: checkoutData.planId,
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", session.user.id);

        // Recarregar o plano atual
        await fetchCurrentPlan();

        // Atualizar o contexto de assinatura
        await checkSubscription();

        toast.success("Assinatura ativada com sucesso!");

        // Voltar para seleção de planos
        setCurrentStep("select-plan");
        setCheckoutData(null);
        setCardData({
          cardNumber: "",
          expiryDate: "",
          cvv: "",
          cardName: "",
        });
      } else {
        throw new Error("Falha ao processar assinatura");
      }
    } catch (error) {
      console.error("Erro ao processar pagamento:", error);
      setError(error.message || "Ocorreu um erro ao processar o pagamento");
      toast.error("Erro ao processar pagamento. Tente novamente.");
    } finally {
      setLoading(false);
    }
  };

  const cancelSubscription = async () => {
    try {
      setCancelingPlan(true);

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para cancelar a assinatura");
        return;
      }

      // Implementação simplificada - apenas marca como não assinado no banco
      const { error } = await supabase
        .from("subscribers")
        .update({
          subscribed: false,
          subscription_end: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          stripe_subscription_id: null,
        })
        .eq("user_id", session.user.id);

      if (error) {
        console.error("Erro ao cancelar assinatura:", error);
        toast.error("Não foi possível cancelar a assinatura");
        return;
      }

      // Atualizar o estado local
      setCurrentPlan(null);

      // Atualizar o contexto de assinatura
      await checkSubscription();

      toast.success("Assinatura cancelada com sucesso");
    } catch (error) {
      console.error("Error canceling subscription:", error);
      toast.error("Erro ao cancelar assinatura");
    } finally {
      setCancelingPlan(false);
    }
  };

  // Removido useEffect para processar retorno do Stripe pois agora usamos checkout interno

  const handleAdditionalLicenses = async (planId: string) => {
    try {
      setLoading(true);
      setError(null);

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        toast.error("Você precisa estar logado para adquirir licenças adicionais");
        return;
      }

      // Determinar o preço com base no plano
      const selectedPlanObj = plans.find((p) => p.id === planId);
      if (!selectedPlanObj) {
        throw new Error("Plano não encontrado");
      }

      // Converter o preço de string para número (por usuário adicional)
      let additionalLicensePrice = 0;
      if (planId === "shared") {
        additionalLicensePrice = 2000; // R$ 20,00
      } else if (planId === "business") {
        additionalLicensePrice = 1700; // R$ 17,00
      }

      // Usar diretamente o Stripe Checkout para licenças adicionais
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Não foi possível carregar o Stripe");
      }

      // Determinar o price ID correto para licenças adicionais
      let additionalPriceId;
      if (planId === "shared") {
        additionalPriceId = PRICE_IDS.additional_license_shared;
      } else if (planId === "business") {
        additionalPriceId = PRICE_IDS.additional_license_business;
      } else {
        throw new Error("Licenças adicionais não disponíveis para este plano");
      }

      const { error } = await stripe.redirectToCheckout({
        mode: "subscription",
        lineItems: [{ price: additionalPriceId, quantity: 1 }],
        successUrl: `${window.location.origin}/settings/payment/success?plan=${planId}&additional=true`,
        cancelUrl: `${window.location.origin}/settings`,
        customerEmail: session.user.email,
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error("Erro ao adicionar licenças:", error);
      setError(error.message || "Ocorreu um erro ao processar sua solicitação");
      toast.error("Erro ao processar licenças adicionais. Tente novamente mais tarde.");
    } finally {
      setLoading(false);
    }
  };

  const renderPlanSelection = () => (
    <div className="grid gap-8 md:grid-cols-3">
      {plans.map((plan) => {
        const isCurrentPlan = currentPlan === plan.id;

        return (
          <Card key={plan.name} className={`flex flex-col ${isCurrentPlan ? "border-primary" : ""}`}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <CardTitle>{plan.name}</CardTitle>
                {isCurrentPlan && <Badge className="bg-primary">Ativo</Badge>}
              </div>
              <CardDescription>{plan.description}</CardDescription>
              <div className="text-3xl font-bold">{plan.price}</div>
              <div className="text-sm text-muted-foreground">por mês</div>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-2">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <Check className="h-4 w-4 mr-2 text-green-500" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {isCurrentPlan ? (
                <Button variant="outline" className="w-full" onClick={cancelSubscription} disabled={cancelingPlan}>
                  {cancelingPlan ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                  Cancelar Plano
                </Button>
              ) : (
                <>
                  <Button className="w-full" onClick={() => handlePlanSelection(plan.id, plan.priceId)} disabled={loading}>
                    {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                    {currentPlan ? "Mudar para este plano" : "Assinar"}
                  </Button>

                  {/* Botão adicional para adicionar licenças no plano atual */}
                  {isCurrentPlan && plan.id !== "individual" && (
                    <Button variant="outline" className="w-full mt-2" onClick={() => handleAdditionalLicenses(plan.id)} disabled={loading}>
                      Adquirir licenças adicionais
                    </Button>
                  )}
                </>
              )}
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );

  const renderCheckout = () => {
    if (!checkoutData) return null;

    return (
      <div className="max-w-xl mx-auto">
        <div className="mb-6 text-center">
          <h2 className="text-2xl font-bold mb-2">Finalizar Assinatura</h2>
          <p className="text-muted-foreground">
            {checkoutData.planName} - {checkoutData.planPrice}/mês
          </p>
        </div>

        <Card>
          <CardContent className="pt-6">
            {error && (
              <div className="mb-4 p-3 bg-destructive/10 border border-destructive rounded-md">
                <p className="text-destructive text-sm">{error}</p>
              </div>
            )}

            <div className="space-y-4 mb-6">
              <div className="p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">Resumo da Assinatura</h3>
                <div className="flex justify-between items-center">
                  <span>Plano {checkoutData.planName}</span>
                  <span className="font-semibold">{checkoutData.planPrice}/mês</span>
                </div>
              </div>

              <div className="p-4 border rounded-lg">
                <h3 className="font-semibold mb-3">Informações de Pagamento</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">Nome no Cartão</label>
                    <input
                      type="text"
                      placeholder="João Silva"
                      className="w-full p-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                      value={cardData.cardName}
                      onChange={(e) => setCardData({ ...cardData, cardName: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Número do Cartão</label>
                    <input
                      type="text"
                      placeholder="1234 5678 9012 3456"
                      className="w-full p-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                      value={cardData.cardNumber}
                      onChange={(e) => setCardData({ ...cardData, cardNumber: formatCardNumber(e.target.value) })}
                      maxLength={19}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium mb-1">Validade</label>
                      <input
                        type="text"
                        placeholder="MM/AA"
                        className="w-full p-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                        value={cardData.expiryDate}
                        onChange={(e) => setCardData({ ...cardData, expiryDate: formatExpiryDate(e.target.value) })}
                        maxLength={5}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">CVV</label>
                      <input
                        type="text"
                        placeholder="123"
                        className="w-full p-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                        value={cardData.cvv}
                        onChange={(e) => setCardData({ ...cardData, cvv: e.target.value.replace(/[^0-9]/g, "").slice(0, 4) })}
                        maxLength={4}
                      />
                    </div>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-2">🔒 Este é um checkout de demonstração. Nenhum pagamento real será processado.</p>
              </div>
            </div>

            <div className="space-y-3">
              <Button className="w-full" onClick={handleCheckoutConfirm} disabled={loading || !isFormValid()}>
                {loading ? (
                  <div className="flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processando...
                  </div>
                ) : (
                  "Confirmar Assinatura"
                )}
              </Button>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setCurrentStep("select-plan");
                  setError(null);
                  setCheckoutData(null);
                  setCardData({
                    cardNumber: "",
                    expiryDate: "",
                    cvv: "",
                    cardName: "",
                  });
                }}
                disabled={loading}
              >
                Voltar para seleção de planos
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return <div className="container py-8">{currentStep === "select-plan" ? renderPlanSelection() : renderCheckout()}</div>;
};
