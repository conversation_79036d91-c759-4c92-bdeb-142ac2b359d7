import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

interface SubscriptionAlertProps {
  isOpen: boolean;
  onClose: () => void;
  onViewPlans: () => void;
}

export const SubscriptionAlert = ({ isOpen, onClose, onViewPlans }: SubscriptionAlertProps) => {
  return (
    <AlertDialog open={isOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Período de teste encerrado</AlertDialogTitle>
          <AlertDialogDescription>
            Seu período de teste gratuito terminou. Para continuar aproveitando todos os benefícios como workspaces e convites ilimitados, escolha um
            plano.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose}>Depois</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button onClick={onViewPlans}>Ver planos</Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
