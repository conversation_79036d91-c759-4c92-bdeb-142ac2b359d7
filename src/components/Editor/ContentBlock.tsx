import React, { useRef, useEffect, useState } from "react";
import {
  Check,
  ChevronRight,
  ChevronDown,
  Image as ImageIcon,
  GripVertical,
  Copy,
  Trash2,
  Bookmark,
  Scissors,
  Kanban as KanbanIcon,
} from "lucide-react";
import { BlockType } from "../../types";
import { toast } from "sonner";
import { WorkspaceMetrics } from "@/components/Dashboard/WorkspaceMetrics";
import { MembersOverview } from "@/components/Dashboard/MembersOverview";
import { KanbanBoard } from "@/components/Kanban/KanbanBoard";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Profile, InvitationData } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { StickyNoteBlock } from "./StickyNoteBlock";
import { HtmlEditorBlock } from "./HtmlEditorBlock";

interface ContentBlockProps {
  id: string;
  type: BlockType;
  content: string;
  checked?: boolean;
  collapsed?: boolean;
  onContentChange: (id: string, content: string) => void;
  onTypeChange: (id: string, type: BlockType) => void;
  onKeyDown: (e: React.KeyboardEvent, id: string) => void;
  onFocus: (id: string) => void;
  onBlur: () => void;
  onDelete: (id: string) => void;
  onToggleCheck?: (id: string) => void;
  onToggleCollapse?: (id: string) => void;
  onDuplicate?: (id: string) => void;
  onDragStart?: (e: React.DragEvent, id: string) => void;
  onDragEnd?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent, id: string) => void;
  onImageDrop?: (id: string, file: File) => void;
  readOnly?: boolean;
  searchHighlight?: string | null;
}

// Componentes isolados para Dashboard e Kanban
const DashboardBlock: React.FC = () => {
  const { currentWorkspace, workspaces, setCurrentWorkspace, workspaceMembers, updateMemberRole, removeMember } = useWorkspace();
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>("member");
  const [pendingInvitations, setPendingInvitations] = useState<InvitationData[]>([]);
  const [selectedInvitationId, setSelectedInvitationId] = useState<string | null>(null);
  const [selectedInvitationRole, setSelectedInvitationRole] = useState<string>("member");
  const [isManagingInvitation, setIsManagingInvitation] = useState(false);

  useEffect(() => {
    if (!currentWorkspace && workspaces && workspaces.length > 0) {
      setCurrentWorkspace(workspaces[0]);
    }
  }, [currentWorkspace, workspaces, setCurrentWorkspace]);

  useEffect(() => {
    if (currentWorkspace && isPermissionsModalOpen) {
      fetchPendingInvitations();
    }
  }, [currentWorkspace, isPermissionsModalOpen]);

  const fetchPendingInvitations = async () => {
    if (!currentWorkspace) return;

    try {
      const { data, error } = await supabase
        .from("workspace_invitations")
        .select("id, email, role, created_at")
        .eq("workspace_id", currentWorkspace.id)
        .eq("accepted", false);

      if (error) {
        console.error("Error fetching pending invitations:", error);
        return;
      }

      setPendingInvitations((data as InvitationData[]) || []);
    } catch (err) {
      console.error("Error fetching pending invitations:", err);
    }
  };

  const handleUpdatePermission = async () => {
    if (!isManagingInvitation && selectedMemberId && currentWorkspace) {
      await updateMemberRole(currentWorkspace.id, selectedMemberId, selectedRole as "owner" | "admin" | "member" | "viewer");
      setIsPermissionsModalOpen(false);
    } else if (isManagingInvitation && selectedInvitationId && currentWorkspace) {
      await updateInvitationRole(selectedInvitationId, selectedInvitationRole as "admin" | "member" | "viewer");
      setIsPermissionsModalOpen(false);
    }
  };

  const handleRemoveMember = async () => {
    if (!isManagingInvitation && selectedMemberId && currentWorkspace) {
      await removeMember(currentWorkspace.id, selectedMemberId);
      setIsPermissionsModalOpen(false);
    } else if (isManagingInvitation && selectedInvitationId) {
      await cancelInvitation(selectedInvitationId);
      setIsPermissionsModalOpen(false);
    }
  };

  const updateInvitationRole = async (invitationId: string, role: string) => {
    try {
      const { error } = await supabase.from("workspace_invitations").update({ role }).eq("id", invitationId);

      if (error) throw error;

      toast.success("Permissão do convite atualizada com sucesso!");
      fetchPendingInvitations();
    } catch (err) {
      console.error("Error updating invitation role:", err);
      toast.error("Erro ao atualizar permissão do convite");
    }
  };

  const cancelInvitation = async (invitationId: string) => {
    try {
      const { error } = await supabase.from("workspace_invitations").delete().eq("id", invitationId);

      if (error) throw error;

      toast.success("Convite cancelado com sucesso!");
      fetchPendingInvitations();
    } catch (err) {
      console.error("Error canceling invitation:", err);
      toast.error("Erro ao cancelar convite");
    }
  };

  const openPermissionsModal = (member: Profile) => {
    setSelectedMemberId(member.id);
    setSelectedRole(member.role || "member");
    setIsManagingInvitation(false);
    setIsPermissionsModalOpen(true);
  };

  const openInvitationPermissionsModal = (invitation: InvitationData) => {
    setSelectedInvitationId(invitation.id);
    setSelectedInvitationRole(invitation.role || "member");
    setIsManagingInvitation(true);
    setIsPermissionsModalOpen(true);
  };

  return (
    <div className="h-full flex flex-col bg-notion-page text-notion-text">
      {currentWorkspace ? (
        <div className="max-w-7xl mx-auto space-y-6">
          <Card className="bg-notion-sidebar border-notion-border">
            <CardHeader className="pb-2">
              <CardTitle className="text-2xl font-bold text-notion-text">{currentWorkspace.name || "Workspace sem nome"}</CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-notion-muted">
              {currentWorkspace.id && <p>ID: {currentWorkspace.id}</p>}
              <p>Criado em: {new Date(currentWorkspace.createdAt).toLocaleDateString("pt-BR")}</p>
            </CardContent>
          </Card>

          <h2 className="text-xl font-semibold text-notion-text mt-6 mb-4">Métricas do Workspace</h2>
          <WorkspaceMetrics />

          <Tabs defaultValue="members" className="mt-8">
            <TabsList className="bg-notion-sidebar border border-notion-border">
              <TabsTrigger value="members" className="data-[state=active]:bg-notion-hover">
                Membros
              </TabsTrigger>
              <TabsTrigger value="stats" className="data-[state=active]:bg-notion-hover">
                Estatísticas
              </TabsTrigger>
              <TabsTrigger value="settings" className="data-[state=active]:bg-notion-hover">
                Configurações
              </TabsTrigger>
            </TabsList>

            <TabsContent value="members" className="mt-4">
              <MembersOverview onManagePermissions={openPermissionsModal} onManageInvitation={openInvitationPermissionsModal} />
            </TabsContent>

            <TabsContent value="stats" className="mt-4">
              <Card className="bg-notion-sidebar border-notion-border">
                <CardHeader>
                  <CardTitle className="text-notion-text">Estatísticas do Workspace</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-notion-text mb-2">Atividades recentes</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-notion-muted">Páginas criadas (últimos 7 dias)</span>
                          <span className="text-notion-text font-medium">5</span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-notion-muted">Edições (últimos 7 dias)</span>
                          <span className="text-notion-text font-medium">23</span>
                        </div>
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-notion-muted">Comentários (últimos 7 dias)</span>
                          <span className="text-notion-text font-medium">7</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-notion-text mb-2">Uso de armazenamento</h3>
                      <div className="bg-notion-page h-2 rounded-full overflow-hidden">
                        <div
                          className="bg-purple-600 h-full"
                          style={{
                            width: "35%",
                          }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs mt-1">
                        <span className="text-notion-muted">1.2 GB usados</span>
                        <span className="text-notion-muted">5 GB total</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="mt-4">
              <Card className="bg-notion-sidebar border-notion-border">
                <CardHeader>
                  <CardTitle className="text-notion-text">Configurações do Workspace</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="font-medium text-notion-text">Permissões</h3>
                      <p className="text-notion-muted text-sm">Configure o acesso de membros e convites para este workspace.</p>
                      <Button onClick={() => setIsPermissionsModalOpen(true)} className="border-notion-border text-notion-text mt-2">
                        Gerenciar permissões
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <Card className="max-w-md w-full bg-notion-sidebar border-notion-border">
            <CardHeader>
              <CardTitle className="text-notion-text">Nenhum workspace selecionado</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-notion-muted mb-4">Selecione um workspace existente ou crie um novo para visualizar o dashboard.</p>
              <Button className="w-full bg-purple-600 hover:bg-purple-700">Criar novo workspace</Button>
            </CardContent>
          </Card>
        </div>
      )}

      <Dialog open={isPermissionsModalOpen} onOpenChange={setIsPermissionsModalOpen}>
        <DialogContent className="bg-notion-sidebar border-notion-border text-notion-text">
          <DialogHeader>
            <DialogTitle>Gerenciar Permissões</DialogTitle>
            <DialogDescription className="text-notion-muted">
              {isManagingInvitation ? "Altere as permissões do convite pendente." : "Altere as permissões dos membros deste workspace."}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {!isManagingInvitation ? (
              <div className="space-y-2">
                <h4 className="font-medium text-notion-text">Selecione um membro</h4>
                {workspaceMembers && workspaceMembers.length > 0 ? (
                  <Select
                    value={selectedMemberId || ""}
                    onValueChange={(value) => {
                      setSelectedMemberId(value);
                      const member = workspaceMembers.find((m) => m.id === value);
                      if (member && member.role) {
                        setSelectedRole(member.role);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full bg-notion-page border-notion-border">
                      <SelectValue placeholder="Selecione um membro" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-page border-notion-border">
                      {workspaceMembers.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.name} ({member.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-notion-muted text-sm">Nenhum membro encontrado.</p>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <h4 className="font-medium text-notion-text">Convite selecionado</h4>
                {pendingInvitations && pendingInvitations.length > 0 ? (
                  <Select
                    value={selectedInvitationId || "unassigned"}
                    onValueChange={(value) => {
                      setSelectedInvitationId(value === "unassigned" ? null : value);
                      if (value !== "unassigned") {
                        const invitation = pendingInvitations.find((inv) => inv.id === value);
                        if (invitation && invitation.role) {
                          setSelectedInvitationRole(invitation.role);
                        }
                      }
                    }}
                  >
                    <SelectTrigger className="w-full bg-notion-page border-notion-border">
                      <SelectValue placeholder="Selecione um convite" />
                    </SelectTrigger>
                    <SelectContent className="bg-notion-page border-notion-border">
                      <SelectItem value="unassigned">Selecione um convite</SelectItem>
                      {pendingInvitations.map((invitation) => (
                        <SelectItem key={invitation.id} value={invitation.id}>
                          {invitation.email} (Pendente)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-notion-muted text-sm">Nenhum convite pendente encontrado.</p>
                )}
              </div>
            )}

            {((selectedMemberId && !isManagingInvitation) || (selectedInvitationId && isManagingInvitation)) && (
              <div className="space-y-2">
                <h4 className="font-medium text-notion-text">Permissão</h4>
                <Select
                  value={isManagingInvitation ? selectedInvitationRole : selectedRole}
                  onValueChange={isManagingInvitation ? setSelectedInvitationRole : setSelectedRole}
                >
                  <SelectTrigger className="w-full bg-notion-page border-notion-border">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-notion-page border-notion-border">
                    {!isManagingInvitation && <SelectItem value="owner">Proprietário</SelectItem>}
                    <SelectItem value="admin">Administrador</SelectItem>
                    <SelectItem value="member">Membro</SelectItem>
                    <SelectItem value="viewer">Visualizador</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-between">
            <Button
              onClick={handleRemoveMember}
              className="bg-red-500 hover:bg-red-600"
              disabled={(!selectedMemberId && !isManagingInvitation) || (!selectedInvitationId && isManagingInvitation)}
            >
              {isManagingInvitation ? "Cancelar convite" : "Remover membro"}
            </Button>
            <div className="flex gap-2">
              <Button className="bg-notion-page" onClick={() => setIsPermissionsModalOpen(false)}>
                Cancelar
              </Button>
              <Button
                onClick={handleUpdatePermission}
                disabled={(!selectedMemberId && !isManagingInvitation) || (!selectedInvitationId && isManagingInvitation)}
              >
                Salvar alterações
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const KanbanBlock: React.FC = () => {
  return (
    <div className="h-full flex flex-col bg-notion-page text-notion-text">
      <div className="flex-1 overflow-hidden h-[450px]">
        <KanbanBoard />
      </div>
    </div>
  );
};

export const ContentBlock: React.FC<ContentBlockProps> = ({
  id,
  type,
  content,
  checked = false,
  collapsed = false,
  onContentChange,
  onTypeChange,
  onKeyDown,
  onFocus,
  onBlur,
  onDelete,
  onToggleCheck,
  onToggleCollapse,
  onDuplicate,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  onImageDrop,
  readOnly = false,
  searchHighlight = null,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [language, setLanguage] = useState("javascript");
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [showBlockMenu, setShowBlockMenu] = useState(false);
  const [dragOverState, setDragOverState] = useState<"none" | "subitem" | "reorder">("none");
  const [selectedText, setSelectedText] = useState<string>("");

  useEffect(() => {
    if (contentRef.current) {
      // Garantir que o conteúdo seja sempre inicializado, mesmo se for vazio
      if (content === undefined || content === null) {
        contentRef.current.innerHTML = "";
      } else if (contentRef.current.innerHTML !== content) {
        contentRef.current.innerHTML = content;
      }
    }

    // Aplicar estilos específicos com base no tipo
    switch (type) {
      case "bulleted_list":
        if (contentRef.current && !contentRef.current.innerHTML.includes("<li>")) {
          contentRef.current.innerHTML = `<ul><li>${contentRef.current.innerHTML || ""}</li></ul>`;
          onContentChange(id, contentRef.current.innerHTML);
        }
        break;
      case "numbered_list":
        if (contentRef.current && !contentRef.current.innerHTML.includes("<li>")) {
          contentRef.current.innerHTML = `<ol><li>${contentRef.current.innerHTML || ""}</li></ol>`;
          onContentChange(id, contentRef.current.innerHTML);
        }
        break;
      case "callout":
        if (contentRef.current && !contentRef.current.innerHTML.includes("callout-content")) {
          contentRef.current.innerHTML = `<div class="callout-content">${contentRef.current.innerHTML || ""}</div>`;
          onContentChange(id, contentRef.current.innerHTML);
        }
        break;
    }
  }, [type, content]);

  useEffect(() => {
    if (!contentRef.current || !searchHighlight || type === "code") {
      return;
    }

    const searchRegex = new RegExp(`(${escapeRegExp(searchHighlight)})`, "gi");
    const currentHtml = contentRef.current.innerHTML;

    if (!currentHtml || currentHtml.includes('data-highlighted="true"')) {
      return;
    }

    try {
      const placeholders: { [key: string]: string } = {};
      let cleanedHtml = currentHtml.replace(/<[^>]+>/g, (match) => {
        const key = `__PLACEHOLDER_${Object.keys(placeholders).length}__`;
        placeholders[key] = match;
        return key;
      });

      cleanedHtml = cleanedHtml.replace(searchRegex, '<span data-highlighted="true" class="bg-yellow-200">$1</span>');

      Object.entries(placeholders).forEach(([key, value]) => {
        cleanedHtml = cleanedHtml.replace(key, value);
      });

      contentRef.current.innerHTML = cleanedHtml;
    } catch (error) {
      console.error("Error highlighting search text:", error);
    }

    return () => {
      if (contentRef.current && !searchHighlight) {
        const currentContent = contentRef.current.innerHTML;
        if (currentContent.includes('data-highlighted="true"')) {
          contentRef.current.innerHTML = currentContent.replace(/<span data-highlighted="true"[^>]*>(.*?)<\/span>/g, "$1");
        }
      }
    };
  }, [searchHighlight, type]);

  const escapeRegExp = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  };

  const handleChange = () => {
    if (contentRef.current && !readOnly) {
      const newContent = contentRef.current.innerHTML;
      onContentChange(id, newContent);

      if (type === "empty_line" && newContent && newContent !== "<br>") {
        onTypeChange(id, "text");
      }
    }
  };

  const handleCheckboxToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onToggleCheck && !readOnly) {
      onToggleCheck(id);
    }
  };

  const handleCollapseToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onToggleCollapse && !readOnly) {
      onToggleCollapse(id);
    }
  };

  const handleLocalKeyDown = (e: React.KeyboardEvent) => {
    if (readOnly) return;

    // Repassar evento Tab para o PageEditor - Importante para subitem de imagem
    if (e.key === "Tab") {
      e.preventDefault();
      onKeyDown(e, id);
      return;
    }

    // Se for uma linha em branco e o usuário pressionar "/" ou Enter, disparar o evento para o PageEditor
    if (type === "empty_line" && (e.key === "/" || e.key === "Enter")) {
      e.preventDefault();
      onKeyDown(e, id);
      return;
    }

    if (e.key === "Enter" && !e.shiftKey) {
      if (type === "code") {
        return;
      }
      e.preventDefault();
      onKeyDown(e, id);
      return;
    }

    // Verifica se o bloco está vazio e é um checkbox ou toggle
    if (e.key === "Backspace" && contentRef.current) {
      const isEmpty = contentRef.current.innerText.trim() === "";
      // Só exclui o bloco se o usuário pressionou backspace e ele já está vazio
      if (isEmpty) {
        e.preventDefault();
        onDelete(id);
        return;
      }
    }

    if (e.key === "/" && !e.shiftKey && type !== "code") {
      onKeyDown(e, id);
    }
  };

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (!readOnly) {
      setLanguage(e.target.value);
    }
  };

  const handleDragStart = (e: React.DragEvent) => {
    if (onDragStart && !readOnly) {
      onDragStart(e, id);
      setIsDragging(true);
    }
  };

  const handleDragEnd = (e: React.DragEvent) => {
    if (onDragEnd && !readOnly) {
      onDragEnd(e);
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (onDragOver && !readOnly) {
      e.preventDefault();
      onDragOver(e);

      // Determina se o cursor está no meio do bloco (criação de subitem)
      // ou nas bordas (reordenação)
      const rect = e.currentTarget.getBoundingClientRect();
      const mouseY = e.clientY;
      const targetMiddle = rect.top + rect.height / 2;

      if (Math.abs(mouseY - targetMiddle) < rect.height * 0.3) {
        setDragOverState("subitem");
      } else {
        setDragOverState("reorder");
      }
    }
  };

  const handleDragLeave = () => {
    setDragOverState("none");
  };

  const handleDrop = (e: React.DragEvent) => {
    if (!readOnly) {
      e.preventDefault();

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        if (file.type.startsWith("image/") && onImageDrop) {
          onImageDrop(id, file);
          return;
        }
      }

      if (onDrop) {
        onDrop(e, id);
      }
    }
  };

  const handleDuplicateClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDuplicate && !readOnly) {
      onDuplicate(id);
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onDelete && !readOnly) {
      onDelete(id);
    }
  };

  const handleContextMenuAction = (action: string) => {
    if (readOnly) return;

    switch (action) {
      case "duplicate":
        if (onDuplicate) onDuplicate(id);
        break;
      case "delete":
        if (onDelete) onDelete(id);
        break;
      default:
        break;
    }
  };

  const getBlockTypeStyles = () => {
    switch (type) {
      case "heading_1":
        return "text-3xl font-bold";
      case "heading_2":
        return "text-2xl font-bold";
      case "heading_3":
        return "text-xl font-bold";
      case "bulleted_list":
        return "pl-5 list-disc ml-2";
      case "numbered_list":
        return "pl-5 list-decimal ml-2";
      case "todo":
        return "flex items-center gap-2 min-h-[28px]";
      case "toggle":
        return "flex items-start gap-2 min-h-[28px]";
      case "code":
        return "font-mono bg-gray-800 text-gray-100 rounded-md my-4 overflow-auto";
      case "columns":
        return "my-4";
      case "image":
        return "flex flex-col items-center my-4";
      case "callout":
        return "bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-md my-4";
      case "table":
        return "overflow-x-auto my-4";
      case "dashboard":
        return "my-4 w-full";
      case "kanban":
        return "my-4 w-full h-[500px]";
      case "sticky_note":
        return "my-4 w-full";
      case "divider":
        return "my-4 w-full";
      case "empty_line":
        return "my-2 w-full min-h-[24px]";
      case "html_editor":
        return "my-4 w-full";
      default:
        return "";
    }
  };

  const getBlockPlaceholder = () => {
    switch (type) {
      case "text":
        return "Digite / para ver os blocos disponíveis...";
      case "heading_1":
        return "Título 1";
      case "heading_2":
        return "Título 2";
      case "heading_3":
        return "Título 3";
      case "bulleted_list":
        return "Item de lista";
      case "numbered_list":
        return "Item numerado";
      case "todo":
        return "Tarefa a fazer...";
      case "toggle":
        return "Bloco oculto (clique para mostrar/ocultar)";
      case "code":
        return "Digite seu código aqui...";
      case "columns":
        return "Colunas";
      case "image":
        return "Adicionar imagem";
      case "callout":
        return "Nota importante";
      case "table":
        return "Tabela";
      case "dashboard":
        return "Dashboard do Workspace";
      case "kanban":
        return "Quadro Kanban";
      case "divider":
        return "Divisor horizontal";
      case "empty_line":
        return "Pressione / para adicionar o bloco desejado...";
      case "html_editor":
        return "Editor HTML - Digite seu código HTML aqui...";
      default:
        return "Pressione / para adicionar o bloco desejado..";
    }
  };

  const renderToggle = () => {
    if (type === "toggle") {
      return (
        <button className="flex-shrink-0 w-5 h-5 mt-1 text-notion-muted hover:text-notion-text" onClick={handleCollapseToggle} disabled={readOnly}>
          {collapsed ? <ChevronRight size={20} /> : <ChevronDown size={20} />}
        </button>
      );
    }
    return null;
  };

  const renderCheckbox = () => {
    if (type === "todo") {
      return (
        <div
          className={`flex-shrink-0 w-5 h-5 border ${checked ? "bg-blue-500 border-blue-500" : "border-notion-border"} rounded cursor-pointer ${
            readOnly ? "opacity-50" : ""
          }`}
          onClick={handleCheckboxToggle}
        >
          {checked && <Check size={16} className="text-white w-full h-full" />}
        </div>
      );
    }
    return null;
  };

  const renderBlockContent = () => {
    if (type === "divider") {
      return (
        <div className="w-full py-2 flex items-center justify-center">
          <hr className="w-full border-t border-notion-border" />
        </div>
      );
    }

    if (type === "empty_line") {
      return (
        <div
          className="w-full min-h-6 py-1.5 outline-none hover:bg-notion-hover rounded transition-colors relative flex items-center"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={() => contentRef.current?.focus()}
        >
          {(isFocused || isHovered) && (!content || content === "<br>") && (
            <div className="absolute inset-0 pointer-events-none text-notion-muted flex items-center px-2">{getBlockPlaceholder()}</div>
          )}
          <div
            ref={contentRef}
            data-block-id={id}
            contentEditable={!readOnly}
            suppressContentEditableWarning
            className="w-full min-h-[24px] outline-none px-2 z-10"
            onInput={handleChange}
            onKeyDown={handleLocalKeyDown}
            onFocus={() => {
              setIsFocused(true);
              onFocus(id);
            }}
            onBlur={() => {
              setIsFocused(false);
              onBlur();
            }}
            style={{
              cursor: "text",
              userSelect: "text",
              WebkitUserSelect: "text",
              MozUserSelect: "text",
              msUserSelect: "text",
              pointerEvents: "auto",
            }}
          />
        </div>
      );
    }

    if (type === "sticky_note") {
      return <StickyNoteBlock id={id} content={content} onDelete={onDelete} />;
    }

    if (type === "columns") {
      return (
        <div className="flex gap-4 w-full">
          <div className="flex-1 border border-notion-border p-4 rounded-md" contentEditable={!readOnly} suppressContentEditableWarning>
            Coluna 1
          </div>
          <div className="flex-1 border border-notion-border p-4 rounded-md" contentEditable={!readOnly} suppressContentEditableWarning>
            Coluna 2
          </div>
        </div>
      );
    }

    if (type === "table") {
      if (content) {
        return <div dangerouslySetInnerHTML={{ __html: content }} className="w-full" />;
      }

      // Template padrão para uma nova tabela
      const defaultTable = `
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-100">
              <th class="border border-notion-border p-2">Coluna 1</th>
              <th class="border border-notion-border p-2">Coluna 2</th>
              <th class="border border-notion-border p-2">Coluna 3</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="border border-notion-border p-2" contenteditable=${!readOnly}>Célula 1</td>
              <td class="border border-notion-border p-2" contenteditable=${!readOnly}>Célula 2</td>
              <td class="border border-notion-border p-2" contenteditable=${!readOnly}>Célula 3</td>
            </tr>
            <tr>
              <td class="border border-notion-border p-2" contenteditable=${!readOnly}>Célula 4</td>
              <td class="border border-notion-border p-2" contenteditable=${!readOnly}>Célula 5</td>
              <td class="border border-notion-border p-2" contenteditable=${!readOnly}>Célula 6</td>
            </tr>
          </tbody>
        </table>
      `;

      if (onContentChange && !content) {
        onContentChange(id, defaultTable);
      }

      return <div dangerouslySetInnerHTML={{ __html: content || defaultTable }} className="w-full overflow-x-auto" />;
    }

    if (type === "code") {
      return (
        <div className="code-block w-full rounded-md overflow-hidden my-1 text-gray-100">
          <div className="toolbar">
            <select
              value={language}
              onChange={handleLanguageChange}
              className="bg-gray-800 text-gray-200 outline-none border-none text-sm px-2 py-1 rounded"
              disabled={readOnly}
            >
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="json">JSON</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="jsx">JSX</option>
              <option value="tsx">TSX</option>
              <option value="python">Python</option>
              <option value="java">Java</option>
              <option value="csharp">C#</option>
              <option value="cpp">C++</option>
              <option value="php">PHP</option>
              <option value="ruby">Ruby</option>
              <option value="go">Go</option>
              <option value="rust">Rust</option>
              <option value="kotlin">Kotlin</option>
              <option value="swift">Swift</option>
              <option value="sql">SQL</option>
              <option value="bash">Bash</option>
              <option value="powershell">PowerShell</option>
            </select>
            <div className="flex gap-2">
              {selectedText && (
                <button
                  className="text-gray-300 hover:text-white transition p-1"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    navigator.clipboard.writeText(selectedText);
                    toast.success("Texto selecionado copiado!");
                  }}
                  title="Copiar seleção"
                >
                  <Scissors size={14} />
                </button>
              )}
              <button
                className="text-gray-300 hover:text-white transition p-1"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (contentRef.current) {
                    const text = contentRef.current.innerText;
                    navigator.clipboard.writeText(text);
                    toast.success("Código copiado para a área de transferência!");
                  }
                }}
                title="Copiar código"
              >
                <Copy size={14} />
              </button>
            </div>
          </div>
          <pre className="p-4 m-0 overflow-x-auto relative" style={{ textAlign: "left" }}>
            <div
              ref={contentRef}
              data-block-id={id}
              contentEditable={!readOnly}
              suppressContentEditableWarning
              spellCheck="false"
              dir="ltr"
              style={{
                whiteSpace: "pre",
                fontFamily: "var(--font-mono, 'Menlo', 'Monaco', 'Courier New', monospace)",
                position: "relative",
                cursor: "text",
                userSelect: "text",
                WebkitUserSelect: "text",
                MozUserSelect: "text",
                msUserSelect: "text",
                pointerEvents: "auto",
              }}
              className={`outline-none min-h-[24px] block w-full text-gray-100 empty:before:content-[attr(data-placeholder)] empty:before:text-gray-500 empty:before:pointer-events-none caret-white`}
              onInput={handleChange}
              onKeyDown={handleLocalKeyDown}
              onFocus={() => {
                setIsFocused(true);
                onFocus(id);
              }}
              onBlur={() => {
                setIsFocused(false);
                onBlur();
              }}
              data-placeholder={getBlockPlaceholder()}
            />
          </pre>
        </div>
      );
    }

    if (type === "image") {
      if (content && content.includes("src=")) {
        return <div dangerouslySetInnerHTML={{ __html: content }} className="max-w-full" />;
      }

      return (
        <div
          className="flex flex-col items-center justify-center border-2 border-dashed border-notion-border rounded-md p-8 w-full"
          onClick={() => {
            if (!readOnly && type === "image") {
              const fileInput = document.createElement("input");
              fileInput.type = "file";
              fileInput.accept = "image/*";
              fileInput.onchange = (e) => {
                const files = (e.target as HTMLInputElement).files;
                if (files && files.length > 0 && onImageDrop) {
                  onImageDrop(id, files[0]);
                }
              };
              fileInput.click();
            }
          }}
        >
          <ImageIcon className="w-10 h-10 text-notion-muted mb-2" />
          <div className="text-notion-muted text-center" data-placeholder={getBlockPlaceholder()}>
            {content || getBlockPlaceholder()}
          </div>
        </div>
      );
    }

    if (type === "dashboard") {
      return <DashboardBlock />;
    }

    if (type === "kanban") {
      return <KanbanBlock />;
    }

    if (type === "html_editor") {
      return (
        <HtmlEditorBlock
          content={content}
          onChange={(newContent) => onContentChange(id, newContent)}
          readOnly={readOnly}
        />
      );
    }

    if (type === "callout") {
      return (
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-2 mt-0.5 text-blue-500">
            <Bookmark size={18} />
          </div>
          <div
            ref={contentRef}
            data-block-id={id}
            contentEditable={!readOnly}
            suppressContentEditableWarning
            className={`outline-none min-h-[24px] w-full ${
              checked ? "line-through text-notion-muted" : ""
            } empty:before:content-[attr(data-placeholder)] empty:before:text-notion-muted empty:before:pointer-events-none`}
            onInput={handleChange}
            onKeyDown={handleLocalKeyDown}
            onFocus={() => {
              setIsFocused(true);
              onFocus(id);
            }}
            onBlur={() => {
              setIsFocused(false);
              onBlur();
            }}
            data-placeholder={getBlockPlaceholder()}
          />
        </div>
      );
    }

    if (type === "bulleted_list") {
      return (
        <div
          ref={contentRef}
          data-block-id={id}
          contentEditable={!readOnly}
          suppressContentEditableWarning
          className={`outline-none min-h-[24px] w-full list-disc pl-5 ml-2 ${
            checked ? "line-through text-notion-muted" : ""
          } empty:before:content-[attr(data-placeholder)] empty:before:text-notion-muted empty:before:pointer-events-none`}
          onInput={handleChange}
          onKeyDown={handleLocalKeyDown}
          onFocus={() => {
            setIsFocused(true);
            onFocus(id);
          }}
          onBlur={() => {
            setIsFocused(false);
            onBlur();
          }}
          data-placeholder={getBlockPlaceholder()}
          style={{
            cursor: "text",
            userSelect: "text",
            WebkitUserSelect: "text",
            MozUserSelect: "text",
            msUserSelect: "text",
            pointerEvents: "auto",
          }}
        />
      );
    }

    if (type === "numbered_list") {
      return (
        <div
          ref={contentRef}
          data-block-id={id}
          contentEditable={!readOnly}
          suppressContentEditableWarning
          className={`outline-none min-h-[24px] w-full list-decimal pl-5 ml-2 ${
            checked ? "line-through text-notion-muted" : ""
          } empty:before:content-[attr(data-placeholder)] empty:before:text-notion-muted empty:before:pointer-events-none`}
          onInput={handleChange}
          onKeyDown={handleLocalKeyDown}
          onFocus={() => {
            setIsFocused(true);
            onFocus(id);
          }}
          onBlur={() => {
            setIsFocused(false);
            onBlur();
          }}
          data-placeholder={getBlockPlaceholder()}
          style={{
            cursor: "text",
            userSelect: "text",
            WebkitUserSelect: "text",
            MozUserSelect: "text",
            msUserSelect: "text",
            pointerEvents: "auto",
          }}
        />
      );
    }

    const placeholder = getBlockPlaceholder();
    const isEmpty = !content || content === "<br>";

    // Para tipos todo e toggle, simplificar para garantir que o contentEditable funcione corretamente
    if (type === "todo" || type === "toggle") {
      return (
        <div
          ref={contentRef}
          data-block-id={id}
          contentEditable={!readOnly}
          suppressContentEditableWarning
          className={`outline-none min-h-[24px] ml-2 flex-grow ${
            checked ? "line-through text-notion-muted" : ""
          } empty:before:content-[attr(data-placeholder)] empty:before:text-notion-muted empty:before:pointer-events-none`}
          onInput={handleChange}
          onKeyDown={handleLocalKeyDown}
          onFocus={() => {
            setIsFocused(true);
            onFocus(id);
          }}
          onBlur={() => {
            setIsFocused(false);
            onBlur();
          }}
          data-placeholder={placeholder}
          style={{
            cursor: "text",
            userSelect: "text",
            WebkitUserSelect: "text",
            MozUserSelect: "text",
            msUserSelect: "text",
            pointerEvents: "auto",
          }}
        />
      );
    }

    return (
      <div
        ref={contentRef}
        data-block-id={id}
        contentEditable={!readOnly}
        suppressContentEditableWarning
        className={`outline-none min-h-[24px] w-full ${
          checked ? "line-through text-notion-muted" : ""
        } empty:before:content-[attr(data-placeholder)] empty:before:text-notion-muted empty:before:pointer-events-none`}
        onInput={handleChange}
        onKeyDown={handleLocalKeyDown}
        onFocus={() => {
          setIsFocused(true);
          onFocus(id);
        }}
        onBlur={() => {
          setIsFocused(false);
          onBlur();
        }}
        data-placeholder={placeholder}
        style={{
          cursor: "text",
          userSelect: "text",
          WebkitUserSelect: "text",
          MozUserSelect: "text",
          msUserSelect: "text",
          pointerEvents: "auto",
        }}
      />
    );
  };

  useEffect(() => {
    if (!showBlockMenu) return;
    const handleClick = (e: MouseEvent) => {
      setShowBlockMenu(false);
    };
    window.addEventListener("mousedown", handleClick);
    return () => window.removeEventListener("mousedown", handleClick);
  }, [showBlockMenu]);

  // Função para lidar com seleção de texto
  const handleSelectionChange = () => {
    const selection = window.getSelection();
    if (selection && selection.toString() && contentRef.current?.contains(selection.anchorNode)) {
      setSelectedText(selection.toString());
    } else {
      setSelectedText("");
    }
  };

  // Adiciona e remove event listener para seleção de texto
  useEffect(() => {
    document.addEventListener("selectionchange", handleSelectionChange);
    return () => {
      document.removeEventListener("selectionchange", handleSelectionChange);
    };
  }, []);

  return (
    <div
      className={`content-block group relative mb-1 ${isDragging ? "opacity-50" : ""}`}
      draggable={!readOnly && !isFocused}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={(e) => {
        setDragOverState("none");
        handleDrop(e);
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        userSelect: isFocused ? "text" : "inherit",
        WebkitUserSelect: isFocused ? "text" : "inherit",
        MozUserSelect: isFocused ? "text" : "inherit",
        msUserSelect: isFocused ? "text" : "inherit",
      }}
    >
      {!readOnly && (
        <div className="absolute left-0 top-1/2 -translate-x-6 -translate-y-1/2 transform opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
          <button className="p-0.5 text-notion-muted hover:text-notion-text cursor-grab" aria-label="Drag to reorder">
            <GripVertical size={16} />
          </button>
        </div>
      )}

      {!readOnly && (
        <div className="absolute right-0 top-1/2 translate-x-6 -translate-y-1/2 transform opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
          <button className="p-0.5 text-notion-muted hover:text-notion-text" onClick={handleDuplicateClick} aria-label="Duplicar bloco">
            <Copy size={16} />
          </button>
          <button className="p-0.5 text-notion-muted hover:text-red-500" onClick={handleDeleteClick} aria-label="Excluir bloco">
            <Trash2 size={16} />
          </button>
        </div>
      )}

      {/* Indicadores visuais de drag and drop */}
      {dragOverState === "subitem" && (
        <div className="absolute inset-0 border-2 border-blue-400 bg-blue-50 bg-opacity-30 rounded-md pointer-events-none">
          <div className="absolute top-1/2 left-1 transform -translate-y-1/2 text-blue-500">
            <ChevronRight size={16} />
          </div>
        </div>
      )}

      {dragOverState === "reorder" && <div className="absolute inset-x-0 -top-1 h-2 border-t-2 border-gray-400 pointer-events-none"></div>}

      <div
        className={`px-2 py-1 rounded-sm ${getBlockTypeStyles()}`}
        onClick={() => (type === "todo" || type === "toggle" ? contentRef.current?.focus() : null)}
      >
        {renderToggle()}
        {renderCheckbox()}
        <div className="select-text" style={{ userSelect: "text", WebkitUserSelect: "text", MozUserSelect: "text", msUserSelect: "text" }}>
          {renderBlockContent()}
        </div>
      </div>
    </div>
  );
};
