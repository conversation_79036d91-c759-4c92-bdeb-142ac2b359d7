
import React, { useState, useEffect } from 'react';
import { Search, ArrowUp, ArrowDown, X } from 'lucide-react';
import { Input } from '../ui/input';

interface ContentSearchProps {
  onHighlight: (query: string, index?: number) => void;
  onClearHighlight: () => void;
}

export const ContentSearch: React.FC<ContentSearchProps> = ({ 
  onHighlight, 
  onClearHighlight
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [matchCount, setMatchCount] = useState(0);
  const [currentMatch, setCurrentMatch] = useState(0);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+F or Cmd+F to open search
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        setIsOpen(true);
      }
      
      // Escape to close search
      if (e.key === 'Escape') {
        setIsOpen(false);
        setSearchTerm('');
        onClearHighlight();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClearHighlight]);

  useEffect(() => {
    if (searchTerm) {
      // Update the onHighlight function signature to accept the index parameter
      onHighlight(searchTerm);
    } else {
      onClearHighlight();
      setMatchCount(0);
      setCurrentMatch(0);
    }
  }, [searchTerm, onHighlight, onClearHighlight]);

  const handleNextMatch = () => {
    if (matchCount > 0) {
      setCurrentMatch(prev => (prev + 1) % matchCount);
      onHighlight(searchTerm, currentMatch + 1);
    }
  };

  const handlePrevMatch = () => {
    if (matchCount > 0) {
      setCurrentMatch(prev => (prev - 1 + matchCount) % matchCount);
      onHighlight(searchTerm, currentMatch - 1);
    }
  };

  if (!isOpen) {
    return (
      <button 
        className="fixed bottom-4 right-4 bg-notion-page text-notion-text p-2 rounded-full shadow-md hover:bg-notion-hover"
        onClick={() => setIsOpen(true)}
      >
        <Search size={16} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 flex items-center gap-2 bg-notion-page border border-notion-border rounded-md shadow-md p-1">
      <div className="relative">
        <Search className="absolute left-2 top-2 text-notion-muted w-4 h-4" />
        <Input
          className="pl-8 pr-1 h-8 w-48"
          placeholder="Buscar na página..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          autoFocus
        />
      </div>
      
      {matchCount > 0 && (
        <div className="text-notion-muted text-xs">
          {currentMatch + 1}/{matchCount}
        </div>
      )}
      
      <button 
        className="p-1 text-notion-muted hover:text-notion-text hover:bg-notion-hover rounded-sm"
        onClick={handlePrevMatch}
      >
        <ArrowUp size={14} />
      </button>
      
      <button 
        className="p-1 text-notion-muted hover:text-notion-text hover:bg-notion-hover rounded-sm"
        onClick={handleNextMatch}
      >
        <ArrowDown size={14} />
      </button>
      
      <button 
        className="p-1 text-notion-muted hover:text-notion-text hover:bg-notion-hover rounded-sm ml-1"
        onClick={() => {
          setIsOpen(false);
          setSearchTerm('');
          onClearHighlight();
        }}
      >
        <X size={14} />
      </button>
    </div>
  );
};
