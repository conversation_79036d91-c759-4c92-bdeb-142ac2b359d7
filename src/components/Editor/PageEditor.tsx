import React, { useState, useEffect, useRef, useCallback } from "react";
import { useWorkspace } from "../../contexts/WorkspaceContext";
import { ScrollArea } from "../ui/scroll-area";
import { BlockMenu } from "./BlockMenu";
import { ImageUpload } from "./ImageUpload";
import { ContentBlock } from "./ContentBlock";
import { ContentSearch } from "./ContentSearch";
import { BlockType } from "../../types";
import { v4 as uuidv4 } from "uuid";
import { useDebounce } from "../../hooks/useDebounce";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useSubscription } from "@/contexts/SubscriptionProvider";
import { useAuth } from "@/contexts/AuthContext";
import { CollaborationOverlay } from "../Shared/CollaborationOverlay";

interface Block {
  id: string;
  type: BlockType;
  content: string;
  checked?: boolean;
  collapsed?: boolean;
  parentId?: string | null;
  level?: number;
}

export const PageEditor = () => {
  const { currentPage, updatePage, currentWorkspace } = useWorkspace();
  const { hasReachedLimit, isAdmin, isSubscribed } = useSubscription();
  const { user } = useAuth();
  const [blocks, setBlocks] = useState<Block[]>([{ id: uuidv4(), type: "empty_line" as BlockType, content: "" }]);
  const [showBlockMenu, setShowBlockMenu] = useState(false);
  const [blockMenuPosition, setBlockMenuPosition] = useState({ x: 0, y: 0 });
  const [activeBlockId, setActiveBlockId] = useState<string | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  const [isDirty, setIsDirty] = useState(false);
  const [lastSavedBlocks, setLastSavedBlocks] = useState<string>("");
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Refs para uso no cleanup
  const currentPageRef = useRef(currentPage);
  const blocksRef = useRef(blocks);
  const isDirtyRef = useRef(isDirty);
  const lastSavedBlocksRef = useRef(lastSavedBlocks);

  const [draggedBlockId, setDraggedBlockId] = useState<string | null>(null);

  const [searchHighlights, setSearchHighlights] = useState<string | null>(null);
  const [currentHighlightIndex, setCurrentHighlightIndex] = useState(0);

  // Estados para controle de modais e UI
  const [showSearch, setShowSearch] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);

  // Verificar se o usuário é o proprietário do workspace atual
  const isWorkspaceOwner = currentWorkspace && user ? currentWorkspace.ownerId === user.id : false;

  // Verificar se o plano está expirado para limitar certas operações
  const isPlanExpired = isWorkspaceOwner && hasReachedLimit && !isSubscribed && !isAdmin;

  // Não bloquear completamente a edição, permitindo editar e adicionar conteúdo
  const isReadOnly = false;

  // Permitir adicionar novos conteúdos em páginas existentes
  const canAddNewContent = true;

  // Atualizar refs quando os valores mudarem
  useEffect(() => {
    currentPageRef.current = currentPage;
    blocksRef.current = blocks;
    isDirtyRef.current = isDirty;
    lastSavedBlocksRef.current = lastSavedBlocks;
  }, [currentPage, blocks, isDirty, lastSavedBlocks]);

  // Função para realizar o salvamento sem toasts
  const savePageSilently = useCallback(() => {
    if (!isDirty || isReadOnly || !currentPage) return false;

    const blocksJson = JSON.stringify(blocks);

    // Verificar se o conteúdo atual e o último salvo são realmente diferentes
    if (blocksJson === lastSavedBlocks) {
      console.log("Conteúdo sem alterações, ignorando salvamento silencioso");
      setIsDirty(false);
      return false;
    }

    // Verificar se houve mudanças reais no conteúdo
    const hasRealChanges = blocks.some((block) => block.content !== "" || (typeof block.content === "string" && block.content.trim() !== ""));

    if (!hasRealChanges) {
      console.log("Sem mudanças significativas, ignorando salvamento silencioso");
      setIsDirty(false);
      return false;
    }

    updatePage(currentPage.id, {
      content: blocksJson,
    })
      .then(() => {
        setIsDirty(false);
        setLastSavedBlocks(blocksJson);
        console.log("Autosave: Página salva silenciosamente");
      })
      .catch((error) => {
        console.error("Erro no autosave:", error);
      });

    return true;
  }, [blocks, isDirty, isReadOnly, currentPage, updatePage, lastSavedBlocks]);

  // Iniciar timer para autosave quando o usuário parar de digitar
  const startAutosaveTimer = useCallback(() => {
    // Indica que o usuário está digitando
    setIsTyping(true);

    // Limpa o timeout anterior, se existir
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Define um novo timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);

      // Só salva se estiver dirty
      if (isDirty) {
        savePageSilently();
      }
    }, 2000); // 2 segundos após parar de digitar
  }, [isDirty, savePageSilently]);

  // Efeito para limpar o timeout quando o componente desmontar
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Efeito para salvar quando ocorrer mudança de página
  useEffect(() => {
    return () => {
      // Este código executa quando o componente for desmontar (mudança de página)
      if (isDirtyRef.current && !isReadOnly && currentPageRef.current) {
        const blocksJson = JSON.stringify(blocksRef.current);
        if (blocksJson !== lastSavedBlocksRef.current) {
          updatePage(currentPageRef.current.id, {
            content: blocksJson,
          }).then(() => {
            console.log("Página salva ao navegar para outra página");
          });
        }
      }
    };
  }, [updatePage, isReadOnly]);

  // Função para verificar se uma ação deve ser permitida com base nas limitações
  const checkActionAllowed = (action: () => void) => {
    // Executar a ação independentemente do status da assinatura
    action();
  };

  // Restaurar a função handleSavePage para o evento manual
  useEffect(() => {
    const handleSavePage = () => {
      if (!isDirty || isReadOnly || !currentPage) return;

      const blocksJson = JSON.stringify(blocks);

      // Verificar se o conteúdo atual e o último salvo são realmente diferentes
      if (blocksJson === lastSavedBlocks) {
        console.log("Conteúdo sem alterações, ignorando salvamento manual");
        setIsDirty(false);
        return;
      }

      console.log("Manual save triggered: Saving blocks...");

      updatePage(currentPage.id, {
        content: blocksJson,
      })
        .then(() => {
          setIsDirty(false);
          setLastSavedBlocks(blocksJson);
          console.log("Blocks saved successfully.");
        })
        .catch((error) => {
          console.error("Error saving blocks manually:", error);
        });
    };

    window.addEventListener("save-page", handleSavePage);
    return () => {
      window.removeEventListener("save-page", handleSavePage);
    };
  }, [blocks, isDirty, isReadOnly, currentPage, updatePage, lastSavedBlocks]);

  useEffect(() => {
    if (currentPage) {
      if (currentPage.content) {
        try {
          const contentData = typeof currentPage.content === "string" ? JSON.parse(currentPage.content) : currentPage.content;

          if (Array.isArray(contentData) && contentData.length > 0) {
            setBlocks(contentData);
            // Armazenar estado inicial para comparações
            const blocksJson = JSON.stringify(contentData);
            setLastSavedBlocks(blocksJson);
            return;
          }
        } catch (e) {
          if (typeof currentPage.content === "string" && currentPage.content.length > 0) {
            const initialBlocks = [
              {
                id: uuidv4(),
                type: "text" as BlockType,
                content: currentPage.content,
              },
            ];
            setBlocks(initialBlocks);
            // Armazenar estado inicial para comparações
            const blocksJson = JSON.stringify(initialBlocks);
            setLastSavedBlocks(blocksJson);
            return;
          }
        }
      }
      const initialBlocks = [{ id: uuidv4(), type: "empty_line" as BlockType, content: "" }];
      setBlocks(initialBlocks);
      // Armazenar estado inicial para comparações
      const blocksJson = JSON.stringify(initialBlocks);
      setLastSavedBlocks(blocksJson);
    }
  }, [currentPage?.id]);

  useEffect(() => {
    const handleAddBlock = (e: CustomEvent) => {
      const blockType = e.detail?.type || "empty_line";

      const newBlock = {
        id: uuidv4(),
        type: blockType as BlockType,
        content: "",
        level: 0,
        parentId: null,
      };

      setBlocks((prevBlocks) => [...prevBlocks, newBlock]);
      setIsDirty(true);

      setTimeout(() => {
        setActiveBlockId(newBlock.id);
        focusBlock(newBlock.id);
      }, 10);
    };

    window.addEventListener("add-block", handleAddBlock as EventListener);
    return () => window.removeEventListener("add-block", handleAddBlock as EventListener);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        if (isDirty) {
          updatePage(currentPage!.id, {
            content: JSON.stringify(blocks),
          });
          setIsDirty(false);
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [blocks, isDirty, currentPage, updatePage]);

  useEffect(() => {
    const createBucket = async () => {
      const { data: existingBuckets, error: checkError } = await supabase.storage.listBuckets();

      if (checkError) {
        console.error("Error checking buckets:", checkError);
        return;
      }

      const bucketExists = existingBuckets.some((bucket) => bucket.name === "public");

      if (!bucketExists) {
        try {
          const { data, error } = await supabase.storage.createBucket("public", {
            public: true,
          });

          if (error) {
            console.error("Error creating bucket:", error);
          } else {
            console.log("Public bucket created successfully");
          }
        } catch (error) {
          console.error("Error creating storage bucket:", error);
        }
      } else {
        console.log("Public bucket already exists");
      }
    };

    createBucket();
  }, []);

  const handleContentChange = (blockId: string, content: string) => {
    if (isReadOnly) return;

    const newBlocks = blocks.map((block) => (block.id === blockId ? { ...block, content } : block));
    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();
  };

  const handleTypeChange = (blockId: string, newType: BlockType) => {
    if (isReadOnly) return;

    const newBlocks = blocks.map((block) => (block.id === blockId ? { ...block, type: newType } : block));
    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();
  };

  const handleToggleCheck = (blockId: string) => {
    if (isReadOnly) return;

    const newBlocks = blocks.map((block) => (block.id === blockId ? { ...block, checked: !block.checked } : block));
    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();
  };

  const handleToggleCollapse = (blockId: string) => {
    if (isReadOnly) return;

    const blockToToggle = blocks.find((block) => block.id === blockId);
    if (!blockToToggle) return;

    const newCollapsedState = !blockToToggle.collapsed;

    const newBlocks = blocks.map((block) => {
      if (block.id === blockId) {
        return { ...block, collapsed: newCollapsedState };
      }

      if (newCollapsedState && blockToToggle && isChildBlock(block, blockId)) {
        return { ...block, collapsed: true };
      }

      return block;
    });

    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();
  };

  const isChildBlock = (block: Block, parentId: string): boolean => {
    if (!block.parentId) return false;
    if (block.parentId === parentId) return true;

    const parentBlock = blocks.find((b) => b.id === block.parentId);
    if (!parentBlock) return false;

    return isChildBlock(parentBlock, parentId);
  };

  const handleDeleteBlock = (blockId: string) => {
    if (isReadOnly) return;

    // Se for o último bloco, substitui por um bloco vazio de texto em vez de excluir
    if (blocks.length <= 1) {
      const newBlock = { id: uuidv4(), type: "text" as BlockType, content: "" };
      setBlocks([newBlock]);
      setActiveBlockId(newBlock.id);

      // Se não estava dirty antes, agora está
      if (!isDirty) {
        setIsDirty(true);
      }

      // Inicia o timer para autosave
      startAutosaveTimer();

      setTimeout(() => focusBlock(newBlock.id), 10);
      return;
    }

    const newBlocks = blocks.filter((block) => block.id !== blockId);
    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();

    const deletedIndex = blocks.findIndex((block) => block.id === blockId);
    const newFocusIndex = Math.max(0, deletedIndex - 1);
    setActiveBlockId(newBlocks[newFocusIndex]?.id || null);
    setTimeout(() => {
      if (newBlocks[newFocusIndex]?.id) {
        focusBlock(newBlocks[newFocusIndex].id);
      }
    }, 10);
  };

  const focusBlock = (blockId: string) => {
    if (isReadOnly) return;

    setTimeout(() => {
      // Primeiro, procura pelo elemento de conteúdo do bloco específico
      const blockElement = document.querySelector(`[data-block-id="${blockId}"]`) as HTMLElement;

      if (blockElement) {
        // Coloca o cursor no final do texto
        blockElement.focus();

        // Para posicionar o cursor no final do texto
        if (document.createRange && window.getSelection) {
          const range = document.createRange();
          range.selectNodeContents(blockElement);
          range.collapse(false); // false significa colapsar para o final
          const selection = window.getSelection();
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
      }
    }, 10);
  };

  const handleKeyDown = (e: React.KeyboardEvent, blockId: string) => {
    if (isReadOnly) return;

    const currentBlockIndex = blocks.findIndex((block) => block.id === blockId);
    const currentBlock = blocks[currentBlockIndex];

    if (!currentBlock) return;

    if (e.key === "Tab") {
      e.preventDefault();

      if (e.shiftKey) {
        // Reduzir nível (unindent)
        if (currentBlock.level && currentBlock.level > 0) {
          const parentBlock = blocks.find((b) => b.id === currentBlock.parentId);
          const grandparentId = parentBlock?.parentId || null;

          const newBlocks = [...blocks];
          newBlocks[currentBlockIndex] = {
            ...newBlocks[currentBlockIndex],
            level: Math.max(0, (currentBlock.level || 1) - 1),
            parentId: grandparentId,
          };

          setBlocks(newBlocks);

          // Se não estava dirty antes, agora está
          if (!isDirty) {
            setIsDirty(true);
          }

          // Inicia o timer para autosave
          startAutosaveTimer();

          console.log(`Tab+Shift: Reduzindo nível do bloco ${blockId}`);

          // Focar novamente para garantir que a UI seja atualizada
          setTimeout(() => focusBlock(blockId), 10);
        }
      } else {
        // Aumentar nível (indent) - adicionar como subitem da linha acima
        let previousVisibleBlockIndex = -1;

        // Encontrar o bloco visível anterior
        for (let i = currentBlockIndex - 1; i >= 0; i--) {
          const potentialPreviousBlock = blocks[i];
          if (isBlockVisible(potentialPreviousBlock)) {
            previousVisibleBlockIndex = i;
            break;
          }
        }

        if (previousVisibleBlockIndex !== -1) {
          const previousBlock = blocks[previousVisibleBlockIndex];

          // Sempre incremente em 1 o nível atual e defina o parent como o bloco anterior
          const newLevel = (currentBlock.level || 0) + 1;
          const newParentId = previousBlock.id;

          console.log(`Tab: Transformando bloco ${blockId} em subitem de ${previousBlock.id}`);

          // Criar novo array de blocos (mutação imutável)
          const newBlocks = [...blocks];

          // Atualizar o bloco atual para ter o novo nível e parentId
          newBlocks[currentBlockIndex] = {
            ...newBlocks[currentBlockIndex],
            level: newLevel,
            parentId: newParentId,
          };

          // Atualizar o estado de blocos e marcar como sujo para salvar
          setBlocks(newBlocks);

          // Se não estava dirty antes, agora está
          if (!isDirty) {
            setIsDirty(true);
          }

          // Inicia o timer para autosave
          startAutosaveTimer();

          // Focar novamente para garantir que a UI seja atualizada
          setTimeout(() => focusBlock(blockId), 10);
        } else {
          console.log("Não foi possível encontrar um bloco visível anterior para criar subitem");
        }
      }
    } else if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();

      // Manter o tipo apenas se for uma lista ou um to-do
      const newType: BlockType = ["bulleted_list", "numbered_list", "todo", "toggle"].includes(currentBlock.type)
        ? (currentBlock.type as BlockType)
        : "empty_line";
      const newLevel = currentBlock.level || 0;
      const newParentId = currentBlock.parentId;

      const newBlock = {
        id: uuidv4(),
        type: newType,
        content: "",
        checked: newType === "todo" ? false : undefined,
        collapsed: newType === "toggle" ? false : undefined,
        level: newLevel,
        parentId: newParentId,
      };

      const newBlocks = [...blocks.slice(0, currentBlockIndex + 1), newBlock, ...blocks.slice(currentBlockIndex + 1)];

      setBlocks(newBlocks);

      // Se não estava dirty antes, agora está
      if (!isDirty) {
        setIsDirty(true);
      }

      // Inicia o timer para autosave
      startAutosaveTimer();

      setTimeout(() => {
        setActiveBlockId(newBlock.id);
        focusBlock(newBlock.id);
      }, 0);
    } else if (e.key === "/" && currentBlock.type !== "code") {
      e.preventDefault();
      const rect = (e.target as Element).getBoundingClientRect();

      setBlockMenuPosition({
        x: rect.left,
        y: rect.top - 10,
      });
      setActiveBlockId(blockId);
      setShowBlockMenu(true);
    } else if (e.key === "Backspace" && !currentBlock.content.trim() && blocks.length > 1) {
      e.preventDefault();
      handleDeleteBlock(blockId);
    } else if (e.key === "Escape") {
      setShowBlockMenu(false);
    }
  };

  const handleBlockSelect = (blockType: BlockType) => {
    if (isReadOnly) return;

    setShowBlockMenu(false);

    if (!activeBlockId) return;

    const currentBlockIndex = blocks.findIndex((block) => block.id === activeBlockId);

    if (currentBlockIndex === -1) return;

    const newBlocks = [...blocks];
    newBlocks[currentBlockIndex] = {
      ...newBlocks[currentBlockIndex],
      type: blockType,
      content:
        blockType === "columns"
          ? '<div class="flex gap-4 w-full"><div class="flex-1">Coluna 1</div><div class="flex-1">Coluna 2</div></div>'
          : blockType === "table"
          ? `<table class="w-full border-collapse">
              <thead>
                <tr class="bg-gray-100">
                  <th class="border border-notion-border p-2">Coluna 1</th>
                  <th class="border border-notion-border p-2">Coluna 2</th>
                  <th class="border border-notion-border p-2">Coluna 3</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="border border-notion-border p-2">Célula 1</td>
                  <td class="border border-notion-border p-2">Célula 2</td>
                  <td class="border border-notion-border p-2">Célula 3</td>
                </tr>
                <tr>
                  <td class="border border-notion-border p-2">Célula 4</td>
                  <td class="border border-notion-border p-2">Célula 5</td>
                  <td class="border border-notion-border p-2">Célula 6</td>
                </tr>
              </tbody>
            </table>`
          : blockType === "todo" || blockType === "toggle"
          ? "" // Deixar o conteúdo vazio para edição pelo usuário
          : blockType === "divider" || blockType === "empty_line"
          ? "" // Os tipos divider e empty_line não precisam de conteúdo
          : "",
      checked: blockType === "todo" ? false : undefined,
      collapsed: blockType === "toggle" ? false : undefined,
    };

    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();

    setTimeout(() => {
      focusBlock(activeBlockId);
    }, 10);
  };

  const handleCoverImageChange = async (url: string) => {
    if (currentPage && !isReadOnly) {
      updatePage(currentPage.id, { cover: url });
    }
  };

  const handleEditorClick = (e: React.MouseEvent) => {
    if (!canAddNewContent) return;

    if (editorRef.current && e.target === editorRef.current) {
      const lastBlockElement = editorRef.current.lastElementChild;
      if (lastBlockElement) {
        const editorRect = editorRef.current.getBoundingClientRect();
        const lastBlockRect = lastBlockElement.getBoundingClientRect();
        if (e.clientY > lastBlockRect.bottom + 10) {
          const newBlock = {
            id: uuidv4(),
            type: "empty_line" as BlockType,
            content: "",
            level: 0,
            parentId: null,
          };
          setBlocks([...blocks, newBlock]);
          setIsDirty(true);
        }
      }
    }
  };

  const handleDuplicateBlock = (blockId: string) => {
    if (isReadOnly) return;

    const blockToDuplicate = blocks.find((block) => block.id === blockId);
    if (!blockToDuplicate) return;

    const newBlock = {
      ...blockToDuplicate,
      id: uuidv4(),
      content: blockToDuplicate.content,
    };

    const blockIndex = blocks.findIndex((block) => block.id === blockId);
    const newBlocks = [...blocks.slice(0, blockIndex + 1), newBlock, ...blocks.slice(blockIndex + 1)];

    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();
  };

  const handleDragStart = (e: React.DragEvent, blockId: string) => {
    if (isReadOnly) return;

    setDraggedBlockId(blockId);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragEnd = () => {
    if (isReadOnly) return;

    setDraggedBlockId(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (isReadOnly) return;

    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (isReadOnly) return;

    if (!draggedBlockId || draggedBlockId === targetId) return;

    // Encontra os blocos de origem e destino
    const sourceIndex = blocks.findIndex((block) => block.id === draggedBlockId);
    const targetIndex = blocks.findIndex((block) => block.id === targetId);

    if (sourceIndex === -1 || targetIndex === -1) return;

    const sourceBlock = blocks[sourceIndex];
    const targetBlock = blocks[targetIndex];

    // Verificar a posição do mouse em relação ao elemento alvo
    const targetElement = e.currentTarget as HTMLElement;
    const targetRect = targetElement.getBoundingClientRect();
    const mouseY = e.clientY;
    const targetMiddle = targetRect.top + targetRect.height / 2;
    const dropZoneSize = targetRect.height * 0.3; // 30% da altura do elemento é a zona de "subitem"

    const newBlocks = [...blocks];

    // Se o mouse está próximo do meio do elemento, criar como subitem
    if (Math.abs(mouseY - targetMiddle) < dropZoneSize) {
      console.log(`Transformando bloco ${draggedBlockId} em subitem de ${targetId}`);

      // Remove o bloco arrastado da posição atual
      const [draggedBlock] = newBlocks.splice(sourceIndex, 1);

      // Modifica o bloco para se tornar subitem do alvo
      const updatedBlock = {
        ...draggedBlock,
        level: (targetBlock.level || 0) + 1,
        parentId: targetId,
      };

      // Insere o bloco modificado após o bloco alvo
      newBlocks.splice(targetIndex + 1, 0, updatedBlock);

      // Usa console.log em vez de toast para evitar notificações de autosave
      console.log("Bloco adicionado como subitem");
    } else {
      // Comportamento original: apenas reordenar na mesma hierarquia
      // Remove o bloco da posição atual
      const [draggedBlock] = newBlocks.splice(sourceIndex, 1);

      // Preserva o nível hierárquico e parentId do bloco arrastado
      // Insere na nova posição
      newBlocks.splice(targetIndex, 0, draggedBlock);
    }

    setBlocks(newBlocks);

    // Se não estava dirty antes, agora está
    if (!isDirty) {
      setIsDirty(true);
    }

    // Inicia o timer para autosave
    startAutosaveTimer();

    setDraggedBlockId(null);
  };

  const isBlockVisible = (block: Block): boolean => {
    if (!block.parentId) return true;

    const parentBlock = blocks.find((b) => b.id === block.parentId);
    if (!parentBlock) return true;

    if (parentBlock.collapsed) return false;

    return isBlockVisible(parentBlock);
  };

  const handleImageDrop = async (blockId: string, file: File) => {
    if (isReadOnly) return;

    if (!file || !file.type.startsWith("image/")) {
      toast.error("Apenas arquivos de imagem são permitidos");
      return;
    }

    try {
      toast.loading("Enviando imagem...");

      const fileExt = file.name.split(".").pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `uploads/${fileName}`;

      const { data, error } = await supabase.storage.from("public").upload(filePath, file);

      if (error) {
        console.error("Error uploading image:", error);
        toast.error("Falha ao enviar a imagem");
        return;
      }

      const { data: publicUrlData } = supabase.storage.from("public").getPublicUrl(filePath);

      const imageUrl = publicUrlData.publicUrl;

      const newBlocks = blocks.map((block) => {
        if (block.id === blockId) {
          let blockType = block.type;
          if (blockType !== "image") {
            blockType = "image";
          }

          return {
            ...block,
            type: blockType,
            content: `<img src="${imageUrl}" alt="Imagem" class="max-w-full h-auto rounded-md" />`,
          };
        }
        return block;
      });

      setBlocks(newBlocks);

      // Se não estava dirty antes, agora está
      if (!isDirty) {
        setIsDirty(true);
      }

      // Inicia o timer para autosave
      startAutosaveTimer();

      toast.success("Imagem enviada com sucesso");
    } catch (error) {
      console.error("Error handling image:", error);
      toast.error("Falha ao processar a imagem");
    } finally {
      toast.dismiss();
    }
  };

  const highlightContentMatches = (query: string, goToIndex?: number) => {
    if (!query) {
      setSearchHighlights(null);
      return;
    }

    setSearchHighlights(query);

    setTimeout(() => {
      const highlights = document.querySelectorAll('[data-highlighted="true"]');
      const highlightCount = highlights.length;

      if (highlightCount > 0) {
        const index = typeof goToIndex === "number" ? goToIndex % highlightCount : 0;
        setCurrentHighlightIndex(index);

        const activeHighlight = highlights[index] as HTMLElement;
        if (activeHighlight) {
          activeHighlight.classList.add("bg-yellow-300");
          activeHighlight.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }
    }, 50);
  };

  const clearContentHighlights = () => {
    setSearchHighlights(null);
    setCurrentHighlightIndex(0);
  };

  // Adicionar suporte para Realtime do Supabase
  useEffect(() => {
    if (!currentPage || !currentPage.id) return;

    // Armazenar referência para o último usuário que modificou a página
    const lastModifiedByUserId = user?.id;

    // Inscrever para mudanças em tempo real na tabela de páginas
    const subscription = supabase
      .channel(`page-${currentPage.id}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "pages",
          filter: `id=eq.${currentPage.id}`,
        },
        (payload) => {
          // Verificar se a atualização foi feita por outro usuário
          if (payload.new && payload.new.updated_by && payload.new.updated_by !== user?.id) {
            console.log("Página atualizada por outro usuário:", payload.new.updated_by);

            // Atualizar o conteúdo apenas se não estamos no meio de uma edição
            if (!isDirty && !isTyping) {
              // Extrair o conteúdo do payload
              const newContent = payload.new.content;

              // Somente atualizar se o conteúdo é diferente do que estamos vendo
              if (newContent && JSON.stringify(blocks) !== newContent) {
                try {
                  const contentData = typeof newContent === "string" ? JSON.parse(newContent) : newContent;

                  if (Array.isArray(contentData) && contentData.length > 0) {
                    toast.info("A página foi atualizada por outro usuário");
                    setBlocks(contentData);
                    // Armazenar estado inicial para comparações
                    setLastSavedBlocks(JSON.stringify(contentData));
                  }
                } catch (e) {
                  console.error("Erro ao processar alterações em tempo real:", e);
                }
              }
            } else {
              // Se estamos editando, exibir uma notificação de conflito
              toast.warning("Outro usuário modificou esta página. Suas alterações podem gerar um conflito.", {
                duration: 5000,
                action: {
                  label: "Recarregar",
                  onClick: () => {
                    window.location.reload();
                  },
                },
              });
            }
          }
        }
      )
      .subscribe();

    // Limpar a inscrição quando desmontar
    return () => {
      subscription.unsubscribe();
    };
  }, [currentPage, user, blocks, isDirty, isTyping]);

  // Ref para o container do editor
  const containerRef = useRef<HTMLDivElement>(null);

  // Função para upload de imagem para capa
  const handleImageUpload = async (file: File, isCover = false) => {
    try {
      toast.loading("Enviando imagem...");

      const fileExt = file.name.split(".").pop();
      const fileName = `${isCover ? "cover" : "image"}_${uuidv4()}.${fileExt}`;
      const filePath = `${isCover ? "covers" : "images"}/${fileName}`;

      const { data, error } = await supabase.storage.from("public").upload(filePath, file);

      if (error) {
        console.error("Error uploading image:", error);
        toast.error("Falha ao enviar a imagem");
        return;
      }

      const { data: publicUrlData } = supabase.storage.from("public").getPublicUrl(filePath);
      const imageUrl = publicUrlData.publicUrl;

      if (isCover) {
        handleCoverImageChange(imageUrl);
      }

      toast.success("Imagem enviada com sucesso");
      return imageUrl;
    } catch (error) {
      console.error("Error handling image:", error);
      toast.error("Falha ao processar a imagem");
    } finally {
      toast.dismiss();
    }
  };

  // Função para tratar o upload concluído
  const handleImageUploadComplete = async (imageUrl: string) => {
    setShowImageUpload(false);

    if (!activeBlockId) return;

    // Criar bloco de imagem com a URL
    const imgHtml = `<div class="flex justify-center"><img src="${imageUrl}" alt="Imagem" class="max-w-full" /></div>`;
    handleContentChange(activeBlockId, imgHtml);
    handleTypeChange(activeBlockId, "image");
  };

  if (!currentPage) {
    return (
      <div className="h-full flex items-center justify-center text-notion-muted">
        <p>Selecione uma página para começar a editar</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full" ref={containerRef}>
      {isPlanExpired && (
        <div className="w-full bg-amber-800 text-white p-3 flex justify-between items-center">
          <div className="flex items-center">
            <span>
              Você atingiu o limite gratuito e possui acesso limitado aos recursos. Assine agora um de nossos planos para continuar tendo uma
              experiência Premium.
            </span>
          </div>
          <button
            className="bg-amber-700 hover:bg-amber-600 px-4 py-1 rounded-md text-sm"
            onClick={() => window.dispatchEvent(new CustomEvent("open-subscription-modal"))}
          >
            Fazer upgrade do seu plano
          </button>
        </div>
      )}

      {currentPage.cover && (
        <div className="relative">
          <img src={currentPage.cover} alt="Capa da página" className="w-full h-48 object-cover" />
          <button
            onClick={() => {
              if (isReadOnly) return;
              handleCoverImageChange("");
            }}
            className="absolute top-2 right-2 bg-white bg-opacity-80 text-notion-text hover:bg-opacity-100 p-1 rounded text-xs"
          >
            Remover capa
          </button>
        </div>
      )}

      {!currentPage?.cover && (
        <div className="flex justify-center p-4 text-center">
          <button
            onClick={() => {
              if (isReadOnly) return;
              const fileInput = document.createElement("input");
              fileInput.type = "file";
              fileInput.accept = "image/*";
              fileInput.onchange = (e) => {
                const files = (e.target as HTMLInputElement).files;
                if (files && files.length > 0) {
                  handleImageUpload(files[0], true);
                }
              };
              fileInput.click();
            }}
            className="text-sm text-notion-muted hover:text-notion-text mb-2"
          >
            Adicionar capa
          </button>
        </div>
      )}

      <ScrollArea className="flex-1">
        <div className="w-[95%] mx-auto py-8 px-4 min-h-screen" ref={editorRef} onClick={handleEditorClick}>
          {blocks.map((block) => {
            const isVisible = isBlockVisible(block);
            const level = block.level || 0;

            if (!isVisible) return null;

            return (
              <div
                key={block.id}
                className={`relative ${level > 0 ? "mt-1 mb-1" : ""}`}
                style={{
                  paddingLeft: level > 0 ? `${level * 24}px` : "0",
                }}
              >
                {level > 0 && (
                  <div
                    className="absolute left-0 top-0 bottom-0 border-l-2 border-gray-300"
                    style={{
                      height: "100%",
                    }}
                  />
                )}
                <ContentBlock
                  id={block.id}
                  type={block.type}
                  content={block.content}
                  checked={block.checked}
                  collapsed={block.collapsed}
                  onContentChange={handleContentChange}
                  onTypeChange={handleTypeChange}
                  onKeyDown={handleKeyDown}
                  onFocus={setActiveBlockId}
                  onBlur={() => {}}
                  onDelete={handleDeleteBlock}
                  onToggleCheck={handleToggleCheck}
                  onToggleCollapse={handleToggleCollapse}
                  onDuplicate={handleDuplicateBlock}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onImageDrop={handleImageDrop}
                  readOnly={isReadOnly}
                  searchHighlight={searchHighlights}
                />
              </div>
            );
          })}

          {showBlockMenu && activeBlockId && !isReadOnly && (
            <BlockMenu position={blockMenuPosition} onSelect={handleBlockSelect} onClose={() => setShowBlockMenu(false)} />
          )}
        </div>
      </ScrollArea>

      {/* Adicionar overlay de colaboração */}
      {currentPage && <CollaborationOverlay containerRef={containerRef} />}

      {showSearch && (
        <ContentSearch
          onHighlight={(highlight, index) => {
            setSearchHighlights(highlight);
            setCurrentHighlightIndex(index);
          }}
          onClearHighlight={() => setSearchHighlights(null)}
        />
      )}

      {showImageUpload && <ImageUpload onUpload={handleImageUploadComplete} onClose={() => setShowImageUpload(false)} />}
    </div>
  );
};
