import React, { useState } from "react";
import { useRealtime } from "@/contexts/RealtimeContext";
import { StickyNote } from "@/types";
import { StickyNoteComponent } from "../Shared/StickyNoteComponent";

interface StickyNoteBlockProps {
  id: string;
  content: string;
  onDelete: (id: string) => void;
}

export const StickyNoteBlock: React.FC<StickyNoteBlockProps> = ({ id, content, onDelete }) => {
  const { createStickyNote } = useRealtime();
  const [isCreated, setIsCreated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Tentar analisar o conteúdo se já existir
  const [noteData, setNoteData] = useState<StickyNote | null>(() => {
    if (content) {
      try {
        return JSON.parse(content);
      } catch (e) {
        return null;
      }
    }
    return null;
  });

  // Criar uma nova sticky note se ainda não criada
  const createNewNote = async () => {
    if (isCreated || noteData || isLoading) return;

    setIsLoading(true);
    try {
      // Calcular uma posição central relativa à janela
      const centerX = Math.max(window.innerWidth / 2 - 100, 50);
      const centerY = Math.max(window.innerHeight / 2 - 100, 50);

      // Adicionar uma pequena aleatoriedade para evitar sobreposição quando múltiplos usuários criam notas
      const randomOffset = 20;
      const x = centerX + Math.random() * randomOffset - randomOffset / 2;
      const y = centerY + Math.random() * randomOffset - randomOffset / 2;

      // Selecionar uma cor aleatória
      const colors = ["#FFD133", "#33FF57", "#3357FF", "#FF33A8", "#33FFF6", "#FF5733", "#9E33FF"];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];

      const note = await createStickyNote("Digite sua nota aqui...", x, y, randomColor);

      if (note) {
        setNoteData(note);
        setIsCreated(true);
      }
    } catch (error) {
      console.error("Erro ao criar nota adesiva:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Criar a nota automaticamente quando o componente for montado
  React.useEffect(() => {
    if (!isCreated && !noteData && !isLoading) {
      createNewNote();
    }
  }, [isCreated, noteData, isLoading]);

  if (!noteData) {
    return (
      <div className="flex justify-center items-center p-4 border border-dashed border-gray-300 rounded-md my-4">
        <span className="text-notion-muted">{isLoading ? "Criando nota adesiva..." : "Aguardando criação da nota..."}</span>
      </div>
    );
  }

  return (
    <div className="my-4 relative min-h-[200px]">
      <div className="sticky-note-preview relative">
        <StickyNoteComponent note={noteData} />
      </div>
    </div>
  );
};
