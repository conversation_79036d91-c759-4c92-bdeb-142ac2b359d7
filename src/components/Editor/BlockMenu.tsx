import React, { useRef, useEffect, useState } from "react";
import {
  Text,
  Heading1,
  Heading2,
  Heading3,
  CheckSquare,
  ChevronRight,
  Code,
  Columns,
  ListOrdered,
  List,
  Image,
  Bookmark,
  Table,
  Layout,
  Kanban,
  SeparatorHorizontal,
  Space,
  Code2,
} from "lucide-react";
import { BlockType } from "../../types";

interface BlockMenuProps {
  position: { x: number; y: number };
  onSelect: (blockType: BlockType) => void;
  onClose: () => void;
}

export const BlockMenu: React.FC<BlockMenuProps> = ({ position, onSelect, onClose }) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    // Focus the search input when menu is shown
    const searchInput = menuRef.current?.querySelector("input");
    if (searchInput) {
      setTimeout(() => searchInput.focus(), 50);
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  const blockOptions = [
    {
      id: "text",
      name: "Texto",
      icon: <Text className="w-4 h-4" />,
      description: "Texto simples, começa com parágrafo.",
    },
    {
      id: "heading_1",
      name: "Título 1",
      icon: <Heading1 className="w-4 h-4" />,
      description: "Título de seção grande.",
    },
    {
      id: "heading_2",
      name: "Título 2",
      icon: <Heading2 className="w-4 h-4" />,
      description: "Título de seção médio.",
    },
    {
      id: "heading_3",
      name: "Título 3",
      icon: <Heading3 className="w-4 h-4" />,
      description: "Título de seção pequeno.",
    },
    {
      id: "bulleted_list",
      name: "Lista com Marcadores",
      icon: <List className="w-4 h-4" />,
      description: "Lista simples com marcadores.",
    },
    {
      id: "numbered_list",
      name: "Lista Numerada",
      icon: <ListOrdered className="w-4 h-4" />,
      description: "Lista numerada sequencial.",
    },
    {
      id: "todo",
      name: "Lista de Tarefas",
      icon: <CheckSquare className="w-4 h-4" />,
      description: "Lista de itens com caixas de seleção.",
    },
    {
      id: "toggle",
      name: "Toggle",
      icon: <ChevronRight className="w-4 h-4" />,
      description: "Conteúdo expansível que pode ser ocultado.",
    },
    {
      id: "code",
      name: "Código",
      icon: <Code className="w-4 h-4" />,
      description: "Bloco de código com realce de sintaxe.",
    },
    {
      id: "columns",
      name: "Colunas",
      icon: <Columns className="w-4 h-4" />,
      description: "Conteúdo dividido em colunas.",
    },
    {
      id: "image",
      name: "Imagem",
      icon: <Image className="w-4 h-4" />,
      description: "Imagem, arquivo ou vídeo.",
    },
    {
      id: "callout",
      name: "Destaque",
      icon: <Bookmark className="w-4 h-4" />,
      description: "Colorido bloco em destaque.",
    },
    {
      id: "table",
      name: "Tabela",
      icon: <Table className="w-4 h-4" />,
      description: "Tabela para organizar dados.",
    },
    {
      id: "dashboard",
      name: "Dashboard",
      icon: <Layout className="w-4 h-4" />,
      description: "Painel de controle do workspace.",
    },
    {
      id: "kanban",
      name: "Kanban",
      icon: <Kanban className="w-4 h-4" />,
      description: "Quadro Kanban para organizar tarefas.",
    },
    {
      id: "divider",
      name: "Divisor",
      icon: <SeparatorHorizontal className="w-4 h-4" />,
      description: "Linha horizontal para separar conteúdo.",
    },
    {
      id: "empty_line",
      name: "Linha em Branco",
      icon: <Space className="w-4 h-4" />,
      description: "Espaço em branco que permite edição ao clicar.",
    },
    {
      id: "html_editor",
      name: "Editor HTML",
      icon: <Code2 className="w-4 h-4" />,
      description: "Editor HTML com preview e templates.",
    },
  ] as const;

  const filteredBlocks = blockOptions.filter((block) => block.name.toLowerCase().includes(searchQuery.toLowerCase()));

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex((prev) => (prev < filteredBlocks.length - 1 ? prev + 1 : prev));
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex((prev) => (prev > 0 ? prev - 1 : 0));
    } else if (e.key === "Enter") {
      e.preventDefault();
      if (filteredBlocks.length > 0) {
        onSelect(filteredBlocks[selectedIndex].id as BlockType);
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      onClose();
    }
  };

  // Calculate the position with some smart positioning to avoid going off-screen
  const calculatePosition = () => {
    const menuWidth = 264; // approximate width
    const menuHeight = Math.min(filteredBlocks.length * 32 + 100, 320); // approximate height with max

    let x = position.x;
    let y = position.y;

    // Ensure the menu doesn't go off the right edge
    if (x + menuWidth > window.innerWidth) {
      x = window.innerWidth - menuWidth - 20;
    }

    // Ensure the menu doesn't go off the bottom edge
    if (y + menuHeight > window.innerHeight + window.scrollY) {
      y = Math.max(position.y - menuHeight, window.scrollY + 10);
    }

    return { x, y };
  };

  const { x, y } = calculatePosition();

  return (
    <div
      ref={menuRef}
      className="absolute bg-notion-page border border-notion-border shadow-lg rounded-md z-50 min-w-64 overflow-hidden"
      style={{
        left: `${x}px`,
        // top: `${y}px`,
        top: `30%`,
        width: "400px",
      }}
      onKeyDown={handleKeyDown}
    >
      <div className="p-2 border-b border-notion-border">
        <input
          type="text"
          placeholder="Pesquisar..."
          className="w-full px-2 py-1 bg-transparent border-none outline-none text-notion-text text-sm"
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setSelectedIndex(0); // Reset selection when typing
          }}
          autoFocus
        />
      </div>
      <div className="py-2 max-h-64 overflow-auto">
        <div className="px-3 py-1 text-xs text-notion-muted border-b border-notion-border">BLOCOS BÁSICOS</div>
        {filteredBlocks.length > 0 ? (
          filteredBlocks.map((block, index) => (
            <div
              key={block.id}
              className={`flex items-center px-3 py-1.5 hover:bg-notion-hover cursor-pointer text-notion-text ${
                index === selectedIndex ? "bg-notion-hover" : ""
              }`}
              onClick={() => onSelect(block.id as BlockType)}
              onMouseEnter={() => setSelectedIndex(index)}
              role="button"
              tabIndex={0}
            >
              <div className="mr-2 text-notion-muted">{block.icon}</div>
              <div>{block.name}</div>
            </div>
          ))
        ) : (
          <div className="px-3 py-2 text-notion-muted text-sm">Nenhum resultado encontrado</div>
        )}
      </div>
    </div>
  );
};
