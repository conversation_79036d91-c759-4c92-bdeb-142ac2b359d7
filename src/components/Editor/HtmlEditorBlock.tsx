import React, { useState, useRef, useEffect } from "react";
import { <PERSON>, Eye, Edit3 } from "lucide-react";
import { Button } from "../ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";

interface HtmlEditorBlockProps {
  content: string;
  onChange: (content: string) => void;
  readOnly?: boolean;
}

export const HtmlEditorBlock: React.FC<HtmlEditorBlockProps> = ({ 
  content, 
  onChange, 
  readOnly = false 
}) => {
  const [htmlCode, setHtmlCode] = useState(content || '');
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setHtmlCode(content || '');
  }, [content]);

  const handleCodeChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCode = e.target.value;
    setHtmlCode(newCode);
    onChange(newCode);
  };

  const insertTemplate = (template: string) => {
    if (readOnly) return;
    
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentCode = htmlCode;
    
    const newCode = currentCode.substring(0, start) + template + currentCode.substring(end);
    setHtmlCode(newCode);
    onChange(newCode);

    // Reposition cursor after the inserted template
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + template.length, start + template.length);
    }, 0);
  };

  const templates = [
    {
      name: "Div Container",
      code: '<div class="container">\n  <p>Conteúdo aqui</p>\n</div>'
    },
    {
      name: "Botão",
      code: '<button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">\n  Clique aqui\n</button>'
    },
    {
      name: "Card",
      code: '<div class="bg-white shadow-md rounded-lg p-6 max-w-sm">\n  <h3 class="text-lg font-semibold mb-2">Título do Card</h3>\n  <p class="text-gray-600">Descrição do card aqui.</p>\n</div>'
    },
    {
      name: "Lista",
      code: '<ul class="list-disc pl-6">\n  <li>Item 1</li>\n  <li>Item 2</li>\n  <li>Item 3</li>\n</ul>'
    },
    {
      name: "Grid",
      code: '<div class="grid grid-cols-2 gap-4">\n  <div class="bg-gray-200 p-4">Coluna 1</div>\n  <div class="bg-gray-200 p-4">Coluna 2</div>\n</div>'
    },
    {
      name: "Alert",
      code: '<div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">\n  <strong>Atenção!</strong> Esta é uma mensagem de alerta.\n</div>'
    }
  ];

  return (
    <div className="w-full border border-notion-border rounded-md overflow-hidden">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'edit' | 'preview')} className="w-full">
        <div className="flex items-center justify-between bg-notion-hover px-3 py-2 border-b border-notion-border">
          <TabsList className="grid w-fit grid-cols-2 bg-transparent">
            <TabsTrigger 
              value="edit" 
              className="flex items-center gap-2 text-notion-text data-[state=active]:bg-notion-page"
            >
              <Edit3 className="h-3.5 w-3.5" />
              Editor
            </TabsTrigger>
            <TabsTrigger 
              value="preview" 
              className="flex items-center gap-2 text-notion-text data-[state=active]:bg-notion-page"
            >
              <Eye className="h-3.5 w-3.5" />
              Preview
            </TabsTrigger>
          </TabsList>
          
          <div className="flex items-center gap-1 text-notion-muted">
            <Code className="h-3.5 w-3.5" />
            <span className="text-xs">HTML Editor</span>
          </div>
        </div>

        <TabsContent value="edit" className="mt-0">
          <div className="flex">
            {/* Templates sidebar */}
            <div className="w-48 bg-notion-sidebar border-r border-notion-border p-2">
              <div className="text-xs font-medium text-notion-muted mb-2">TEMPLATES</div>
              <div className="space-y-1">
                {templates.map((template, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs h-8 text-notion-text hover:bg-notion-hover"
                    onClick={() => insertTemplate(template.code)}
                    disabled={readOnly}
                  >
                    {template.name}
                  </Button>
                ))}
              </div>
            </div>

            {/* Code editor */}
            <div className="flex-1">
              <textarea
                ref={textareaRef}
                value={htmlCode}
                onChange={handleCodeChange}
                placeholder="Digite seu código HTML aqui..."
                className="w-full h-64 p-3 font-mono text-sm bg-notion-page text-notion-text border-none outline-none resize-none"
                readOnly={readOnly}
                style={{ 
                  fontFamily: "'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace",
                  lineHeight: "1.5"
                }}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="preview" className="mt-0">
          <div className="min-h-64 p-4 bg-notion-page">
            {htmlCode ? (
              <div 
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: htmlCode }}
              />
            ) : (
              <div className="flex items-center justify-center h-32 text-notion-muted">
                <p>Adicione código HTML para ver o preview</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};