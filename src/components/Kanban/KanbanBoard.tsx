import React, { useState, useEffect, useMemo } from "react";
import { Task, TaskStatus, TaskSubtask, Profile } from "@/types/index";
import { useTasks } from "@/contexts/TasksContext";
import { TaskCard } from "./TaskCard";
import { TaskDialog } from "./TaskDialog";
import { Calendar } from "@/components/Shared/Calendar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useWorkspace } from "@/contexts/WorkspaceContext";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Loader2, RefreshCw, AlertTriangle, ChevronLeft, ChevronRight, CalendarIcon, Users, Search } from "lucide-react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { format, startOfMonth, endOfMonth, isWithinInterval, addMonths, subMonths } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "../ui/badge";
import { renderProfileName, isInviteProfile, canBeAssignedToTask } from "@/utils/profileUtils";

export const KanbanBoard = () => {
  const { tasks, updateTask, deleteTask, duplicateTask, loading, refreshTasks, error } = useTasks();
  const { workspaceMembers, currentWorkspace, getAllWorkspaceUsers } = useWorkspace();
  const [selectedTab, setSelectedTab] = useState<"kanban" | "calendar">("kanban");
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [isCreateMode, setIsCreateMode] = useState(true);
  const [filteredStatus, setFilteredStatus] = useState<string>("all");
  const [filteredUserId, setFilteredUserId] = useState<string>("all");
  const [showDialog, setShowDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [initialStartDate, setInitialStartDate] = useState<Date>(new Date());
  const [initialEndDate, setInitialEndDate] = useState<Date>(new Date());
  const [refreshing, setRefreshing] = useState(false);
  const [errorState, setErrorState] = useState<string | null>(null);
  const [allUsers, setAllUsers] = useState<Profile[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Novos estados para filtro de mês e múltiplos usuários
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isUserFilterOpen, setIsUserFilterOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const statusColumns: { id: TaskStatus; label: string }[] = [
    { id: "backlog", label: "Backlog" },
    { id: "aguardando", label: "Aguardando" },
    { id: "em_progresso", label: "Em Progresso" },
    { id: "concluido", label: "Concluído" },
  ];

  useEffect(() => {
    if (!currentWorkspace) {
      setErrorState("Selecione um workspace para visualizar as tarefas");
      setAllUsers([]);
    } else {
      setErrorState(null);
      loadAllUsers();
    }
  }, [currentWorkspace]);

  // Novo efeito para inicializar o filtro de usuário padrão
  useEffect(() => {
    if (filteredUserId && filteredUserId !== "all") {
      setSelectedUsers([filteredUserId]);
    } else {
      setSelectedUsers([]);
    }
  }, [filteredUserId]);

  // Função para carregar todos os usuários
  const loadAllUsers = async () => {
    if (!currentWorkspace) return;

    try {
      setLoadingUsers(true);
      setErrorState(null);
      const users = await getAllWorkspaceUsers(currentWorkspace.id);

      if (Array.isArray(users)) {
        console.log(
          `Carregados ${users.length} usuários para o Kanban:`,
          users.map((u) => ({ id: u.id, name: u.name, email: u.email, displayName: renderProfileName(u) }))
        );
        setAllUsers(users);
      } else {
        console.error("getAllWorkspaceUsers não retornou um array:", users);
        setAllUsers([]);
        setErrorState("Erro ao carregar usuários");
      }
    } catch (error) {
      console.error("Erro ao carregar todos os usuários:", error);
      setAllUsers([]);
      setErrorState("Erro ao carregar usuários");
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleRefreshTasks = async () => {
    if (!currentWorkspace) {
      toast.error("Selecione um workspace para visualizar as tarefas");
      return;
    }

    setRefreshing(true);
    setErrorState(null);

    try {
      await refreshTasks();
      await loadAllUsers(); // Recarregar usuários também
      toast.success("Dados atualizados com sucesso");
    } catch (error) {
      console.error("Erro ao atualizar dados:", error);
      toast.error("Erro ao atualizar dados");
      setErrorState("Não foi possível carregar os dados. Tente novamente.");
    } finally {
      setRefreshing(false);
    }
  };

  const handlePrevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const toggleUserFilter = (userId: string) => {
    setSelectedUsers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  const getUserById = (userId: string): Profile | undefined => {
    if (!userId) return undefined;
    return allUsers.find((user) => user.id === userId);
  };

  // Filtrar e ordenar usuários para exibição
  const getFilteredDisplayUsers = useMemo(() => {
    if (!Array.isArray(allUsers)) return [];

    return allUsers
      .filter((user) => user && canBeAssignedToTask(user))
      .sort((a, b) => {
        const nameA = renderProfileName(a).toLowerCase();
        const nameB = renderProfileName(b).toLowerCase();
        return nameA.localeCompare(nameB);
      });
  }, [allUsers]);

  // Renderizar nome do usuário
  const renderUserName = (user: Profile | undefined) => {
    return renderProfileName(user);
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setIsCreateMode(false);
    setIsTaskDialogOpen(true);
  };

  const handleAddTask = (startDate?: Date, endDate?: Date) => {
    if (!currentWorkspace) {
      toast.error("Selecione um workspace antes de criar uma tarefa");
      return;
    }

    setEditingTask(null);
    setIsCreateMode(true);

    // Validar as datas antes de configurá-las
    if (startDate && !isNaN(startDate.getTime())) {
      setInitialStartDate(startDate);
    } else {
      setInitialStartDate(new Date());
    }

    if (endDate && !isNaN(endDate.getTime())) {
      setInitialEndDate(endDate);
    } else {
      setInitialEndDate(new Date());
    }

    setIsTaskDialogOpen(true);
  };

  // Adicionar um ouvinte para o evento personalizado
  useEffect(() => {
    const addTaskHandler = () => handleAddTask();
    window.addEventListener("add-task", addTaskHandler);
    return () => {
      window.removeEventListener("add-task", addTaskHandler);
    };
  }, [currentWorkspace]);

  const handleTaskDialogClose = () => {
    setIsTaskDialogOpen(false);
    setEditingTask(null);
  };

  const handleDeleteTask = async (taskId: string) => {
    if (window.confirm("Tem certeza que deseja excluir esta tarefa?")) {
      try {
        const success = await deleteTask(taskId);
        if (success) {
          toast.success("Tarefa excluída com sucesso");
        } else {
          toast.error("Não foi possível excluir a tarefa");
        }
      } catch (error) {
        console.error("Erro ao excluir tarefa:", error);
        toast.error("Erro ao excluir tarefa");
      }
    }
  };

  const handleDuplicateTask = async (taskId: string) => {
    try {
      const newTask = await duplicateTask(taskId);
      if (newTask) {
        toast.success("Tarefa duplicada com sucesso");
      } else {
        toast.error("Não foi possível duplicar a tarefa");
      }
    } catch (error) {
      console.error("Erro ao duplicar tarefa:", error);
      toast.error("Erro ao duplicar tarefa");
    }
  };

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, taskId: string) => {
    e.dataTransfer.setData("taskId", taskId);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.add("bg-notion-hover");
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.currentTarget.classList.remove("bg-notion-hover");
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>, status: TaskStatus) => {
    e.preventDefault();
    e.currentTarget.classList.remove("bg-notion-hover");

    const taskId = e.dataTransfer.getData("taskId");
    if (taskId) {
      try {
        const updatedTask = await updateTask(taskId, { status });
        if (updatedTask) {
          const statusLabel = {
            backlog: "Backlog",
            aguardando: "Aguardando", 
            em_progresso: "Em Progresso",
            concluido: "Concluído"
          }[status] || status;
          toast.success(`Tarefa movida para ${statusLabel}`);
        }
      } catch (error) {
        console.error("Erro ao mover tarefa:", error);
        toast.error("Não foi possível mover a tarefa");
      }
    }
  };

  // Agrupar tarefas por status
  const groupedTasks = useMemo(() => {
    if (!Array.isArray(tasks)) {
      return {
        backlog: [],
        aguardando: [],
        em_progresso: [],
        concluido: [],
      };
    }

    let filtered = [...tasks];

    // Filtrar por usuários (múltiplos)
    if (selectedUsers.length > 0) {
      filtered = filtered.filter((task) => task.assigneeId && selectedUsers.includes(task.assigneeId));
    }

    // Filtrar por status
    if (filteredStatus !== "all") {
      filtered = filtered.filter((task) => task.status === filteredStatus);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((task) => {
        return (
          task.title?.toLowerCase().includes(query) ||
          task.description?.toLowerCase().includes(query) ||
          task.category?.toLowerCase().includes(query) ||
          task.organization?.toLowerCase().includes(query) ||
          task.assigneeName?.toLowerCase().includes(query)
        );
      });
    }

    // Separate backlog tasks from date-filtered tasks
    const backlogTasks = filtered.filter((task) => task.status === "backlog");
    const nonBacklogTasks = filtered.filter((task) => task.status !== "backlog");

    // Filtrar tarefas não-backlog por mês atual
    const startOfMonthDate = startOfMonth(currentMonth);
    const endOfMonthDate = endOfMonth(currentMonth);

    const dateFilteredTasks = nonBacklogTasks.filter((task) => {
      if (!task.startDate || !task.endDate) return false;

      const taskStartDate = new Date(task.startDate);
      const taskEndDate = new Date(task.endDate);

      // Verificar se as datas são válidas
      if (isNaN(taskStartDate.getTime()) || isNaN(taskEndDate.getTime())) {
        return false;
      }

      // Tarefa começa, termina ou atravessa o mês atual
      return (
        // Começa dentro do mês
        (taskStartDate >= startOfMonthDate && taskStartDate <= endOfMonthDate) ||
        // Termina dentro do mês
        (taskEndDate >= startOfMonthDate && taskEndDate <= endOfMonthDate) ||
        // Atravessa o mês (começa antes e termina depois)
        (taskStartDate <= startOfMonthDate && taskEndDate >= endOfMonthDate)
      );
    });

    return {
      backlog: backlogTasks,
      aguardando: dateFilteredTasks.filter((task) => task.status === "aguardando"),
      em_progresso: dateFilteredTasks.filter((task) => task.status === "em_progresso"),
      concluido: dateFilteredTasks.filter((task) => task.status === "concluido"),
    };
  }, [tasks, selectedUsers, filteredStatus, currentMonth, searchQuery]);

  const renderErrorState = () => (
    <div className="flex flex-col items-center justify-center p-8 bg-red-50 rounded-lg border border-red-200 text-red-800 my-4">
      <AlertTriangle className="w-10 h-10 text-red-500 mb-4" />
      <h3 className="text-lg font-medium">Erro ao carregar tarefas</h3>
      <p className="mb-4 text-center">{error || errorState}</p>
      <Button onClick={handleRefreshTasks} variant="outline" size="sm" className="bg-white border-red-300">
        <RefreshCw className="h-4 w-4 mr-2" />
        Tentar novamente
      </Button>
    </div>
  );

  return (
    <div className="p-4 h-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-notion-text">Quadro Kanban</h1>
        <div className="flex items-center gap-3">
          {/* Filtro de Mês */}
          <div className="flex items-center space-x-2 mr-2">
            <button onClick={handlePrevMonth} className="p-1 rounded-full hover:bg-notion-hover">
              <ChevronLeft className="h-4 w-4 text-notion-text" />
            </button>
            <span className="text-notion-text font-medium">{format(currentMonth, "MMMM yyyy", { locale: ptBR })}</span>
            <button onClick={handleNextMonth} className="p-1 rounded-full hover:bg-notion-hover">
              <ChevronRight className="h-4 w-4 text-notion-text" />
            </button>
          </div>

          {/* Campo de Pesquisa */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-notion-muted" />
            <input
              type="text"
              placeholder="Pesquisar tarefas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-[200px] bg-notion-sidebar border border-notion-border rounded-md text-notion-text placeholder-notion-muted focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Filtro de Status */}
          <Select value={filteredStatus} onValueChange={setFilteredStatus}>
            <SelectTrigger className="w-[180px] bg-notion-sidebar border-notion-border focus:ring-0 focus:ring-offset-0">
              <SelectValue placeholder="Filtrar por status" />
            </SelectTrigger>
            <SelectContent className="bg-notion-sidebar border-notion-border">
              <SelectItem value="all" className="text-notion-text">
                Todos os status
              </SelectItem>
              <SelectItem value="backlog" className="text-notion-text">
                Backlog
              </SelectItem>
              <SelectItem value="aguardando" className="text-notion-text">
                Aguardando
              </SelectItem>
              <SelectItem value="em_progresso" className="text-notion-text">
                Em Progresso
              </SelectItem>
              <SelectItem value="concluido" className="text-notion-text">
                Concluído
              </SelectItem>
            </SelectContent>
          </Select>

          {/* Filtro de Múltiplos Usuários */}
          <Popover open={isUserFilterOpen} onOpenChange={setIsUserFilterOpen}>
            <PopoverContent className="w-[250px] p-0 bg-notion-sidebar border-notion-border">
              <Command className="bg-notion-sidebar">
                <CommandInput placeholder="Buscar usuário..." className="h-9 border-none focus:ring-0 bg-notion-sidebar text-notion-text" />
                <ScrollArea className="h-[200px]">
                  {loadingUsers ? (
                    <div className="flex items-center justify-center p-4 text-sm text-notion-text">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Carregando usuários...
                    </div>
                  ) : (
                    <>
                      <CommandEmpty>Nenhum usuário encontrado.</CommandEmpty>
                      <CommandGroup heading="Usuários">
                        {getFilteredDisplayUsers.length > 0 ? (
                          getFilteredDisplayUsers.map((user: Profile) => {
                            const isSelected = selectedUsers.includes(user.id);

                            return (
                              <CommandItem
                                key={user.id}
                                onSelect={() => toggleUserFilter(user.id)}
                                className={`text-notion-text ${isSelected ? "bg-purple-500/30" : ""}`}
                              >
                                <div
                                  className={`mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-notion-border ${
                                    isSelected ? "bg-purple-500 text-white" : "opacity-50"
                                  }`}
                                >
                                  {isSelected ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-4 w-4">
                                      <path d="M5 13l4 4L19 7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                  ) : null}
                                </div>
                                <span>{renderProfileName(user)}</span>
                              </CommandItem>
                            );
                          })
                        ) : (
                          <div className="p-2 text-sm text-notion-muted">Nenhum usuário disponível</div>
                        )}
                      </CommandGroup>
                    </>
                  )}
                </ScrollArea>
              </Command>
            </PopoverContent>
          </Popover>

          <Button
            onClick={handleRefreshTasks}
            variant="outline"
            size="icon"
            className="bg-notion-sidebar border-notion-border text-notion-text"
            disabled={refreshing || loading || !currentWorkspace}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`} />
          </Button>

          <button
            onClick={() => handleAddTask()}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
            disabled={!currentWorkspace}
          >
            <Plus className="w-4 h-4 mr-1" />
            <span>Nova Tarefa</span>
          </button>
        </div>
      </div>

      {(error || errorState) && renderErrorState()}

      <Tabs defaultValue="kanban" value={selectedTab} onValueChange={(value) => setSelectedTab(value as "kanban" | "calendar")}>
        <TabsList className="mb-4">
          <TabsTrigger value="kanban" className="data-[state=active]:bg-notion-hover">
            Kanban
          </TabsTrigger>
          <TabsTrigger value="calendar" className="data-[state=active]:bg-notion-hover">
            Calendário
          </TabsTrigger>
        </TabsList>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-8 h-8 text-purple-600 animate-spin" />
            <span className="ml-2 text-notion-text">Carregando tarefas...</span>
          </div>
        ) : !currentWorkspace ? (
          <div className="text-center py-8 text-notion-muted">Selecione um workspace para visualizar as tarefas</div>
        ) : (
          <>
            <TabsContent value="kanban" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {statusColumns.map((column) => (
                  <div key={column.id} className="bg-notion-page rounded-md">
                    <div
                      className={`p-3 rounded-t-md flex justify-between items-center ${
                        column.id === "backlog" ? "bg-gray-500" : 
                        column.id === "aguardando" ? "bg-amber-500" : 
                        column.id === "em_progresso" ? "bg-blue-500" : "bg-green-500"
                      }`}
                    >
                      <h3 className="font-medium text-white">{column.label}</h3>
                      <span className="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
                        {groupedTasks[column.id as TaskStatus].length}
                      </span>
                    </div>
                    <div
                      className="p-3 min-h-[200px] h-[calc(100vh-240px)] overflow-y-auto"
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, column.id as TaskStatus)}
                    >
                      {groupedTasks[column.id as TaskStatus].length > 0 ? (
                        groupedTasks[column.id as TaskStatus].map((task) => (
                          <div key={task.id} draggable onDragStart={(e) => handleDragStart(e, task.id)}>
                            <TaskCard
                              task={task}
                              onEditClick={handleEditTask}
                              onDeleteClick={handleDeleteTask}
                              onDuplicateClick={handleDuplicateTask}
                            />
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-notion-muted py-4">Sem tarefas</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="calendar" className="space-y-4">
              <Calendar
                onEditTask={handleEditTask}
                onDeleteTask={handleDeleteTask}
                onDuplicateTask={handleDuplicateTask}
                initialMonth={currentMonth}
                selectedUsers={selectedUsers}
                allUsers={allUsers}
              />
            </TabsContent>
          </>
        )}
      </Tabs>

      {isTaskDialogOpen && (
        <TaskDialog
          open={isTaskDialogOpen}
          onClose={handleTaskDialogClose}
          task={editingTask}
          isCreateMode={isCreateMode}
          initialStartDate={initialStartDate}
          initialEndDate={initialEndDate}
          allUsers={allUsers}
        />
      )}
    </div>
  );
};
