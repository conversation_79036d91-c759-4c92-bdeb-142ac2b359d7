import React, { useState, useEffect, useRef } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { TaskAttachment } from "@/types";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, X, Download } from "lucide-react";
import { getAttachmentUrl } from "@/services/taskAttachmentService";
import { Loader2 } from "lucide-react";

interface TaskAttachmentViewerProps {
  open: boolean;
  onClose: () => void;
  attachments: TaskAttachment[];
  initialAttachmentId?: string;
}

export const TaskAttachmentViewer: React.FC<TaskAttachmentViewerProps> = ({ open, onClose, attachments, initialAttachmentId }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentUrl, setCurrentUrl] = useState<string | null>(null);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [rotation, setRotation] = useState(0);
  const imageRef = useRef<HTMLImageElement>(null);
  
  // Filtrar apenas imagens
  const imageAttachments = attachments.filter(att => att.mimeType?.startsWith('image/'));

  useEffect(() => {
    if (open && imageAttachments.length > 0) {
      let startIndex = 0;

      // Se tiver um ID inicial, encontra o índice correspondente
      if (initialAttachmentId) {
        const foundIndex = imageAttachments.findIndex((a) => a.id === initialAttachmentId);
        if (foundIndex >= 0) {
          startIndex = foundIndex;
        }
      }

      setCurrentIndex(startIndex);
      loadAttachment(startIndex);
      resetImageTransform();
    }
  }, [open, imageAttachments, initialAttachmentId]);

  // Reset image transform when changing images
  const resetImageTransform = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
    setRotation(0);
  };

  const loadAttachment = async (index: number) => {
    if (index < 0 || index >= imageAttachments.length) return;

    setLoading(true);
    setError(null);
    resetImageTransform();

    try {
      // Se já tiver URL, usar
      if (imageAttachments[index].url) {
        setCurrentUrl(imageAttachments[index].url);
      } else {
        const url = await getAttachmentUrl(imageAttachments[index].filePath);
        if (url) {
          setCurrentUrl(url);
        } else {
          setError("Não foi possível carregar a imagem");
        }
      }
    } catch (error) {
      console.error("Erro ao carregar imagem:", error);
      setError("Erro ao carregar a imagem");
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (currentIndex < imageAttachments.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      loadAttachment(nextIndex);
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      loadAttachment(prevIndex);
    }
  };

  // Zoom functions - removido useCallback para evitar re-renders
  const handleZoomIn = () => {
    setScale(prevScale => {
      const newScale = Math.min(prevScale * 1.2, 5);
      return newScale;
    });
  };

  const handleZoomOut = () => {
    setScale(prevScale => {
      const newScale = Math.max(prevScale / 1.2, 0.1);
      if (newScale <= 1) {
        setPosition({ x: 0, y: 0 });
      }
      return newScale;
    });
  };

  const handleResetZoom = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
    setRotation(0);
  };

  const handleRotate = () => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  };

  // Mouse drag functions
  const handleMouseDown = (e: React.MouseEvent) => {
    if (scale > 1) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && scale > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Wheel zoom
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale(prevScale => {
      const newScale = Math.min(Math.max(prevScale * delta, 0.1), 5);
      if (newScale <= 1) {
        setPosition({ x: 0, y: 0 });
      }
      return newScale;
    });
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;
      
      switch (e.key) {
        case 'ArrowLeft':
          handlePrev();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        case 'Escape':
          onClose();
          break;
        case '+':
        case '=':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case '0':
          handleResetZoom();
          break;
        case 'r':
        case 'R':
          handleRotate();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [open, currentIndex, imageAttachments.length]);

  const getCurrentAttachment = () => {
    if (imageAttachments.length === 0 || currentIndex < 0 || currentIndex >= imageAttachments.length) {
      return null;
    }
    return imageAttachments[currentIndex];
  };

  const currentAttachment = getCurrentAttachment();
  
  // Se não há imagens, não renderizar o modal
  if (imageAttachments.length === 0) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[100vw] max-h-[100vh] w-screen h-screen bg-black border-none p-0 overflow-hidden">
        {/* Header com controles */}
        <div className="absolute top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm p-4 flex justify-between items-center">
          <div className="text-white text-sm font-medium truncate max-w-md">
            {currentAttachment?.filename} ({currentIndex + 1} de {imageAttachments.length})
          </div>
          
          <div className="flex items-center gap-2">
            {/* Download */}
            <Button asChild variant="ghost" size="icon" className="text-white hover:bg-white/20">
              <a
                href={currentUrl || "#"}
                target="_blank"
                rel="noopener noreferrer"
                download={currentAttachment?.filename}
                title="Download"
              >
                <Download className="h-5 w-5" />
              </a>
            </Button>
            
            {/* Fechar */}
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={onClose} 
              className="text-white hover:bg-white/20"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Área da imagem */}
        <div 
          className="w-full h-full flex items-center justify-center bg-black overflow-hidden relative"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
          style={{ cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
        >
          {loading ? (
            <div className="flex flex-col items-center text-white">
              <Loader2 className="w-8 h-8 animate-spin mb-2" />
              <span>Carregando imagem...</span>
            </div>
          ) : error ? (
            <div className="text-red-400 text-center">
              <div className="text-lg mb-2">❌ Erro ao carregar</div>
              <div>{error}</div>
            </div>
          ) : currentUrl ? (
            <img
              ref={imageRef}
              src={currentUrl}
              alt={currentAttachment?.filename || "Imagem"}
              className="select-none"
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${scale}) rotate(${rotation}deg)`,
                transformOrigin: 'center center',
                maxHeight: scale <= 1 ? 'calc(100vh - 120px)' : 'none',
                maxWidth: scale <= 1 ? 'calc(100vw - 40px)' : 'none',
                objectFit: 'contain',
                transition: isDragging ? 'none' : 'transform 0.1s ease-out'
              }}
              onMouseDown={handleMouseDown}
              onDoubleClick={() => {
                if (scale > 1) {
                  handleResetZoom();
                } else {
                  setScale(2);
                  setPosition({ x: 0, y: 0 });
                }
              }}
              draggable={false}
              onLoad={() => {
                if (scale === 1) {
                  setPosition({ x: 0, y: 0 });
                }
              }}
            />
          ) : (
            <div className="text-gray-400 text-center">
              <div className="text-lg mb-2">📷</div>
              <div>Nenhuma imagem selecionada</div>
            </div>
          )}
        </div>

        {/* Setas de navegação */}
        {imageAttachments.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                handlePrev();
              }}
              disabled={currentIndex === 0 || loading}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full w-12 h-12 border border-white/20"
              title="Imagem anterior (seta esquerda)"
            >
              <ChevronLeft className="h-8 w-8" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                handleNext();
              }}
              disabled={currentIndex === imageAttachments.length - 1 || loading}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full w-12 h-12 border border-white/20"
              title="Próxima imagem (seta direita)"
            >
              <ChevronRight className="h-8 w-8" />
            </Button>
          </>
        )}

        {/* Instruções de atalhos */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 backdrop-blur-sm px-3 py-1 rounded-lg">
          <div className="text-white/70 text-xs text-center">
            ↑↓ Zoom • ← → Navegar • R Girar • 0 Reset • ESC Fechar • Duplo clique: Zoom
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};