import React from "react";
import { Task, TaskType } from "@/types/index";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Edit, Trash, Copy, CheckSquare, Calendar, Paperclip } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { format, isValid } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface TaskCardProps {
  task: Task;
  onEditClick: (task: Task) => void;
  onDeleteClick: (taskId: string) => void;
  onDuplicateClick: (taskId: string) => void;
}

export const TaskCard: React.FC<TaskCardProps> = ({ task, onEditClick, onDeleteClick, onDuplicateClick }) => {
  const { users } = useAuth();

  const assignee = users?.find((user) => user.id === task.assigneeId);

  // Buscar o ID do criador da tarefa de forma simplificada
  const creatorId = task.createdBy;

  // Buscar o perfil do criador na lista de usuários
  const creator = creatorId ? users?.find((user) => user.id === creatorId) : undefined;

  // Determinar o nome do criador (priorizar createdByName se existir)
  const creatorName = task.createdByName || creator?.name || creator?.email;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "facil":
        return "bg-green-500 hover:bg-green-600";
      case "medio":
        return "bg-amber-500 hover:bg-amber-600";
      case "dificil":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "backlog":
        return "bg-gray-500 hover:bg-gray-600";
      case "aguardando":
        return "bg-amber-500 hover:bg-amber-600";
      case "em_progresso":
        return "bg-blue-500 hover:bg-blue-600";
      case "concluido":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case "alta":
        return "bg-red-500 hover:bg-red-600";
      case "media":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "baixa":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-gray-400 hover:bg-gray-500";
    }
  };

  const formatStatus = (status: string) => {
    switch (status) {
      case "backlog":
        return "Backlog";
      case "aguardando":
        return "Aguardando";
      case "em_progresso":
        return "Em Progresso";
      case "concluido":
        return "Concluído";
      default:
        return status;
    }
  };

  const formatPriority = (priority?: string) => {
    switch (priority) {
      case "alta":
        return "Alta";
      case "media":
        return "Média";
      case "baixa":
        return "Baixa";
      default:
        return "Normal";
    }
  };

  const formatDifficulty = (difficulty: string) => {
    switch (difficulty) {
      case "facil":
        return "Fácil";
      case "medio":
        return "Médio";
      case "dificil":
        return "Difícil";
      default:
        return difficulty;
    }
  };

  const getTaskTypeDisplay = (taskType: TaskType) => {
    switch (taskType) {
      case "bug":
        return { label: "🐞 BUG", color: "bg-red-500 hover:bg-red-600" };
      case "nova_funcionalidade":
        return { label: "✨ NOVO ITEM", color: "bg-purple-500 hover:bg-purple-600" };
      default:
        return null;
    }
  };

  // Formatar a data de forma segura
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return "";

    // Converter para objeto Date se for string
    const dateObj = typeof date === "string" ? new Date(date) : date;

    // Verificar se a data é válida
    if (!isValid(dateObj)) return "";

    return format(dateObj, "dd/MM/yyyy", { locale: ptBR });
  };

  const completedSubtasks = task.subtasks ? task.subtasks.filter((st) => st.completed).length : 0;
  const totalSubtasks = task.subtasks ? task.subtasks.length : 0;

  // Contar anexos
  const totalAttachments = task.attachments ? task.attachments.length : 0;

  // Extrair iniciais para o avatar
  const getInitials = (name?: string) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Manipular clique no card para editar a tarefa
  const handleCardClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Verificar se o clique não foi em um botão ou outro elemento interativo
    const target = e.target as HTMLElement;
    if (!target.closest("button")) {
      onEditClick(task);
    }
  };

  return (
    <Card
      className="mb-4 bg-notion-sidebar border-notion-border shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleCardClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between">
          <CardTitle className="text-notion-text text-base font-medium">{task.title}</CardTitle>
          <div className="flex space-x-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditClick(task);
                    }}
                    className="p-1 text-notion-muted hover:text-notion-text rounded"
                  >
                    <Edit size={14} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Editar tarefa</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDuplicateClick(task.id);
                    }}
                    className="p-1 text-notion-muted hover:text-notion-text rounded"
                  >
                    <Copy size={14} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Duplicar tarefa</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteClick(task.id);
                    }}
                    className="p-1 text-notion-muted hover:text-red-500 rounded"
                  >
                    <Trash size={14} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Excluir tarefa</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="flex flex-col gap-2 mt-2">
          {/* Usuário Atribuído */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-notion-muted font-medium min-w-[60px]">Atribuído:</span>
            {assignee ? (
              <div className="flex items-center gap-1">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={assignee.avatar_url || ""} alt={assignee.name} />
                  <AvatarFallback className="text-[10px] bg-purple-600 text-white">{getInitials(assignee.name)}</AvatarFallback>
                </Avatar>
                <span className="text-xs text-notion-muted">{assignee.name || assignee.email || "Usuário"}</span>
              </div>
            ) : (
              <span className="text-xs text-notion-muted">Não atribuído</span>
            )}
          </div>

          {/* Criador da Tarefa */}
          {(creatorName || creator) && (
            <div className="flex items-center gap-2">
              <span className="text-xs text-notion-muted font-medium min-w-[60px]">Criado por:</span>
              <div className="flex items-center gap-1">
                {creator && (
                  <Avatar className="h-5 w-5">
                    <AvatarImage src={creator.avatar_url || ""} alt={creator.name} />
                    <AvatarFallback className="text-[10px] bg-green-600 text-white">{getInitials(creator.name || creatorName)}</AvatarFallback>
                  </Avatar>
                )}
                <span className="text-xs text-notion-muted">{creatorName || "Usuário"}</span>
              </div>
            </div>
          )}
        </div>

        {/* Tag de Tipo de Tarefa */}
        {task.taskType && (
          <div className="flex items-center gap-2 mt-2">
            <span className="text-xs text-notion-muted font-medium min-w-[60px]">Tipo:</span>
            {(() => {
              const typeDisplay = getTaskTypeDisplay(task.taskType);
              return typeDisplay ? <Badge className={`${typeDisplay.color} text-white text-xs`}>{typeDisplay.label}</Badge> : null;
            })()}
          </div>
        )}
      </CardHeader>
      <CardContent className="pb-2">
        {task.description && <p className="text-notion-muted text-sm line-clamp-2 mb-2">{task.description}</p>}

        {/* Datas */}
        {(task.startDate || task.endDate) && (
          <div className="flex items-center mb-2 text-xs text-notion-muted">
            <Calendar className="h-3 w-3 mr-1" />
            <span>
              {formatDate(task.startDate) || "?"} - {formatDate(task.endDate) || "?"}
            </span>
          </div>
        )}

        <div className="flex items-center gap-3">
          {/* Subtarefas */}
          {totalSubtasks > 0 && (
            <div className="flex items-center text-xs text-notion-muted gap-1">
              <CheckSquare className="h-5 w-5" />
              <span>
                {completedSubtasks}/{totalSubtasks}
                {totalSubtasks > 0 && ` (${Math.round((completedSubtasks / totalSubtasks) * 100)}%)`}
              </span>
            </div>
          )}

          {/* Anexos */}
          {totalAttachments > 0 && (
            <div className="flex items-center text-xs text-notion-muted gap-1">
              <Paperclip className="h-3 w-3" />
              <span>
                {totalAttachments} {totalAttachments === 1 ? "anexo" : "anexos"}
              </span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex flex-col gap-2 pt-0">
        {/* Tags com labels descritivos */}
        <div className="flex flex-col gap-2 w-full">
          {/* Linha 1: Status */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-notion-muted font-medium min-w-[50px]">Status:</span>
            <Badge className={`${getStatusColor(task.status)} text-white text-xs`}>{formatStatus(task.status)}</Badge>
          </div>

          {/* Linha 2: Dificuldade */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-notion-muted font-medium min-w-[50px]">Dificuldade:</span>
            <Badge className={`${getDifficultyColor(task.difficulty)} text-white text-xs`}>{formatDifficulty(task.difficulty)}</Badge>
          </div>

          {/* Linha 3: Prioridade (se existir) */}
          {task.priority && (
            <div className="flex items-center gap-2">
              <span className="text-xs text-notion-muted font-medium min-w-[50px]">Prioridade:</span>
              <Badge className={`${getPriorityColor(task.priority)} text-white text-xs`}>{formatPriority(task.priority)}</Badge>
            </div>
          )}
        </div>

        {/* Tempo estimado */}
        {task.estimatedTime && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-notion-muted font-medium min-w-[50px]">Tempo estimado:</span>
            <span className="text-xs text-notion-muted">{task.estimatedTime}</span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
};
