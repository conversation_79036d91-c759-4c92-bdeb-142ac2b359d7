import React, { useState, useEffect } from "react";
import { TaskAttachment } from "@/types";
import { X, FileIcon, Image, File, FileText, Download, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { TaskAttachmentViewer } from "./TaskAttachmentViewer";
import { toast } from "sonner";
import { getAttachmentUrl } from "@/services/taskAttachmentService";

interface TaskAttachmentsListProps {
  attachments: TaskAttachment[];
  onRemove?: (attachmentId: string) => Promise<boolean>;
  readOnly?: boolean;
}

export const TaskAttachmentsList: React.FC<TaskAttachmentsListProps> = ({ attachments, onRemove, readOnly = false }) => {
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedAttachmentId, setSelectedAttachmentId] = useState<string | undefined>(undefined);
  const [attachmentUrls, setAttachmentUrls] = useState<Record<string, string>>({});
  const [loadingUrls, setLoadingUrls] = useState<Record<string, boolean>>({});

  const handleViewAttachment = (attachmentId: string) => {
    setSelectedAttachmentId(attachmentId);
    setViewerOpen(true);
  };

  const handleRemoveAttachment = async (attachmentId: string, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!onRemove) return;

    if (window.confirm("Tem certeza que deseja remover este anexo?")) {
      try {
        const success = await onRemove(attachmentId);
        if (!success) {
          toast.error("Não foi possível remover o anexo");
        }
      } catch (error) {
        console.error("Erro ao remover anexo:", error);
        toast.error("Erro ao remover anexo");
      }
    }
  };

  const getFileIcon = (mimeType?: string) => {
    if (!mimeType) return <File className="h-6 w-6 text-gray-400" />;

    if (mimeType.startsWith("image/")) {
      return <Image className="h-6 w-6 text-blue-500" />;
    }

    if (mimeType.includes("pdf")) {
      return <FileText className="h-6 w-6 text-red-500" />;
    }

    if (mimeType.includes("word") || mimeType.includes("document")) {
      return <FileText className="h-6 w-6 text-blue-600" />;
    }

    if (mimeType.includes("excel") || mimeType.includes("spreadsheet")) {
      return <FileText className="h-6 w-6 text-green-600" />;
    }

    return <FileIcon className="h-6 w-6 text-gray-500" />;
  };

  // Carregar URL de um anexo específico
  const loadAttachmentUrl = async (attachment: TaskAttachment) => {
    if (attachmentUrls[attachment.id] || loadingUrls[attachment.id]) return;

    setLoadingUrls(prev => ({ ...prev, [attachment.id]: true }));
    
    try {
      let url = attachment.url;
      
      if (!url) {
        url = await getAttachmentUrl(attachment.filePath);
      }
      
      if (url) {
        setAttachmentUrls(prev => ({ ...prev, [attachment.id]: url }));
      }
    } catch (error) {
      console.error('Erro ao carregar URL do anexo:', attachment.filename, error);
    } finally {
      setLoadingUrls(prev => ({ ...prev, [attachment.id]: false }));
    }
  };

  // Carregar URLs de todos os anexos de imagem
  useEffect(() => {
    // Limpar URLs de anexos que não existem mais
    const currentAttachmentIds = new Set(attachments.map(a => a.id));
    setAttachmentUrls(prev => {
      const filtered = Object.fromEntries(
        Object.entries(prev).filter(([id]) => currentAttachmentIds.has(id))
      );
      return filtered;
    });
    
    // Carregar URLs para anexos de imagem
    attachments.forEach(attachment => {
      if (attachment.mimeType?.startsWith('image/')) {
        loadAttachmentUrl(attachment);
      }
    });
  }, [attachments]);

  const getThumbnail = (attachment: TaskAttachment) => {
    const isImage = attachment.mimeType?.startsWith("image/");
    const attachmentUrl = attachmentUrls[attachment.id] || attachment.url;
    const isLoadingUrl = loadingUrls[attachment.id];

    if (isImage) {
      if (isLoadingUrl) {
        return (
          <div className="w-full h-12 flex items-center justify-center bg-gray-100 rounded-md">
            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          </div>
        );
      }
      
      if (attachmentUrl) {
        return (
          <div className="w-full h-12 relative overflow-hidden rounded-md bg-gray-50">
            <img 
              src={attachmentUrl} 
              alt={attachment.filename} 
              className="w-full h-full object-cover" 
              onError={(e) => {
                console.error('Erro ao carregar imagem:', attachment.filename);
                // Remover a URL com erro e tentar recarregar
                setAttachmentUrls(prev => {
                  const { [attachment.id]: removed, ...rest } = prev;
                  return rest;
                });
                // Tentar recarregar depois de um tempo
                setTimeout(() => loadAttachmentUrl(attachment), 1000);
              }}
            />
          </div>
        );
      }
    }

    // Se for imagem mas não conseguiu carregar URL, tentar mais uma vez
    if (isImage && !isLoadingUrl && !attachmentUrl) {
      // Tentar carregar novamente
      setTimeout(() => loadAttachmentUrl(attachment), 100);
    }

    return <div className="w-full h-12 flex items-center justify-center bg-gray-100 rounded-md">{getFileIcon(attachment.mimeType)}</div>;
  };

  // Abreviar nomes de arquivos muito longos
  const formatFileName = (filename: string) => {
    if (filename.length <= 15) return filename;

    const extension = filename.split(".").pop() || "";
    const name = filename.substring(0, filename.length - extension.length - 1);

    if (name.length <= 12) return filename;

    return `${name.substring(0, 9)}...${extension ? `.${extension}` : ""}`;
  };

  
  if (attachments.length === 0) {
    return (
      <div className="text-notion-muted text-sm italic p-4 bg-notion-page rounded-md border border-dashed border-notion-border text-center">
        📎 Nenhum anexo encontrado.
      </div>
    );
  }

  return (
    <div className="mt-4">
      <h3 className="text-sm font-medium text-notion-text mb-2">Arquivos em Anexo ({attachments.length})</h3>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
        {attachments.map((attachment) => (
          <div
            key={attachment.id}
            onClick={() => handleViewAttachment(attachment.id)}
            className="bg-notion-page hover:bg-notion-hover border border-notion-border rounded-md p-2 flex flex-col cursor-pointer transition-colors"
          >
            {getThumbnail(attachment)}

            <div className="mt-1 flex items-center justify-between">
              <span className="text-xs text-notion-text truncate" title={attachment.filename}>
                {formatFileName(attachment.filename)}
              </span>

              {!readOnly && onRemove && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 text-notion-muted hover:text-red-500 p-0"
                  onClick={(e) => handleRemoveAttachment(attachment.id, e)}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {viewerOpen && (
        <TaskAttachmentViewer
          open={viewerOpen}
          onClose={() => setViewerOpen(false)}
          attachments={attachments}
          initialAttachmentId={selectedAttachmentId}
        />
      )}
    </div>
  );
};
