```sql
-- 1. Campo Prioridade nas Tasks (pular se já existir)
ALTER TABLE tasks
ADD COLUMN IF NOT EXISTS priority VARCHAR(10) DEFAULT 'baixa'
CHECK (priority IN ('alta', 'baixa', 'média'));

COMMENT ON COLUMN tasks.priority IS 'Prioridade da tarefa: alta, baixa, média';

UPDATE tasks
SET priority = 'baixa'
WHERE priority IS NULL;

-- 2. Coluna Backlog no Kanban
SELECT DISTINCT status FROM tasks;

ALTER TABLE tasks DROP CONSTRAINT IF EXISTS tasks_status_check;

ALTER TABLE tasks
ALTER COLUMN status TYPE VARCHAR(20);

ALTER TABLE tasks
ADD CONSTRAINT tasks_status_check
CHECK (status IN ('aguardando', 'em_progresso', 'concluido', 'backlog'));

CREATE INDEX IF NOT EXISTS idx_tasks_status_backlog
ON tasks(status)
WHERE status = 'backlog';

-- 3. Nome do <PERSON>riador da Tarefa
ALTER TABLE tasks
ADD COLUMN IF NOT EXISTS created_by_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS created_by_name VARCHAR(255);

COMMENT ON COLUMN tasks.created_by_id IS 'ID do usuário que criou a tarefa';
COMMENT ON COLUMN tasks.created_by_name IS 'Nome do usuário que criou a tarefa (snapshot)';

UPDATE tasks
SET created_by_id = assignee_id,
    created_by_name = (
        SELECT COALESCE(
            au.raw_user_meta_data->>'full_name',
            au.raw_user_meta_data->>'name',
            au.email
        )
        FROM auth.users au
        JOIN profiles p ON p.id = au.id
        WHERE p.id = tasks.assignee_id
    )
WHERE created_by_id IS NULL AND assignee_id IS NOT NULL;

CREATE OR REPLACE FUNCTION get_user_display_name(user_id UUID)
RETURNS VARCHAR(255)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    display_name VARCHAR(255);
BEGIN
    SELECT COALESCE(
        raw_user_meta_data->>'full_name',
        raw_user_meta_data->>'name',
        email
    ) INTO display_name
    FROM auth.users
    WHERE id = user_id;

    RETURN COALESCE(display_name, 'Usuário Desconhecido');
END;
$$;

-- 4. Queries de Verificação
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'tasks'
AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT
    tc.constraint_name,
    tc.constraint_type,
    cc.check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.check_constraints cc
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'tasks'
AND tc.table_schema = 'public';

SELECT
    id,
    title,
    status,
    priority,
    created_by_name,
    created_at
FROM tasks
ORDER BY created_at DESC
LIMIT 5;
```
